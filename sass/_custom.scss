/* ============ ADD HERE YOUR CUSTOM SCSS or  plain CSS CODE. ============ 

HOW DOES IT WORK?

    Edit this file, save, 
    view page as admin:
    The style will be AUTOMATICALLY recompiled and 
    your new CSS bundle will be reloaded via ajax to show 
    the new styling edits.

NOTICE for beginners: 

    CODE HERE YOUR MASTERPIECE, but before that, if you haven't yet,
    please take your time to understand and study how <PERSON><PERSON><PERSON> works.
    It can save you a lot of time,
    eg. to standardize project colors, or fully customize typography.
    First of all, have a ride in the WordPress Customizer.
    Then learn to use Bootstrap 5 Classes and utilities.
    If you come from the CSS world, take some time to study the basic of SASS. It's very powerful.
    With great powers...you are warned 

************************************************		*/
//a demo statement - uncomment to see page turn red and blush
//body { background: red; opacity: 0.4;border:5px solid yellow}  

//open menus on hover
//.dropdown:hover>.dropdown-menu { display: block;}
  

//PAGE HAS SCROLLED: From the Customizer, enable the "Scroll Detection" feature to be able to use the classes:

//body.scroll-position-at-top  {background:lime}
//body.scroll-position-not-at-top {background:red}

 

