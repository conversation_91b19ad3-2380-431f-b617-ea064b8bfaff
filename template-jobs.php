<?php
/*
Template Name: Jobs List
*/

// Exit if accessed directly.
defined( 'ABSPATH' ) || exit;

get_header();

?>
    <!-- ========== banner-section start============= -->

    <div class="banner-section d-flex flex-column align-items-staer justify-content-center position-relative"
        style="background-image: url('<?php echo get_the_post_thumbnail_url(get_the_ID(), 'full'); ?>')">
        <div class="inner-overlay"></div>
        <!-- style="background-image:linear-gradient(to bottom, rgba(255,255,255,0.05) 0%, rgba(0,0,0,0.15) 50%), radial-gradient(at top center, rgba(255,255,255,0.20) 50%, rgba(0,0,0,0.40) 190%), url('<?php echo get_the_post_thumbnail_url(get_the_ID(), 'full'); ?>')"> -->
        <!-- style="background-image:linear-gradient(to bottom, rgba(255,255,255,0.05) 0%, rgba(0,0,0,0.15) 100%), radial-gradient(at top center, rgba(255,255,255,0.40) 0%, rgba(0,0,0,0.40) 120%), url('<?php echo get_the_post_thumbnail_url(get_the_ID(), 'full'); ?>')"> -->
        <div class="container-one position-relative">
           
        </div>
    </div>
    <div class="banner-content banner-single-post-title title-bg-red">
        <div class="container-one">
            <h1 class="title-position-design text-white" ><?php echo get_field('banner_title') ? get_field('banner_title') : get_the_title(); ?><span></span></h1>
        </div>
        
    </div>
    <!-- ========== banner-section end============= -->

    <!-- ========== people details section start ============= -->

    <div class="people-section pt-60 pb-60 d-none">
        <div class="container-one">
            <div class="row">
                <div class="col-lg-6">
                    <div class="section-title-one style-yellow">
                        <h2 ><?php echo get_field('people_title'); ?></h2>
                        <div >
                            <p><?php echo get_field('people_description'); ?></p>
                        </div>
                    </div>
                </div>
                <div class="col-lg-6">
                    <div class="right-box">
                    </div>
                </div>
            </div>
        </div>
    </div>

    
    <!-- <div class="team-section pt-n-100 mt-100 mb-20"> -->
    <div class="team-section pt-100">
        <div class="container-one">
            <div class="mb-20 mt-100">
                <?php the_content(); ?>
            </div>
        </div>
    </div>

    

    

    <!-- <div class="footer-top-design-one">
        <div class="box"></div>
    </div> -->



<?php 

get_footer();

