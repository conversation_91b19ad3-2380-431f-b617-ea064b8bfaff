<?php
/*
    Template Name: Boult Renew
*/

// Exit if accessed directly.
defined('ABSPATH') || exit;

get_header();

?>

<div class="d-flex container-fluid current-vacancies-banner" style="background:url(<?php echo get_the_post_thumbnail_url(); ?>);"></div>


<!-- <div class="container p-5 bg-light" style="margin-top:-100px"> -->
<div class="container-one" style="margin-top:-100px">
    <h1 class="display-4"><?php the_title(); ?></h1>
</div>

<?php
$footer_menu_content = get_field('footer_menu_content');
?>
<!-- ========== about-section start============= -->
<div class="vacency-list-section ">
    <div class="container-one">
        <div class="row justify-content-end g-4">
            <div class="col-lg-7">
                <div class="recent-highlight-quotes">
                    <div class="box-inner-content">
                        <?php echo $footer_menu_content['quotes']; ?>

                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<div class="about-section pt-40">
    <div class="container-one">
        <div class="row justify-content-start">
            <div class="col-lg-12">
                <?php

                the_content();

                ?>
            </div>

        </div>
    </div>
</div>


<div class="container-one">

    <div class="row">
        <!-- <div class="col-md-8 offset-md-2"> -->
        <div class="col-md-12">
            <?php

            if (get_theme_mod("enable_sharing_buttons")) picostrap5 - child - base_the_sharing_buttons();

            edit_post_link(__('Edit this post', 'picostrap5-child-base'), '<p class="text-end">', '</p>');

            // If comments are open or we have at least one comment, load up the comment template.
            if (!get_theme_mod("singlepost_disable_comments")) if (comments_open() || get_comments_number()) {
                comments_template();
            }

            ?>

        </div><!-- /col -->
    </div>
</div>


<?php get_footer();
