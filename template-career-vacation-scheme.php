<?php 
/*
    Template Name: Career Vacation Scheme Page
*/

// Exit if accessed directly.
defined( 'ABSPATH' ) || exit;
$pll = pll_current_language();
get_header();

?>

<!-- <div class="logo-section">
    <div class="container-one">
        <div class="row justify-content-start">
            <div class="col-lg-6">
                <div class="logo-area">
                    <img src="<?php //echo get_stylesheet_directory_uri();?>/assets/images/logo/header-logo.svg" alt="image">
                </div>
            </div>
        </div>
    </div>
</div> -->

<!-- ========== header end============= -->
<h1 style="opacity:0; display: none;">hidden</h1>
<!-- ========== software-section start============= -->
<?php 
    $page_title = get_field('page_title');
    $vacation_content = get_field('vacation_content');

?>
<div class="content-section pt-240">
    <div class="container-one">
        <div class="row">
            <div class="col-lg-6">
                <?php if(!empty($page_title)): ?>
                <div class="section-title-one style-yellow title-pb-150">
                    <h2 ><?php echo $page_title; ?></h2>
                </div>
                <?php endif; ?>
            </div>
            <div class="col-lg-6">
                <div class="content">
                    <h6 ><?php echo $vacation_content['title']; ?></h6>
                    <div >
                        
                    <?php echo $vacation_content['content']; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<?php 
    $eligibility_card = get_field('eligibility_card');
?>
<div class="case-study-section pt-100">
    <div class="container-fluid">
        <div class="row">
            <div class="col-lg-5">
                <div class="eligibility-card">
                    <div class="title">
                        <h6 ><?php echo $eligibility_card['title']; ?></h6>
                    </div>
                    <div >
                        
                    <?php echo $eligibility_card['content']; ?>
                    </div>
                </div>
                <?php 
                    $select_people = $eligibility_card['select_people'];
                    if(!empty($select_people)):
                        $roles = get_the_terms( $select_people->ID, 'people_role' );
                        $roles = join(', ', wp_list_pluck($roles, 'name'));

                        $locations = get_the_terms( $select_people->ID, 'location' );
                        $locations = join(', ', wp_list_pluck($locations, 'name'));
                ?>

                <div class="case-author-single d-none">
                    <div class="image-wrap" >
                        <img src="<?php echo get_field('person_photo_1', $select_people->ID) ? get_field('person_photo_1', $select_people->ID)['sizes']['medium_large'] : get_stylesheet_directory_uri() . '/assets/images/team/team.jpg'; ?>" alt="<?php echo get_the_title($select_people->ID); ?>">
                    </div>
                    <div class="content" >
                        <h6><?php echo get_the_title($select_people->ID); ?></h6>
                        <span><?php echo $roles;?></span>
                        <span><?php echo $locations;?></span>
                    </div>
                </div>
                <?php endif; ?>
            </div>
            <?php 
                $big_card_content = get_field('big_card_content');
            ?>
            <div class="col-lg-1"></div>
            <div class="col-lg-6">
                <div class="big-card case-study-single style-red mb-90 d-none">
                    <div class="content">
                        <div class="title">
                            <h4 ><?php // echo get_field('big_card_content_title'); // $big_card_content['title'];?></h4>
                        </div>
                        <div class="body" >
                            <?php 
                                // echo get_field('big_card_content_content');  // $big_card_content['content']; 
                            ?>
                        </div>
                    </div>
                </div>

                <div class="recent-highlight-quotes zn-1">
                    <div class="box-with-border-inner"></div>
                    <div class="box-inner-content bg-primary-green-dark-light">
                        <h4 class="text-whitee" ><?php echo get_field('big_card_content_title'); // $big_card_content['title'];?></h4>
                        <?php 
                            echo get_field('big_card_content_content');  // $big_card_content['content']; 
                        ?>
                        <?php 
                            $select_people = $eligibility_card['select_people'];
                            if(!empty($select_people)):
                                $roles = get_the_terms( $select_people->ID, 'people_role' );
                                $roles = join(', ', wp_list_pluck($roles, 'name'));

                                $locations = get_the_terms( $select_people->ID, 'location' );
                                $locations = join(', ', wp_list_pluck($locations, 'name'));

                                $person_image = get_field('person_photo_1', $select_people->ID) ? get_field('person_photo_1', $select_people->ID)['sizes']['medium_large'] : get_stylesheet_directory_uri() . '/assets/images/team/team.jpg';
                        ?>
                            <div class="team-content quote-inner">
                                <h6><a hreflang="<?php echo esc_attr($pll); ?>" href="<?php echo get_the_permalink($select_people); ?>"><?php echo get_the_title($select_people); ?></a></h6>
                                <span><?php echo strtoupper($roles); ?></span>
                            </div>
                        <?php endif; ?>

                    </div>
                    <div class="box-inner-image">
                        <img src="<?php echo $person_image; ?>" alt="image">
                    </div>
                </div>

                <div class="recent-highlight-quotes zn-1 <?php echo get_field('quotes') ? '' : 'd-none'; ?>">
                    <div class="box-with-border-inner"></div>
                    <div class="box-inner-content">
                        <?php echo get_field('quotes'); ?>
                        <?php 
                            $quote_person_image = get_field('quote_person_image');
                            $person_image = get_stylesheet_directory_uri() . '/assets/images/team/team.jpg';
                            if($quote_person_image){
                                $roles = get_the_terms( $quote_person_image->ID, 'people_role' );
                                $roles = join(', ', wp_list_pluck($roles, 'name'));

                                $person_image = get_field('person_photo_1', $quote_person_image->ID) ? get_field('person_photo_1', $quote_person_image->ID)['sizes']['medium_large'] : get_stylesheet_directory_uri() . '/assets/images/team/team.jpg';
                                ?>
                                    <div class="team-content quote-inner">
                                        <h6><a hreflang="<?php echo esc_attr($pll); ?>" href="<?php echo get_the_permalink($quote_person_image); ?>"><?php echo get_the_title($quote_person_image); ?></a></h6>
                                        <span><?php echo strtoupper($roles); ?></span>
                                    </div>
                                <?php 
                            }
                        ?>
                    </div>
                    <div class="box-inner-image">
                        <img src="<?php echo $person_image; ?>" alt="image">
                    </div>
                </div>

            </div>
        </div>
    </div>
</div>
<?php 
$last_content_group = get_field('last_content_group');
?>
<div class="apply-section pr-container">
    <div class="container-fluid">
        <div class="row">
            <div class="col-xl-8 col-lg-7 order-lg-1 order-2">
                <div class="apply-image" >
                    <img src="<?php echo $last_content_group['image']; ?>" alt="image">
                </div>
                <div class="apply-card" >
                    <h5><?php echo $last_content_group['apply_now_title']; ?></h5>
                    <div class="h-line"></div>
                    <a hreflang="<?php echo esc_attr($pll); ?>" href="<?php echo $last_content_group['apply_now_button']['url']; ?>" class="eg-btn btn--primary-black btn--lg"><?php echo $last_content_group['apply_now_button']['title']; ?></a>
                </div>
            </div>
            <div class="col-xl-4 col-lg-5 order-lg-2 order-1">
                <?php 
                    if(!empty($last_content_group['content_list'])){
                        foreach($last_content_group['content_list'] as $value){
                            ?>
                            <div class="section-title-one style-green" >
                                <h2><?php echo $value['title']; ?></h2>  
                            </div>
                            <?php echo $value['content']; ?>
                            <?php if (!empty($value['link'])) : ?>
                            <div class="mb-70" >
                                <a hreflang="<?php echo esc_attr($pll); ?>" href="<?php echo $value['link']['url']; ?>" class="eg-btn btn--primary-green btn--lg2"><?php echo $value['link']['title']; ?> <i class="bi bi-arrow-right"></i></a> 
                            </div>
                            <?php endif; ?>
                            <?php 
                        }
                    }

                ?>

                
            </div>
        </div>
    </div>
</div>

    <!-- ========== software-section end============= -->

<div class="footer-top-design-ten d-none">
    <div class="box"></div>
</div>
<?php get_footer();