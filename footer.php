</main>

	<!-- ========== footer_section start======== -->
    <?php 
        if(is_front_page()){
            ?>
                <div class="footer-top-design" >
                    <div class="container-one">
                        <div class="footer-box" ></div>
                    </div>
                </div>
            <?php 
        }
        else{
            ?>
                <div class="footer-top-design-two">
                    <div class="container-one">
                        <div class="footer-box"></div>
                    </div>
                </div>
            <?php 
        }
    ?>


    <footer>
        <div class="container">
            <div class="footer-col-wrap">
                <div class="grid-col">
                    <ul class="footer-list footer-links-col1">
                        <?php 
                            $column_1 = get_field('column_1', 'option');

                            $column_1_links = $column_1['column_1_menu'];
                            if(!empty($column_1_links)){
                                foreach ($column_1_links as $value) {
                                    ?>
                                        <li><a href="<?php echo $value['menu_link']['url']; ?>"><?php echo $value['menu_link']['title']; ?></a></li>
                                    <?php 
                                }
                            }
                        ?>
                        <li class="footer-social"><a target="_BLANK" href="<?php echo $column_1['linkedin_link']; ?>"><i class='bx bxl-linkedin'></i></a></li>
                    </ul>
                </div>
                <div class="grid-col">
                    <ul class="footer-list footer-links-col2">
                        <?php 
                            $column_2 = get_field('column_2', 'option');

                            $column_2_links = $column_2['column_2_menu'];
                            if(!empty($column_2_links)){
                                foreach ($column_2_links as $value) {
                                    ?>
                                        <li><a target="<?php echo $value['menu_link']['target']; ?>" href="<?php echo $value['menu_link']['url']; ?>"><?php echo $value['menu_link']['title']; ?></a></li>
                                    <?php 
                                }
                            }
                        ?>
                    </ul>
                </div>
                <div class="grid-col">
                    <div class="footer-address">
                        <?php 
                            $column_3 = get_field('column_3', 'option');
                            $column_location_1 = $column_3['column_location_1'];
                        ?>
                        <h6><?php echo implode(', ', wp_get_post_terms($column_location_1->ID, 'location_city', ['fields' => 'names'])); ?> </h6>
                        <!-- <p><?php echo $column_location_1->post_title; ?> </p> -->
                        <p>
                            <?php 
                                echo nl2br(get_field('address', $column_location_1->ID));
                            ?>
                        </p>
                        <ul class="contact-list">
                            <li><a href="tel:<?php echo nl2br(get_field('telephone', $column_location_1->ID)); ?>"><?php // echo __('T:', 'picostrap5-child-base'); ?> <?php echo nl2br(get_field('telephone', $column_location_1->ID)); ?></a></li>
                            <li><a href="mailto:<?php echo nl2br(get_field('email', $column_location_1->ID)); ?>"><?php // echo __('E:', 'picostrap5-child-base'); ?> <?php echo nl2br(get_field('email', $column_location_1->ID)); ?></a></li>
                        </ul>
                    </div>
                </div>
                <div class="grid-col">
                    <div class="footer-address">
                        <?php 
                            $column_4 = get_field('column_4', 'option');
                            $column_location_2 = $column_4['column_location_2'];
                        ?>
                        <h6><?php echo implode(', ', wp_get_post_terms($column_location_2->ID, 'location_city', ['fields' => 'names'])); ?> </h6>
                        <!-- <p><?php echo $column_location_2->post_title; ?> </p> -->
                        <p>
                            <?php 
                                echo nl2br(get_field('address', $column_location_2->ID));
                            ?>
                        </p>
                        <ul class="contact-list">
                            <li><a href="tel:<?php echo nl2br(get_field('telephone', $column_location_2->ID)); ?>"><?php // echo __('T:', 'picostrap5-child-base'); ?> <?php echo nl2br(get_field('telephone', $column_location_2->ID)); ?></a></li>
                            <li><a href="mailto:<?php echo nl2br(get_field('email', $column_location_2->ID)); ?>"><?php // echo __('E:', 'picostrap5-child-base'); ?> <?php echo nl2br(get_field('email', $column_location_2->ID)); ?></a></li>
                        </ul>
                    </div>
                </div>
                <div class="grid-col">
                    <div class="footer-address">
                        <?php 
                            $column_5 = get_field('column_5', 'option');
                            $column_location_3 = $column_5['column_location_3'];
                        ?>
                        <h6><?php echo implode(', ', wp_get_post_terms($column_location_3->ID, 'location_city', ['fields' => 'names'])); ?> </h6>
                        <!-- <p><?php echo $column_location_3->post_title; ?> </p> -->
                        <p>
                            <?php 
                                echo nl2br(get_field('address', $column_location_3->ID));
                            ?>
                        </p>
                        <ul class="contact-list">
                            <li><a href="tel:<?php echo nl2br(get_field('telephone', $column_location_3->ID)); ?>"><?php // echo __('T:', 'picostrap5-child-base'); ?> <?php echo nl2br(get_field('telephone', $column_location_3->ID)); ?></a></li>
                            <li><a href="mailto:<?php echo nl2br(get_field('email', $column_location_3->ID)); ?>"><?php // echo __('E:', 'picostrap5-child-base'); ?> <?php echo nl2br(get_field('email', $column_location_3->ID)); ?></a></li>
                        </ul>
                    </div>
                </div>
                <div class="grid-col">
                    <div class="footer-address">
                        <?php 
                            $column_6 = get_field('column_6', 'option');
                            $column_location_4 = $column_6['column_location_4'];
                        ?>
                        <h6><?php echo implode(', ', wp_get_post_terms($column_location_4->ID, 'location_city', ['fields' => 'names'])); ?> </h6>
                        <!-- <p><?php echo $column_location_4->post_title; ?> </p> -->
                        <p>
                            <?php 
                                echo nl2br(get_field('address', $column_location_4->ID));
                            ?>
                        </p>
                        <ul class="contact-list">
                            <li><a href="tel:<?php echo nl2br(get_field('telephone', $column_location_4->ID)); ?>"><?php // echo __('T:', 'picostrap5-child-base'); ?> <?php echo nl2br(get_field('telephone', $column_location_4->ID)); ?></a></li>
                            <li><a href="mailto:<?php echo nl2br(get_field('email', $column_location_4->ID)); ?>"><?php // echo __('E:', 'picostrap5-child-base'); ?> <?php echo nl2br(get_field('email', $column_location_4->ID)); ?></a></li>
                        </ul>
                    </div>
                </div>
                <div class="grid-col">
                    <div class="footer-address">
                        <?php 
                            $column_7 = get_field('column_7', 'option');
                            $column_location_5 = $column_7['column_location_5'];
                        ?>
                        <h6><?php echo implode(', ', wp_get_post_terms($column_location_5->ID, 'location_city', ['fields' => 'names'])); ?> </h6>
                        <!-- <p><?php echo $column_location_5->post_title; ?> </p> -->
                        <p>
                            <?php 
                                echo nl2br(get_field('address', $column_location_5->ID));
                            ?>
                        </p>
                        <ul class="contact-list">
                            <li><a href="tel:<?php echo nl2br(get_field('telephone', $column_location_5->ID)); ?>"><?php // echo __('T:', 'picostrap5-child-base'); ?> <?php echo nl2br(get_field('telephone', $column_location_5->ID)); ?></a></li>
                            <li><a href="mailto:<?php echo nl2br(get_field('email', $column_location_5->ID)); ?>"><?php // echo __('E:', 'picostrap5-child-base'); ?> <?php echo nl2br(get_field('email', $column_location_5->ID)); ?></a></li>
                        </ul>
                    </div>
                </div>
                <div class="grid-col">
                    <div class="footer-address">
                        <?php 
                            $column_8 = get_field('column_8', 'option');
                            $column_location_6 = $column_8['column_location_6'];
                        ?>
                        <h6><?php echo implode(', ', wp_get_post_terms($column_location_6->ID, 'location_city', ['fields' => 'names'])); ?> </h6>
                        <!-- <p><?php echo $column_location_6->post_title; ?> </p> -->
                        <p>
                            <?php 
                                echo nl2br(get_field('address', $column_location_6->ID));
                            ?>
                        </p>
                        <ul class="contact-list">
                            <li><a href="tel:<?php echo nl2br(get_field('telephone', $column_location_6->ID)); ?>"><?php // echo __('T:', 'picostrap5-child-base'); ?> <?php echo nl2br(get_field('telephone', $column_location_6->ID)); ?></a></li>
                            <li><a href="mailto:<?php echo nl2br(get_field('email', $column_location_6->ID)); ?>"><?php // echo __('E:', 'picostrap5-child-base'); ?> <?php echo nl2br(get_field('email', $column_location_6->ID)); ?></a></li>
                        </ul>
                    </div>
                </div>
            </div>
            <div class="footer-bottom">
                <p><?php 
                    $footer_content = get_field('footer_content', 'option');
                    echo nl2br($footer_content);
                ?></p>
            </div>
        </div>
    </footer>

    <!-- ========== footer_section end======== -->

	<?php /*if (function_exists("lc_custom_footer")) lc_custom_footer(); else {
		?>
		<?php if (is_active_sidebar( 'footerfull' )): ?>
		<div class="wrapper bg-light mt-5 py-5" id="wrapper-footer-widgets">
			
			<div class="container mb-5">
				
				<div class="row">
					<?php dynamic_sidebar( 'footerfull' ); ?>
				</div>

			</div>
		</div>
		<?php endif ?>
		
		
		<div class="wrapper py-3" id="wrapper-footer-colophon">
			<div class="container-fluid">
		
				<div class="row">
		
					<div class="col text-center">
		
						<footer class="site-footer" id="colophon">
		
							<div class="site-info">
		
								<?php picostrap_site_info(); ?>
		
							</div><!-- .site-info -->
		
						</footer><!-- #colophon -->
		
					</div><!--col end -->
		
				</div><!-- row end -->
		
			</div><!-- container end -->
		
		</div><!-- wrapper end -->
		
	<?php 
	} //END ELSE CASE*/ ?>


	<?php wp_footer(); ?>

	</body>
</html>

