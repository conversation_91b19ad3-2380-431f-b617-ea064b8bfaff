<?php 
/*
    Template Name: Home Page Japanese
*/

// Exit if accessed directly.
defined( 'ABSPATH' ) || exit;

get_header();

?>
<style>
    .bh-slider-cursor {
        display: inline-block;
        width: 10px;
        height: 10px;
        margin-left: -50px;
        margin-top: -105px;
        border-radius: 50%;
        background-color: red;
        left: 0;
        top: 0;
        opacity: 0;
        transition: opacity 0.5s ease-out, width 0.5s ease-out, height 0.5s ease-out;
        text-align: center;
        font-size: 6px;
        
    }
    .bh-slider-block-wrap{
        width: 100%;
        position: relative;
        overflow: hidden;
    }
    .bh-slider-block-wrap:hover .bh-slider-cursor{
        width: 100px;
        height: 100px;
        line-height: 100px;
        font-size: 30px;
        opacity: 1;
        z-index: 22222;
    }
    .bh-cursor {
        position: fixed;
    }

    .box-color-yellow{
        background-color: var(--primary-yellow) !important;
    }
    .box-color-red{
        background-color: var(--primary-red) !important;
    }
    .box-color-green{
        background-color: var(--primary-green) !important;
    }
    .box-color-blue{
        background-color: var(--primary-blue) !important;
    }

</style>

<!-- ========== banner-section start============= -->
<?php
    $banner_section = get_field('banner_section');
?>
<div class="home-banner-section position-relative">
    <div class="container-fluid px-0">
        <div class="swiper home-banner-slider">
            <div class="swiper-wrapper">
                <?php 
                    if(!empty($banner_section['banner_image'])){
                        foreach($banner_section['banner_image'] as $value){
                            ?>
                            <div class="swiper-slide" style="background-image: url('<?php echo $value['image']; ?>');background-size: cover;">
                                <div class="banner-slider-content style-dark">
                                    <h1 ><?php echo $value['title']; ?></h1>
                                </div>
                                <div class="banner-pagination d-flex justify-content-end align-items-start box-color-<?php echo isset($value['box_color']) ? $value['box_color'] : 'yellow'; ?>"></div>
                            </div>
                            <?php 
                        }
                    }
                ?>
            </div>
        </div>
    </div>
    <!-- <div class="banner-pagination d-flex justify-content-end align-items-start"></div> -->
</div>

<!-- ========== banner-section end============= -->

<div class="search-area d-none" >
    <!-- <div class="mobile-box"></div> -->
    <form method="get" action="" id="home-page-banner-search-form">
        <div class="search-block">
            <button type="search"><i class="bi bi-search"></i></button>
            <input type="text" placeholder="<?php echo __('SEARCH OUR TEAM', 'picostrap'); ?>" name="keyword">
        </div>
        <div class="search-block bg-black p-0">
            <div class="home-page-banner-search-list" id="home-page-banner-search-list"></div>
        </div>
    </form>
</div>

<!-- ========== page-content-section start============= -->
<?php 
$expertise_section = get_field('expertise_section');
/*
    
?>
<div class="content-section pl-container pt-100">
    <div class="container-fluid">
        <div class="row justify-content-start">
            <div class="col-lg-5">
                <div class="section-title-one style-yellow">
                    <h2 ><?php echo $expertise_section['title']; ?></h2>
                </div>
                <div >
                    <?php echo $expertise_section['content']; ?>
                    <a href="<?php echo isset($expertise_section['link']) && isset($expertise_section['link']['url']) ? $expertise_section['link']['url'] : ''; ?>" class="eg-btn btn--primary-yellow btn--lg2 mt-3"><?php echo isset($expertise_section['link']) && isset($expertise_section['link']['title']) ? $expertise_section['link']['title'] : ''; ?> <i
                            class="bi bi-arrow-right"></i></a>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- ========== expertise-section start============= -->

<div class="expertise-section pt-100">
    <div class="container-fluid">
        <div class="row justify-content-lg-end justify-content-sm-center justify-content-start">
            <div class="col-xxl-7 col-xl-7 col-lg-7 col-10">
                <div class="expertise-wrapper">
                    <div class="expertise-image" >
                        <img src="<?php echo $expertise_section['quote_image']; ?>" alt="image">
                    </div>
                    <div class="quote-box2" >
                        <!-- <span><?php echo $expertise_section['quote_title']; ?></span> -->
                        <h3 class="text-white"><?php echo $expertise_section['quote_title']; ?></h3>
                        <p><?php echo $expertise_section['quote_content']; ?></p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<?php */ ?>
<?php
    $careers_section = get_field('careers_section');
?>
<div class="content-section pl-container pt-100">
    <div class="container-fluid">
        <div class="row justify-content-start g-4">
            <div class="col-sm-7 col-md-7 col-lg-5">
                <div class="section-title-one style-blue">
                    <h2 ><?php echo $careers_section['title']; ?></h2>
                </div>
                <div >
                    <?php echo $careers_section['content']; ?>
                    <a href="<?php echo isset($careers_section['link']) && isset($careers_section['link']['url']) ? $careers_section['link']['url'] : ''; ?>" class="eg-btn btn--primary-blue btn--lg2 mt-3"><?php echo isset($careers_section['link']) && isset($careers_section['link']['title']) ? $careers_section['link']['title'] : ''; ?><i class="bi bi-arrow-right"></i></a>
                </div>
            </div>
	    <div class="col-sm-5 col-md-5 col-lg-5">
<img src="<?php echo $expertise_section['quote_image']; ?>" alt="image">
            </div>
        </div>
    </div>
</div>
<div class="box-sectoin mt-70 d-none">
    <div class="container-one">
        <div class="row">
            <div class="col-lg-5">

            </div>
            <div class="col-lg-7">
                <div class="career-green-box" >
                    <div class="border-box"></div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- ========== expertise-section start============= -->

<!-- ========== insights section start ============= -->
<?php 
    
?>
<div class="insights-section position-relative bh-slider-block-wrap d-none">
    <span class="bh-cursor bh-slider-cursor arrow-left"><i class="bi bi-arrow-left"></i></span>
    <div class="container-one">
        <div class="section-title-one style-yellow">
            <h2 ><?php echo __('Insights', 'picostrap'); ?></h2>
        </div>
        <div class="row justify-content-center mb-40 g-lg-4 g-3">
            <?php 
                    $args = array(
                        'post_type' => array('bulletin', 'news', 'newsletter', 'tribe_events', 'toolkit', 'upc_news'),
                        'posts_per_page' => 4,
                        'orderby' => 'date',
                        'order' => 'DESC',
                        'post_status' => 'publish',
                    );
                    
                    $query = new WP_Query( $args );
                    
                    if ( $query->have_posts() ) {
                        while ( $query->have_posts() ) {
                            $query->the_post();
                            ?>
                            <!-- <div class="col-lg-3 col-md-6 col-6" >
                                <div class="insight-item style-red">
                                    <?php if(!empty(get_the_post_thumbnail_url())) :?>
                                    <div class="image">
                                       <a href="<?php the_permalink(); ?>"><img src="<?php echo get_the_post_thumbnail_url(); ?>" alt="image"></a> 
                                    </div>
                                    <?php endif; ?>
                                    <div class="content">
                                        <h6><a href="<?php the_permalink(); ?>"><?php echo __('Bulletin', 'picostrap'); ?></a></h6>
                                        <p><?php the_title(); ?></p>
                                        <a href="<?php the_permalink(); ?>"><?php echo __('MORE', 'picostrap'); ?></a>
                                    </div>
                                </div>
                            </div> -->
                            <?php 
                        }
                        wp_reset_postdata();
                    }
            ?>
            
        </div>
        <!-- <a  href="<?php echo home_url('/insights/'); ?>" class="eg-btn btn--primary-yellow btn--lg2"><?php echo __('INSIGHTS', 'picostrap'); ?><i
                class="bi bi-arrow-right"></i></a> -->
    </div>
</div>

<?php $trademark_section = get_field('trademark_section'); ?>
<div class="trademark-section mt-70">
    <div class="container-one">
        <div class="row g-4">
            <div class="col-sm-5 col-md-5 col-lg-5">
                <div class="trademark-iage-green-box">
                    <div class="border-box"></div>
                    <div class="image-box">
                        <img src="<?php echo $trademark_section['image']['url']; ?>" alt="<?php echo $trademark_section['title']; ?>">
                    </div>
                </div>
            </div>
            <div class="col-sm-7 col-md-7 col-lg-7">
                <div class="section-title-one style-yellow">
                    <h2 ><?php echo $trademark_section['title']; ?></h2>
                    <div class="">
                        <?php 
                            echo $trademark_section['content']; 

                            if(isset($trademark_section['link']) && isset($trademark_section['link']['title']) && isset($trademark_section['link']['url'])){
                                ?>
                                    <a href="<?php echo $trademark_section['link']['url']; ?>" class="eg-btn btn--primary-green btn--lg2 mt-3"> <?php echo $trademark_section['link']['title']; ?><i class="bi bi-arrow-right"></i></a>
                                <?php 
                            }
                        ?>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?php $design_team_section = get_field('design_team_section'); ?>
<div class="design-team-section mt-70">
    <div class="container-one">
        <div class="row g-4">
            <div class="col-sm-7 col-md-7 col-lg-7">
                <div class="section-title-one style-yellow">
                    <h2 ><?php echo $design_team_section['title']; ?></h2>
                </div>
                <div >
                    <?php echo $design_team_section['content']; ?>
                    <a href="<?php echo isset($design_team_section['link']) && isset($design_team_section['link']['url']) ? $design_team_section['link']['url'] : ''; ?>" class="eg-btn btn--primary-yellow btn--lg2 mt-3"><?php echo isset($design_team_section['link']) && isset($design_team_section['link']['title']) ? $design_team_section['link']['title'] : ''; ?> <i
                            class="bi bi-arrow-right"></i></a>
                </div>
            </div>
            <div class="col-sm-5 col-md-5 col-lg-5">
                <div class="design-team-image-box">
                    <img src="<?php echo $design_team_section['image']['url']; ?>" alt="<?php echo $design_team_section['title']; ?>">
                </div>
            </div>
        </div>
    </div>
</div>


<?php $plant_rights_section = get_field('plant_rights_section'); ?>
<div class="plant-rights-section mt-70">
    <div class="container-one">
        <div class="row g-4">
            <div class="col-sm-5 col-md-5 col-lg-5">
                <div class="plant-rights-border-box">
                    <div class="border-box"></div>
                </div>
            </div>
            <div class="col-sm-7 col-md-7 col-lg-7">
                <div class="section-title-one style-blue">
                    <h2 ><?php echo $plant_rights_section['title']; ?></h2>
                </div>
                <div >
                    <?php echo $plant_rights_section['content']; ?>
                    <a href="<?php echo isset($plant_rights_section['link']) && isset($plant_rights_section['link']['url']) ? $plant_rights_section['link']['url'] : ''; ?>" class="eg-btn btn--primary-blue btn--lg2 mt-3"><?php echo isset($plant_rights_section['link']) && isset($plant_rights_section['link']['title']) ? $plant_rights_section['link']['title'] : ''; ?><i class="bi bi-arrow-right"></i></a>
                </div>
            </div>
        </div>
    </div>
</div>

<?php $patent_section = get_field('patent_section'); ?>
<div class="patent-section mt-70">
    <div class="container-one">
        <div class="row g-4">
            <div class="col-sm-7 col-md-7 col-lg-7">
                <div class="section-title-one style-yellow">
                    <h2 ><?php echo $patent_section['title']; ?></h2>
                </div>
                <div >
                    <?php echo $patent_section['content']; ?>
                    <a href="<?php echo isset($patent_section['link']) && isset($patent_section['link']['url']) ? $patent_section['link']['url'] : ''; ?>" class="eg-btn btn--primary-yellow btn--lg2 mt-3"><?php echo isset($patent_section['link']) && isset($patent_section['link']['title']) ? $patent_section['link']['title'] : ''; ?> <i
                            class="bi bi-arrow-right"></i></a>
                </div>
            </div>
            <div class="col-sm-5 col-md-5 col-lg-5">
                <div class="design-team-image-box">
                    <img src="<?php echo $patent_section['image']['url']; ?>" alt="<?php echo $patent_section['title']; ?>">
                </div>
            </div>
        </div>
    </div>
</div>

<div class="sector-section mt-70">
    <div class="container-one">
        <div class="section-title-one style-green">
            <h2 ><?php echo get_field('sectors_title') ? get_field('sectors_title') : __('Sectors', 'picostrap'); ?></h2>
        </div>
        <div class="row justify-content-lg-start justify-content-center g-4">
            <?php 
                $list_all_sectors = true;
                if(!get_field('list_all_sectors')){
                    $list_all_sectors = false;
                }

                if($list_all_sectors){
                    $args = array(
                        'post_type' => 'sector',
                        'posts_per_page' => '-1',
                        'post_parent' => '0',
                        'post_status' => 'publish',
                        'orderby' => 'title',
                        'order' => 'ASC',
                    );

                    $q = new WP_Query($args);
                    if($q->have_posts()){
                        while($q->have_posts()){
                            $q->the_post();
                            ?>
                                <div class="col-lg-3 col-md-6 col-sm-6" >
                                    <a href="<?php echo get_the_permalink(); ?>">
                                    <div class="sector-item">
                                        <h6 class="title"><?php echo get_the_title(); ?></h6>
                                        <p><?php echo get_the_excerpt(); ?></p>
                                        <div class="arrow-btn">
                                            <img src="<?php echo get_stylesheet_directory_uri(); ?>/assets/images/icons/arrow-white.svg" alt="arrow">
                                        </div>
                                    </div>
                                    </a>
                                </div>
                            <?php 
                        }
                        wp_reset_postdata( );
                    }
                }
                else{
                    $sectors = get_field('sectors_section');
                    if(!empty($sectors)){
                        foreach ($sectors as $value) {
                            ?>
                                <div class="col-lg-3 col-md-6 col-sm-6" >
                                    <a href="<?php echo isset($value['link']['url']) ? $value['link']['url'] : ''; ?>">
                                    <div class="sector-item">
                                        <h6 class="title"><?php echo $value['title']; ?></h6>
                                        <p><?php echo $value['excerpt']; ?></p>
                                        <div class="arrow-btn">
                                            <img src="<?php echo get_stylesheet_directory_uri(); ?>/assets/images/icons/arrow-white.svg" alt="arrow">
                                        </div>
                                    </div>
                                    </a>
                                </div>
                            <?php 
                        }
                    }
                }

                
            ?>
        </div>
    </div>
</div>


<?php $about_us_section = get_field('about_us_section'); ?>
<div class="about-us-section mt-70">
    <div class="container-one">
        <div class="row g-4">
            <div class="col-sm-7 col-md-7 col-lg-7">
                <div class="section-title-one style-yellow">
                    <h2 ><?php echo $about_us_section['title']; ?></h2>
                </div>
                <div >
                    <?php echo $about_us_section['content']; ?>
                </div>
            </div>
            <div class="col-sm-5 col-md-5 col-lg-5">
                <div class="about-us-quote-border-box">
                    <div class="border-box"></div>
                    <div class="quote-box">
                        <?php echo $about_us_section['quote']; ?>
                    </div>
                </div>
            </div>
        </div>
        <div class="row"></div>
    </div>
</div>


<div class="box-sectoin mt-70 d-none">
    <div class="container-one">
        <div class="row">
            <div class="col-lg-6">

            </div>
            <div class="col-lg-6    ">
                <div class="insight-box-area" >
                    <div class="red-box"></div>
                </div>
            </div>
        </div>
    </div>
</div>
<?php 
    $trending_section = get_field('trending_section');
?>
<div class="trending-section pl-container d-none">
    <div class="container-fluid">
        <div class="row justify-content-start">
            <div class="col-sm-5 col-md-5 col-lg-5" >
                <div class="section-title-one style-green">
                    <h2><?php echo $trending_section['title'];?></h2>
                </div>
                <?php echo $trending_section['content'];?>
                <a href="<?php echo isset($trending_section['link']) && isset($trending_section['link']['url']) ? $trending_section['link']['url'] : '';?>" class="eg-btn btn--primary-green btn--lg2 mt-3"> <?php echo isset($trending_section['link']) && isset($trending_section['link']['title']) ? $trending_section['link']['title'] : '';?><i class="bi bi-arrow-right"></i></a>
            </div>
            <div class="col-sm-7 col-md-7 col-lg-7" >
                <div class="trending-image">
                    <img src=" <?php echo $trending_section['image']; ?>" alt="image">
                </div>
            </div>
        </div>
    </div>
</div>

<!-- ========== insights section end ============= -->


<?php $working_section = get_field('working_section'); ?>
<div class="working-section mt-200">
    <div class="container-one">
        <div class="row g-4">
            <div class="col-sm-7 col-md-7 col-lg-7">
                <?php 
                    foreach ($working_section['left_column'] as $value) {
                        ?>
                            <div class="section-title-one style-red">
                                <h2 ><?php echo $value['title'] ?></h2>
                                <div class="mb-40">
                                    <?php echo $value['content']; ?>
                                </div>
                            </div>
                        <?php 
                    }
                ?>
            </div>
            <div class="col-sm-5 col-md-5 col-lg-5">
                <div class="working-image-box">
                    <div class="box-1">
                        <img src="<?php echo $working_section['right_column']['image_1']['url']; ?>" alt="image">
                    </div>
                    <div class="box-2">
                        <img src="<?php echo $working_section['right_column']['image_2']['url']; ?>" alt="image">
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?php $ip_experts_section = get_field('ip_experts_section'); ?>
<div class="ip-experts-section mt-70 mb-500">
    <div class="container-one">
        <div class="row g-4">
            <div class="col-sm-7 col-md-7 col-lg-7">
                <div class="section-title-one style-blue">
                    <h2 ><?php echo $ip_experts_section['title']; ?></h2>
                </div>
                <div class="row justify-content-lg-start justify-content-center g-sm-4 g-2">
                    <?php 
                        foreach ($ip_experts_section['list'] as $value) {
                            ?>
                                <div class="col-lg-11 col-md-11 col-12">
                                    <div class="sector-item style-blue">
                                        <?php echo $value['content']; ?>
                                    </div>
                                </div>
                            <?php 
                        }
                    ?>
                </div>
            </div>
            <div class="col-sm-5 col-md-5 col-lg-5">
                <div class="ip-expert-image-border-box">
                    <div class="border-box"></div>
                    <div class="image-box">
                        <img src="<?php echo $ip_experts_section['image']['url']; ?>" alt="<?php echo $ip_experts_section['title']; ?>">
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>


<div class="footer-top-design-eight d-none">
    <div class="box"></div>
</div>

<?php get_footer();


