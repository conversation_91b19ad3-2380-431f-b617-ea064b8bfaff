<?php
/*
Template Name: People Listing with Filter
*/

// [wpif-inline-filter list_filter_wrapper_class="filter_list" list_filter_item_class="filter_item"]
// [wp-post-list-ui id="1120"]

// Exit if accessed directly.
defined( 'ABSPATH' ) || exit;

get_header();
$pll = pll_current_language();
?>
<style>
    .filter-search-area {
        background-color: #36a9e1;
    }
</style>
    <!-- ========== banner-section start============= -->

    <div class="banner-section d-flex flex-column align-items-staer justify-content-center position-relative"
        style="background-image: url('<?php echo get_the_post_thumbnail_url(get_the_ID(), 'full'); ?>')">
        <div class="inner-overlay"></div>
        <!-- style="background-image:linear-gradient(to bottom, rgba(255,255,255,0.05) 0%, rgba(0,0,0,0.15) 50%), radial-gradient(at top center, rgba(255,255,255,0.20) 50%, rgba(0,0,0,0.40) 190%), url('<?php echo get_the_post_thumbnail_url(get_the_ID(), 'full'); ?>')"> -->
        <!-- style="background-image:linear-gradient(to bottom, rgba(255,255,255,0.05) 0%, rgba(0,0,0,0.15) 100%), radial-gradient(at top center, rgba(255,255,255,0.40) 0%, rgba(0,0,0,0.40) 120%), url('<?php echo get_the_post_thumbnail_url(get_the_ID(), 'full'); ?>')"> -->
        <div class="container text-center">
            <div class="banner-content">
                <h1 ><?php echo get_field('banner_title') ? get_field('banner_title') : get_the_title(); ?></h1>
            </div>
        </div>
    </div>

    <!-- ========== banner-section end============= -->

    <!-- ========== people details section start ============= -->

    <div class="people-section pt-60 pb-60">
        <div class="container-one">
            <div class="row">
                <div class="col-lg-6">
                    <div class="section-title-one style-yellow">
                        <h2 ><?php echo get_field('people_title'); ?></h2>
                        <div >
                            <p><?php echo get_field('people_description'); ?></p>
                        </div>
                    </div>
                </div>
                <div class="col-lg-6">
                    <div class="right-box">
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- ========== people details section end ============= -->

    <div class="filter-search-area pt-100 pb-100">        
        <div class="container-one" >
            <form class="filter-search-form" id="people-filter-search-form" method="post" action="">
                <input type="hidden" name="action" value="get_main_search_people_data">
                <!-- <div class="row row-cols-lg-5 row-cols-md-4 row-cols-sm-2 row-cols-2 justify-content-start g-3"> -->
                <div class="row row-cols-lg-4 row-cols-md-4 row-cols-sm-2 row-cols-2 justify-content-start g-3">
                    <div class="col d-none">
                        <div class="form-inner">
                            <input type="submit" value="">
                            <i class="bi bi-search"></i>
                        </div>
                    </div>
                    <div class="col">
                        <div class="form-inner">
                            <input type="text" name="name" placeholder="<?php echo __('Name', 'picostrap5-child-base'); ?>">
                        </div>
                    </div>
   
                    <div class="col">
                        <div class="form-inner">
                            <select name="role">
                                <option value=""><?php echo __('Role', 'picostrap5-child-base'); ?></option>
                                <?php 
                                    $roles = get_terms( array(
                                        // 'taxonomy'   => 'people_role',
                                        'taxonomy'   => 'group_listing',
                                        'hide_empty' => true,
                                        'orderby' => 'name',
                                        'orderby' => 'count',
                                        'orderby' => 'meta_value',
                                        'meta_key' => 'order',
                                        'order' => 'DESC',
                                        'order' => 'ASC',
                                    ) );

                                    if(!empty($roles)){
                                        foreach ($roles as $value) {
                                            $l = pll_current_language();
                                            $classAdd = '';
                                            /*
                                            if($value->term_id == '1937' &&  $l == 'en'){
                                               $classAdd = 'style="display:none"';
                                            } */
                                            ?>
                                                <option <?php echo $classAdd;?> value="<?php echo $value->term_id; ?>"><?php echo $value->name; ?></option>
                                            <?php 
                                        }
                                    }
                                ?>
                            </select>
                        </div>
                    </div>
                    <div class="col">
                        <div class="form-inner">
                            <select name="location">
                                <option value=""><?php echo __('Location', 'picostrap5-child-base'); ?></option>
                                <?php 
                                    $locations = get_terms( array(
                                        'taxonomy'   => 'location',
                                        'hide_empty' => false,
                                    ) );

                                    if(!empty($locations)){
                                        foreach ($locations as $value) {
                                            ?>
                                                <option value="<?php echo $value->term_id; ?>"><?php echo $value->name; ?></option>
                                            <?php 
                                        }
                                    }
                                ?>
                            </select>
                        </div>
                    </div>
                    <div class="col">
                        <div class="form-inner">
                            <select name="sector">
                                <option value=""><?php echo __('Sector', 'picostrap5-child-base'); ?></option>
                                <?php 
                                    $sectors = get_posts(['post_type' => 'sector', 'posts_per_page' => '-1', 'post_parent' => '0', 'orderby' => 'title', 'order' => 'ASC']);
                                    if(!empty($sectors)){
                                        foreach ($sectors as $key => $value) {
                                            ?>
                                                <option value="<?php echo $value->ID; ?>"><?php echo $value->post_title; ?></option>
                                            <?php 
                                        }
                                    }
                                ?>
                            </select>
                        </div>
                    </div>
                </div>
            </form>
        </div>
    </div>


    <!-- ========== team section start ============= -->

    <div class="team-section pt-100">
        <div class="container-one">
            <div id="people-filter-search-list">
                <?php 
                    $terms = get_terms( array(
                        'taxonomy'   => 'people_role',
                        'taxonomy'   => 'group_listing',
                        'hide_empty' => true,
                        'orderby' => 'name',
                        'orderby' => 'count',
                        'orderby' => 'meta_value',
                        'meta_key' => 'order',
                        'order' => 'DESC',
                        'order' => 'ASC',
                        'number' => 0,
                        'fields' => 'all',

                    ) );

                    if(!empty($terms)){
                        foreach ($terms as $t) {
                            ?>
                                <div class="section-title-two" id="<?php echo $t->name;?>">
                                    <h4><?php echo $t->name; // __('Partners', 'picostrap5-child-base'); ?></h4>
                                </div>
                                <div class="row row-cols-xl-4 row-cols-lg-4 row-cols-md-3 row-cols-sm-2 row-cols-2 g-4 mb-20">
                                    <?php 
                                        $args = [
                                            'post_type' => 'people',
                                            'posts_per_page' => '-1',
                                            'post_status' => 'publish',
                                            'orderby' => 'title',
                                            'orderby' => 'meta_value',
                                            'meta_key' => 'last_name',
                                            'order' => 'ASC',

                                            'tax_query' => array(
                                                array(
                                                    'taxonomy' => 'people_role',
                                                    'taxonomy' => 'group_listing',
                                                    'field' => 'slug',
                                                    'terms' => array($t->slug),
                                                    'operator' => 'IN',
                                                ),
                                            ),
                                        ];

                                        $q = new WP_Query($args);
                                        if($q->have_posts()){
                                            while($q->have_posts()){
                                                $q->the_post();

                                                $roles = get_the_terms( get_the_ID(), 'people_role' );
                                                $roles = join(', ', wp_list_pluck($roles, 'name'));

                                                $locations = get_the_terms( get_the_ID(), 'location' );
                                                $locations = join(', ', wp_list_pluck($locations, 'name'));

                                                $reverse_person_photo_places = get_field('reverse_person_photo_places');
                                                $imgSize = '';
                                                if(get_field('thum_image_size') === 'large'){
                                                    $imgSize = 'large';
                                                }else{
                                                    $imgSize = 'medium_large';
                                                }
                                                $image_url = get_field('person_photo_2') ? get_field('person_photo_2')['sizes'][$imgSize] : get_stylesheet_directory_uri() . '/assets/images/team/team.jpg';
                                                if($reverse_person_photo_places){
                                                    $image_url = get_field('person_photo_1') ? get_field('person_photo_1')['sizes'][$imgSize] : get_stylesheet_directory_uri() . '/assets/images/team/team.jpg';
                                                }

                                                $peopleLink = '';
                                                if($pll == 'es'){
                                                    $peopleLink =  str_replace("/people/","/equipo/", get_the_permalink());
                                                }else{
                                                    $peopleLink = get_the_permalink();
                                                }
                                                

                                 
                                                ?>
                                                
                                                    <div class="col">
                                                        <div class="team-item style-two style-two">
                                                            
                                                            <div class="team-image">
                                                                <a  hreflang="<?php echo esc_attr($pll); ?>" href="<?php echo $peopleLink; ?>">
                                                                    <img class="<?php echo strpos($image_url, 'team/team.jpg') === false ? '' : 'no-person-image'; ?>" src="<?php echo $image_url; ?>" alt="<?php echo get_the_title(); ?>">
                                                                    <?php 
                                                                        if(strpos($image_url, 'team/team.jpg') === false){
                                                                            ?>
                                                                            <?php 
                                                                        }
                                                                        else{
                                                                            $classes = array('bg-primary-red', 'bg-primary-green', 'bg-primary-yellow', 'bg-primary-blue');
                                                                            shuffle($classes);
                                                                            $c = array_rand($classes, 1);
                                                                            ?>
                                                                                <div class="no-person-image-box <?php echo $classes[$c]; ?>"></div>
                                                                            <?php
                                                                        }
                                                                    ?>
                                                                </a>
                                                            </div>
                                                            <div class="team-content">
                                                                <h6><a hreflang="<?php echo esc_attr($pll); ?>" href="<?php echo $peopleLink; ?>"><?php echo get_the_title(); ?></a></h6>
                                                                <span><?php echo $roles; ?> </span>
                                                                <span><?php echo $locations; ?></span>
                                                            </div>
                                                        </div>
                                                    </div>
                                                <?php 
                                            }
                                            wp_reset_query();
                                        }
                                    ?>
                                </div>
                            <?php 
                        }
                    }
                ?>
            </div>
        </div>
    </div>

    <!-- ========== team section end ============= -->

    <!-- ========== expertise-section start======== -->

    <div class="expertise-section-1">
        <div class="container-one">
            <div class="row justiy-content-start">
                <div class="col-lg-7">
                    <div class="section-title-one style-blue">
                        <h2 ><?php echo get_field('expertise_title'); ?></h2>
                    </div>
                    <div>
                        <?php echo get_field('expertise_description'); ?>
                        <?php 
                            $expertise_button = get_field('expertise_button');
                            if($expertise_button && isset($expertise_button['url'])){
                                ?>
                                    <a  hreflang="<?php echo esc_attr($pll); ?>" href="<?php echo $expertise_button['url']; ?>" class="eg-btn btn--primary-blue btn--lg2"><?php echo $expertise_button['title']; ?> <i
                                            class="bi bi-arrow-right"></i></a>
                                <?php 
                            }
                        ?>
                    </div>
                </div>
                <div class="col-lg-5">
                    <div class="right-box-blue">

                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- ========== expertise-section end======== -->

    <div class="footer-top-design-one">
        <div class="box"></div>
    </div>


    <script type="text/javascript">
        jQuery(function($){
            var ajax_var = null;

            $(document).on('keyup', '#people-filter-search-form [name="name"]', function(e){
                $(this).closest('form').trigger('submit');
            });
            $(document).on('change', '#people-filter-search-form select[name="role"]', function(e){
                $(this).closest('form').trigger('submit');
            });
            $(document).on('change', '#people-filter-search-form select[name="location"]', function(e){
                $(this).closest('form').trigger('submit');
            });
            $(document).on('change', '#people-filter-search-form select[name="sector"]', function(e){
                /*
                if($('select[name="sector"] option:selected').text() != 'Trade marks') {
                    $('select[name="role"]').val('');
                    $('select[name="role"] option').each(function(){
                        if($(this).text() == 'Partners') $(this).hide();
                    });
               }else{
                $('select[name="role"] option').each(function(){
                        if($(this).text() == 'Partners') $(this).show();
                    });
               }
               */
                $(this).closest('form').trigger('submit');
            });

            $(document).on('submit', '#people-filter-search-form', function(e){
                e.preventDefault();

                var this_form = $(this);
                var this_data = this_form.serialize();

                ajax_var = $.ajax({
                    beforeSend : function()    {          
                        if(ajax_var != null) {
                            ajax_var.abort();
                            ajax_var = null;
                        }
                    },
                    url: '<?php echo admin_url("admin-ajax.php"); ?>',
                    method: 'POST',
                    dataType: 'JSON',
                    data: this_data,
                    success: function(r){
                        // $(document).find('#people-filter-search-list').html(r.html);
                        var data = r.data;
                        var roles = r.roles;
                        let output = '';
                        let no_result = true;

                        /*roles.forEach(function(role){
                            output += `
                                <div class="section-title-two">
                                    <h4>${role.name}</h4>
                                </div>
                                <div class="row row-cols-xl-6 row-cols-lg-5 row-cols-md-4 row-cols-sm-2 row-cols-2 g-4 mb-20">
                            `;

                            data.forEach(function (item) {
                                var roles_data = item.roles_data;
                                var related_to_role = false;
                                roles_data.forEach(function(rd){
                                    if(rd.term_id == role.term_id){
                                        related_to_role = true;
                                    }
                                });

                                if(!related_to_role){
                                    continue;
                                }

                                output += `
                                    <div class="col">
                                        <div class="team-item style-two style-two">
                                            <div class="team-image">
                                                <a href="${item.permalink}">
                                                    <img src="${item.image}" alt="${item.title}">
                                                </a>
                                            </div>
                                            <div class="team-content">
                                                <h6><a href="${item.permalink}">${item.title}</a></h6>
                                                <span>${item.roles} </span>
                                                <span>${item.locations}</span>
                                            </div>
                                        </div>
                                    </div>
                                `
                            });

                            output += `</div>`;
                        });*/

                        for(let i = 0; i < roles.length; i++){
                            var role = roles[i];

                            output += `
                                <div class="section-title-two">
                                    <h4>${role.name}</h4>
                                </div>
                                <div class="row row-cols-xl-4 row-cols-lg-4 row-cols-md-3 row-cols-sm-2 row-cols-2 g-4 mb-20">
                            `;

                            for(let j = 0; j < data.length; j++){
                                var item = data[j];    
                                var roles_data = item.roles_data;
                                var related_to_role = false;
                                
                                for (let k = 0; k < roles_data.length; k++) {
                                    var rd = roles_data[k];
                                    if(rd.term_id == role.term_id){
                                        related_to_role = true;
                                        break;
                                    }
                                }

                                if(!related_to_role){
                                    continue;
                                }

                                output += `
                                    <div class="col">
                                        <div class="team-item style-two style-two">
                                            <div class="team-image">
                                                <a href="${item.permalink}">
                                                    <img src="${item.image}" alt="${item.title}">
                                                </a>
                                            </div>
                                            <div class="team-content">
                                                <h6><a href="${item.permalink}">${item.title}</a></h6>
                                                <span>${item.roles} </span>
                                                <span>${item.locations}</span>
                                            </div>
                                        </div>
                                    </div>
                                `;

                                no_result = false;
                            }

                            output += `</div>`;
                        }

                        if(no_result){
                            output = `
                                <div class="row row-cols-xl-12 row-cols-lg-12 row-cols-md-12 row-cols-sm-12 row-cols-12 g-4 mb-20">
                                    <div class="col">
                                        <h4 class="people-no-match text-center">No match</h4>
                                    </div>
                                </div>
                            `;
                        }

                        document.getElementById('people-filter-search-list').innerHTML = output;
                    }
                });
            });
        });
    </script>

<?php 

get_footer();


return;
if ( ! function_exists( 'post_exists' ) ) {
    require_once( ABSPATH . 'wp-admin/includes/post.php' );
}

function people_get_meta_values( $key = '', $type = 'post', $status = 'publish' ) {
    
    global $wpdb;
    
    if( empty( $key ) )
        return;
    
    $r = $wpdb->get_col( $wpdb->prepare( "
        SELECT pm.meta_value FROM {$wpdb->postmeta} pm
        LEFT JOIN {$wpdb->posts} p ON p.ID = pm.post_id
        WHERE pm.meta_key = %s 
        AND p.post_status = %s 
        AND p.post_type = %s
    ", $key, $status, $type ) );
    
    $r = array_unique($r);
    return $r;
}

get_header();
?>

<style type="text/css">

    .letter-wrapper{
        position: relative;
    }
    .letter-wrapper .letter{
        position: absolute;
        z-index: 1;
        background-color: #000;
        color: #fff;
        padding: 5px 10px;
    }
    .people-inline-filter-form.banner-search-form li{
        list-style: none;
    }
    .people-inline-filter-form.banner-search-form .ms-options ul{
        padding-left: 10px;
    }
    .banner-search-form .ms-options-wrap > .ms-options > ul input[type="checkbox"] {
        width: 15px;
        height: 15px;
        top: 12px;
    }
    .banner-search-form .form-inner .nice-select{ 
        display: none;
    }
</style>

<?php 
/*
$args = array(
    'post_type' => 'people',
    'posts_per_page'=> -1,
    'orderby' => 'date',
    'order'   => 'DESC',
);
$query = new WP_Query( $args );
if ($query->have_posts()) {
   while ($query->have_posts()) {
       $query->the_post();
       $content_text = substr(get_field('about', get_the_ID()), 0, 250);
       WPSEO_Meta::set_value('metadesc', $content_text, get_the_ID());
       update_post_meta(get_the_ID(), '_yoast_wpseo_description', $content_text );
   }
} */
    /*
     $args = [
        'post_type' => 'people',
        'posts_per_page' => '-1',
        'post_status' => 'publish',
    ];

    $query = new WP_Query($args);
    if($query->have_posts()){
        while($query->have_posts()){
            $query->the_post();
            echo get_field('testimonials').' = '.get_the_ID().' </br>';
        }
    } */
?>
<?php /*
<div class="container">
    <div class="row">
        <div class="col-md-10 offset-md-1 py-5">
            <div class="people-inline-filter">
                <div class="people-inline-filter-contrainer">
                    <form method="post" action="" class="people-inline-filter-form">
                        <div class="form-group">
                            <input type="text" name="s" value="" placeholder="Name" class="form-control people-name" autocomplete="off">
                            <button type="submit" class="btn btn-primary">Search</button>
                        </div>
                        <div class="people-inline-filter-options">
                            <div class="row">
                                <div class="col-sm-4">
                                    <div class="form-group">
                                        <label class="col-form-label">Role</label>
                                        <?php 
                                            $roles = people_get_meta_values('role', 'people');
                                        ?>
                                        <select multiple name="roles[]" id="people-inline-filter-roles">
                                            <?php 
                                                foreach ($roles as $role) {
                                                    ?>
                                                        <option value="<?php echo $role; ?>"><?php echo $role; ?></option>
                                                    <?php 
                                                }
                                            ?>
                                        </select>
                                    </div>
                                </div>
                                <div class="col-sm-4">
                                    <div class="form-group">
                                        <label class="col-form-label">Expertise</label>
                                        <?php 
                                            $categories = get_categories( array(
                                                'orderby' => 'name',
                                                'order'   => 'ASC'
                                            ) );

                                            $category_teams = [];
                                        ?>
                                        <select multiple name="expertises[]" id="people-inline-filter-expertise">
                                            <?php 
                                                foreach ($categories as $category) {
                                                    $team = get_field('team', 'category_' . $category->term_id);
                                                    $category_teams[$category->name] = wp_list_pluck($team, 'post_title');
                                                    ?>
                                                        <option value="<?php echo $category->name; ?>"><?php echo $category->name; ?></option>
                                                    <?php 
                                                }
                                            ?>
                                        </select>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
                <div class="people-inline-filter-posts">
                    <div class="row">
                        <?php 
                            
                            $args = [
                                'post_type' => 'people',
                                'posts_per_page' => '-1',
                                'post_status' => 'publish',
                                'orderby' => 'title',
                                'orderby' => 'meta_value',
                                'meta_key' => 'last_name',
                                'order' => 'ASC',
                            ];

                            $q = new WP_Query($args);
                            if($q->have_posts()){
                                $letter_now = '';
                                $letter = '';
                                $letter_change = true;
                                while($q->have_posts()){
                                    $q->the_post();
                                    ?>
                                        <div class="col-lg-3 col-md-4 col-sm-6 filter_item" data-title="<?php the_title(); ?>" data-role="<?php the_field('role'); ?>" data-expertise="<?php  ?>">
                                            <div class="team-item style-two style-two">
                                                <?php 
                                                    $letter = substr(get_field('last_name'), 0, 1);
                                                    if($letter_change && $letter_now != $letter){
                                                        ?>
                                                            <span class="team-tag letter letter-<?php echo $letter; ?>"><?php echo $letter; ?></span>
                                                        <?php 
                                                        $letter_now = $letter;
                                                    }
                                                ?>
                                                
                                                <div class="team-image">
                                                    <a href="<?php the_permalink(); ?>">
                                                        <img src="<?php echo get_field('thumbnail_image')['url']; ?>">
                                                    </a>
                                                </div>
                                                <div class="team-content">
                                                    <h6><a href="<?php the_permalink(); ?>"><?php the_title(); ?></a></h6>
                                                    <span>Partner</span>
                                                </div>
                                            </div>
                                        </div>
                                        <?php /*
                                        <div class="col-sm-3 filter_item" data-title="<?php the_title(); ?>" data-role="<?php the_field('role'); ?>" data-expertise="<?php  ?>">
                                            <div class="letter-wrapper">
                                                <?php 
                                                    $letter = substr(get_field('last_name'), 0, 1);
                                                    if($letter_change && $letter_now != $letter){
                                                        ?>
                                                            <span class="letter letter-<?php echo $letter; ?>">
                                                                <?php echo $letter; ?>
                                                            </span>
                                                        <?php 
                                                        $letter_now = $letter;
                                                    }
                                                ?>
                                                <a href="<?php the_permalink(); ?>">
                                                    <img src="<?php echo get_field('thumbnail_image')['url']; ?>">
                                                    <?php // echo $image_tag = apply_filters('post_thumbnail_html', $image_tag ); ?>
                                                    <h2><?php the_title(); ?></h2>
                                                </a>
                                            </div>
                                        </div> 
                                    <?php 
                                }
                            } 
                        ?>
                    </div>
                </div>
            </div>

            <?php 

            /*if ( have_posts() ) : 
                while ( have_posts() ) : the_post();
                    the_content();
                endwhile;
            else :
                _e( 'Sorry, no posts matched your criteria.', 'textdomain' );
            endif;
            ?>
        </div>
    </div>
</div> */ ?>

<!-- ******************* Hero Banner Area Start ******************* -->
<?php 
    if(!empty(get_the_post_thumbnail_url())){
        ?>
        <div style="background-image: url('<?php echo get_the_post_thumbnail_url(); ?>');" class="banner-section-two d-flex flex-column align-items-center justify-content-end">
            <div class="banner-big-text">
                <svg viewBox="0 0 1366 188" xmlns="http://www.w3.org/2000/svg">
                    <g clip-path="url(#clip0_1390_1280)">
                    <path d="M176.48 60.8208C169.97 56.8108 161.74 54.8008 151.78 54.8008C147.6 54.8008 143.35 55.4408 139.01 56.7308C134.67 58.0208 130.49 59.8208 126.48 62.1508C122.46 64.4808 118.85 67.2508 115.64 70.4608C113.01 73.0908 110.78 75.8908 108.91 78.8308C107.22 74.5408 105.13 70.8508 102.63 67.8108C98.93 63.3108 94.48 60.0208 89.26 57.9308C84.04 55.8408 78.06 54.8008 71.31 54.8008C66.97 54.8008 62.6 55.5208 58.18 56.9708C53.76 58.4208 49.63 60.3408 45.77 62.7508C41.92 65.1608 38.5 68.0908 35.53 71.5408C34.81 72.3708 34.14 73.2308 33.5 74.1008L33 57.2008H0V184.651H34.45V108.761C34.45 105.231 35.09 101.981 36.38 99.0008C37.66 96.0308 39.43 93.4608 41.68 91.2908C43.93 89.1208 46.66 87.4408 49.87 86.2308C53.08 85.0308 56.45 84.4208 59.99 84.4208C66.74 84.4208 71.92 86.5908 75.53 90.9308C79.14 95.2708 80.95 101.451 80.95 109.481V184.651H115.16V108.761C115.16 105.231 115.8 102.011 117.09 99.1208C118.37 96.2308 120.14 93.6608 122.39 91.4108C124.64 89.1608 127.37 87.4408 130.58 86.2308C133.79 85.0308 137.24 84.4208 140.94 84.4208C148.01 84.4208 153.27 86.5108 156.72 90.6808C160.17 94.8608 161.9 101.281 161.9 109.951V184.641H196.11V107.541C196.11 95.9808 194.46 86.3008 191.17 78.5108C187.89 70.7408 182.99 64.8308 176.48 60.8208Z"></path>
                    <path d="M249.28 57.2002H215.07V184.65H249.28V57.2002Z"></path>
                    <path d="M311.9 89.13C317.04 86 322.9 84.43 329.49 84.43C333.67 84.43 337.72 85.15 341.66 86.6C345.59 88.05 349.25 90.06 352.62 92.62C355.99 95.19 358.8 98 361.05 101.05L379.6 78.4C374.3 71.01 366.95 65.19 357.55 60.93C348.15 56.68 337.75 54.54 326.35 54.54C314.14 54.54 303.22 57.43 293.58 63.21C283.94 68.99 276.35 76.91 270.81 86.94C265.27 96.98 262.5 108.35 262.5 121.03C262.5 133.4 265.27 144.6 270.81 154.64C276.35 164.68 283.94 172.59 293.58 178.37C303.22 184.15 314.14 187.04 326.35 187.04C337.11 187.04 347.35 184.91 357.07 180.65C366.79 176.4 374.3 170.81 379.6 163.9L361.05 141.25C358.16 144.46 355.11 147.27 351.89 149.68C348.67 152.09 345.18 153.9 341.41 155.1C337.63 156.3 333.66 156.91 329.48 156.91C323.22 156.91 317.51 155.31 312.37 152.09C307.23 148.88 303.13 144.54 300.08 139.08C297.03 133.62 295.5 127.52 295.5 120.77C295.5 113.87 296.95 107.64 299.84 102.1C302.74 96.6 306.76 92.26 311.9 89.13Z"></path>
                    <path d="M485.67 60.3401C479.16 56.4801 471.09 54.5601 461.46 54.5601C454.55 54.5601 447.88 56.0101 441.46 58.9001C435.03 61.7901 429.49 65.6101 424.84 70.3401C423.89 71.3101 423.01 72.3101 422.19 73.3201V6.37012H388.22V184.66C390.31 184.66 392.8 184.66 395.69 184.66C398.58 184.66 401.79 184.66 405.33 184.66H422.44V108.28C422.44 104.75 423.08 101.53 424.37 98.6401C425.65 95.7501 427.54 93.1801 430.03 90.9301C432.52 88.6801 435.37 86.9501 438.58 85.7501C441.79 84.5501 445.33 83.9401 449.18 83.9401C454.16 83.7801 458.25 84.5801 461.47 86.3501C464.68 88.1201 467.09 90.9301 468.7 94.7801C470.3 98.6401 471.11 103.45 471.11 109.24V184.65H505.32V106.83C505.32 95.2701 503.67 85.5901 500.38 77.8001C497.08 70.0101 492.18 64.1901 485.67 60.3401Z"></path>
                    <path d="M623.13 72.5008C617.75 66.8008 611.4 62.3808 604.1 59.2508C596.79 56.1208 588.8 54.5508 580.13 54.5508C570.65 54.5508 561.94 56.2008 553.99 59.4908C546.04 62.7808 539.17 67.4008 533.39 73.3408C527.61 79.2808 523.11 86.3908 519.9 94.6608C516.69 102.931 515.08 112.051 515.08 122.011C515.08 134.541 517.93 145.701 523.63 155.501C529.33 165.301 537.36 173.011 547.72 178.631C558.08 184.251 570.16 187.061 583.98 187.061C589.92 187.061 595.87 186.341 601.81 184.891C607.75 183.441 613.53 181.281 619.16 178.381C624.78 175.491 629.84 172.041 634.34 168.021L618.2 145.131C612.74 149.471 607.68 152.521 603.02 154.291C598.36 156.061 593.22 156.941 587.6 156.941C579.41 156.941 572.3 155.421 566.28 152.361C560.26 149.311 555.64 144.931 552.43 139.231C550.59 135.961 549.29 132.381 548.51 128.511H639.41L639.65 117.911C639.81 108.751 638.45 100.321 635.55 92.6108C632.65 84.9108 628.51 78.2108 623.13 72.5008ZM562.41 87.9208C567.31 85.1108 573.37 83.7008 580.6 83.7008C585.26 83.7008 589.51 84.7108 593.37 86.7108C597.23 88.7208 600.4 91.4108 602.89 94.7808C605.38 98.1508 606.78 102.091 607.11 106.591V107.311H548.85C549.46 104.641 550.28 102.181 551.34 99.9608C553.82 94.7508 557.51 90.7308 562.41 87.9208Z"></path>
                    <path d="M686.07 6.37012H651.86V184.66H686.07V6.37012Z"></path>
                    <path d="M880.93 60.8208C874.42 56.8108 866.19 54.8008 856.23 54.8008C852.05 54.8008 847.8 55.4408 843.46 56.7308C839.12 58.0208 834.94 59.8208 830.93 62.1508C826.91 64.4808 823.3 67.2508 820.09 70.4608C817.46 73.0908 815.23 75.8908 813.36 78.8308C811.67 74.5408 809.58 70.8508 807.08 67.8108C803.38 63.3108 798.93 60.0208 793.71 57.9308C788.49 55.8408 782.51 54.8008 775.76 54.8008C771.42 54.8008 767.05 55.5208 762.63 56.9708C758.21 58.4208 754.08 60.3408 750.22 62.7508C746.37 65.1608 742.95 68.0908 739.98 71.5408C739.26 72.3708 738.59 73.2308 737.95 74.1008L737.45 57.2008H704.44V184.651H738.89V108.761C738.89 105.231 739.53 101.981 740.82 99.0008C742.1 96.0308 743.87 93.4608 746.12 91.2908C748.37 89.1208 751.1 87.4408 754.31 86.2308C757.52 85.0308 760.89 84.4208 764.43 84.4208C771.18 84.4208 776.36 86.5908 779.97 90.9308C783.58 95.2708 785.39 101.451 785.39 109.481V184.651H819.6V108.761C819.6 105.231 820.24 102.011 821.53 99.1208C822.81 96.2308 824.58 93.6608 826.83 91.4108C829.08 89.1608 831.81 87.4408 835.02 86.2308C838.23 85.0308 841.68 84.4208 845.38 84.4208C852.45 84.4208 857.71 86.5108 861.16 90.6808C864.61 94.8608 866.34 101.281 866.34 109.951V184.641H900.55V107.541C900.55 95.9808 898.9 86.3008 895.61 78.5108C892.34 70.7408 887.44 64.8308 880.93 60.8208Z"></path>
                    <path d="M1012.88 63.1008C1002.76 57.4008 991.2 54.5508 978.19 54.5508C965.34 54.5508 953.81 57.4008 943.62 63.1008C933.42 68.8008 925.39 76.6308 919.53 86.5908C913.66 96.5508 910.74 108.031 910.74 121.041C910.74 133.731 913.67 145.051 919.53 155.011C925.39 164.971 933.42 172.801 943.62 178.501C953.82 184.201 965.34 187.051 978.19 187.051C991.2 187.051 1002.77 184.201 1012.88 178.501C1023 172.801 1030.99 164.971 1036.85 155.011C1042.71 145.051 1045.64 133.731 1045.64 121.041C1045.64 108.031 1042.71 96.5508 1036.85 86.5908C1030.99 76.6408 1023 68.8108 1012.88 63.1008ZM1006.73 139.601C1003.92 145.061 1000.06 149.401 995.17 152.611C990.27 155.831 984.61 157.431 978.18 157.431C971.91 157.431 966.29 155.831 961.31 152.611C956.33 149.401 952.4 145.061 949.5 139.601C946.61 134.141 945.24 127.961 945.4 121.051C945.24 113.821 946.6 107.481 949.5 102.021C952.39 96.5608 956.33 92.2208 961.31 89.0108C966.29 85.8008 971.91 84.1908 978.18 84.1908C984.6 84.1908 990.26 85.7608 995.17 88.8908C1000.07 92.0208 1003.92 96.3608 1006.73 101.901C1009.54 107.441 1010.86 113.831 1010.71 121.051C1010.87 127.961 1009.54 134.141 1006.73 139.601Z"></path>
                    <path d="M1135.85 55.1508C1133.36 54.7508 1130.67 54.5508 1127.78 54.5508C1121.35 54.5508 1115.21 56.1208 1109.35 59.2508C1103.48 62.3808 1098.42 66.6008 1094.17 71.9008C1092.58 73.8808 1091.13 75.9508 1089.85 78.0908L1089.24 57.2008H1056.23V184.651H1090.68V118.881C1090.68 114.701 1091.36 110.931 1092.73 107.561C1094.09 104.191 1096.02 101.261 1098.51 98.7708C1101 96.2808 1103.89 94.3508 1107.18 92.9908C1110.47 91.6308 1113.96 90.9408 1117.66 90.9408C1120.55 90.9408 1123.4 91.3008 1126.21 92.0208C1129.02 92.7408 1131.31 93.5108 1133.08 94.3108L1142.24 56.9708C1140.46 56.1608 1138.34 55.5608 1135.85 55.1508Z"></path>
                    <path d="M1243.4 72.5008C1238.02 66.8008 1231.67 62.3808 1224.37 59.2508C1217.06 56.1208 1209.07 54.5508 1200.4 54.5508C1190.92 54.5508 1182.21 56.2008 1174.26 59.4908C1166.31 62.7808 1159.44 67.4008 1153.66 73.3408C1147.88 79.2808 1143.38 86.3908 1140.17 94.6608C1136.96 102.931 1135.35 112.051 1135.35 122.011C1135.35 134.541 1138.2 145.701 1143.9 155.501C1149.6 165.301 1157.63 173.011 1167.99 178.631C1178.35 184.251 1190.43 187.061 1204.25 187.061C1210.19 187.061 1216.14 186.341 1222.08 184.891C1228.02 183.441 1233.8 181.281 1239.43 178.381C1245.05 175.491 1250.11 172.041 1254.61 168.021L1238.47 145.131C1233.01 149.471 1227.95 152.521 1223.29 154.291C1218.63 156.061 1213.49 156.941 1207.87 156.941C1199.68 156.941 1192.57 155.421 1186.55 152.361C1180.53 149.311 1175.91 144.931 1172.7 139.231C1170.86 135.961 1169.56 132.381 1168.78 128.511H1259.68L1259.92 117.911C1260.08 108.751 1258.72 100.321 1255.82 92.6108C1252.91 84.9108 1248.78 78.2108 1243.4 72.5008ZM1182.68 87.9208C1187.58 85.1108 1193.64 83.7008 1200.87 83.7008C1205.53 83.7008 1209.78 84.7108 1213.64 86.7108C1217.5 88.7208 1220.67 91.4108 1223.16 94.7808C1225.65 98.1508 1227.05 102.091 1227.38 106.591V107.311H1169.12C1169.73 104.641 1170.55 102.181 1171.61 99.9608C1174.09 94.7508 1177.78 90.7308 1182.68 87.9208Z"></path>
                    <path d="M1364.19 134.181C1362.99 130.411 1360.94 127.071 1358.05 124.181C1354.83 120.811 1350.58 117.841 1345.28 115.271C1339.98 112.701 1333.47 110.371 1325.76 108.281C1319.98 106.681 1315.28 105.231 1311.67 103.941C1308.06 102.661 1305.44 101.531 1303.84 100.571C1302.23 99.2908 1300.99 97.8808 1300.11 96.3508C1299.22 94.8308 1298.78 93.1808 1298.78 91.4108C1298.78 89.6408 1299.14 88.0408 1299.86 86.5908C1300.58 85.1408 1301.67 83.9008 1303.11 82.8608C1304.56 81.8208 1306.36 81.0508 1308.53 80.5708C1310.7 80.0908 1313.14 79.8508 1315.88 79.8508C1318.93 79.8508 1322.3 80.5408 1326 81.9008C1329.69 83.2708 1333.39 85.0308 1337.08 87.2008C1340.77 89.3708 1344.07 91.9008 1346.96 94.7908L1365.27 74.3108C1361.25 70.3008 1356.55 66.8008 1351.18 63.8308C1345.8 60.8608 1340.01 58.5708 1333.83 56.9608C1327.64 55.3608 1321.26 54.5508 1314.68 54.5508C1305.84 54.5508 1297.69 56.1608 1290.23 59.3708C1282.76 62.5808 1276.77 67.0808 1272.28 72.8608C1267.78 78.6408 1265.53 85.3908 1265.53 93.1008C1265.53 97.9208 1266.29 102.341 1267.82 106.351C1269.34 110.371 1271.55 113.901 1274.45 116.951C1277.98 120.651 1282.56 123.901 1288.18 126.711C1293.8 129.521 1300.71 131.971 1308.9 134.061C1314.2 135.511 1318.58 136.831 1322.03 138.041C1325.48 139.241 1327.85 140.331 1329.14 141.291C1332.03 143.221 1333.48 145.951 1333.48 149.481C1333.48 151.411 1333.12 153.091 1332.4 154.541C1331.68 155.991 1330.59 157.231 1329.15 158.271C1327.7 159.321 1326.02 160.121 1324.09 160.681C1322.16 161.241 1320.07 161.521 1317.82 161.521C1311.56 161.521 1305.41 160.361 1299.39 158.031C1293.37 155.701 1287.86 151.891 1282.89 146.591L1261.45 164.901C1267.23 171.971 1274.74 177.431 1283.98 181.281C1293.21 185.141 1303.69 187.061 1315.42 187.061C1325.06 187.061 1333.69 185.331 1341.32 181.881C1348.95 178.431 1354.97 173.611 1359.39 167.421C1363.81 161.241 1366.02 154.211 1366.02 146.341C1366 142.011 1365.4 137.961 1364.19 134.181Z"></path>
                    <path d="M250.592 27.552C254.804 17.3826 249.975 5.72389 239.806 1.51149C229.637 -2.7009 217.978 2.12822 213.765 12.2976C209.553 22.467 214.382 34.1258 224.552 38.3381C234.721 42.5505 246.38 37.7214 250.592 27.552Z"></path>
                    </g>
                    <defs>
                    <clipPath id="clip0_1390_1280">
                    <rect width="1366" height="187.07"></rect>
                    </clipPath>
                    </defs>
                </svg>
            </div>
            <div class="banner-search-area" style="min-width: 600px;">      
                <a hreflang="<?php echo esc_attr($pll); ?>" href="#team" class="scroll-down">
                    <i class="bi bi-chevron-down"></i>
                </a>     
                <div class="container-one">
                    <form method="post" action="" class="people-inline-filter-form banner-search-form">
                        <div class="row justify-content-center g-2">
                            <div class="col-xl-6 col-lg-6 col-md-6">
                                <div class="form-inner">
                                    <input type="text" name="s" value="" placeholder="Name" class="form-control people-name_1" autocomplete="off">
                                    <button type="submit" class="btn btn-primary">Search</button>
                                </div>
                            </div>
                            <div class="col-xl-3 col-lg-3 col-md-6 d-none">
                                <div class="form-inner">
                                    <?php 
                                        $roles = people_get_meta_values('role', 'people');
                                      

                                    ?>
                                    <select multiple name="roles[]" id="people-inline-filter-roles">
                                        <?php 
                                            foreach ($roles as $role) {
                                                ?>
                                                    <option value="<?php echo $role; ?>"><?php echo $role; ?></option>
                                                <?php 
                                            }
                                        ?>
                                    </select>
                                </div>
                            </div>
                            <div class="col-xl-3 col-lg-3 col-md-6 d-none">
                                <div class="form-inner">
                                    <?php 
                                        $args = array('hide_empty' => false, 'parent' => 226);
                                        $categories = get_categories( $args );

                                        $category_teams = [];
                                    ?>
                                    <select multiple name="expertises[]" id="people-inline-filter-expertise">
                                   
                                        <?php 
                                            foreach ($categories as $category) {
                                                $team = get_field('team', 'category_' . $category->term_id);
                                                $category_teams[$category->name] = wp_list_pluck($team, 'post_title');
                                                ?>
                                                    <option value="<?php echo $category->name; ?>"><?php echo $category->name; ?></option>
                                                <?php 
                                            }
                                        ?>
                                    </select>
                                </div>
                            </div>
                            <div class="col-xl-3 col-lg-3 col-md-6 d-none">
                                <div class="form-inner">
                                    <?php 
                                        $business_cat_id = array('hide_empty' => false, 'parent' => 1);
                                        $business_categories = get_categories( $business_cat_id );

                                        $indivisual_cat_id = array('hide_empty' => false, 'parent' => 225);
                                        $indivisual_categories = get_categories( $indivisual_cat_id );

                                        $busi_indi = array_merge($business_categories, $indivisual_categories);
                                       
                                        $category_teams = [];
                                    ?>
                                    <select multiple name="service[]" id="people-inline-filter-service">
                                        <optgroup label="Business">
                                            <?php 
                                                foreach ($business_categories as $category) {
                                                    $team = get_field('team', 'category_' . $category->term_id);
                                                    $category_teams[$category->name] = wp_list_pluck($team, 'post_title');
                                                    ?>
                                                        <option value="<?php echo $category->name; ?>"><?php echo $category->name; ?></option>
                                                    <?php 
                                                }
                                            ?>
                                        </optgroup>
                                        <optgroup label="Indivisual">
                                            <?php 
                                                foreach ($indivisual_categories as $category) {
                                                    $team = get_field('team', 'category_' . $category->term_id);
                                                    $category_teams[$category->name] = wp_list_pluck($team, 'post_title');
                                                    ?>
                                                        <option value="<?php echo $category->name; ?>"><?php echo $category->name; ?></option>
                                                    <?php 
                                                }
                                            ?>
                                        </optgroup>
                                        <?php
                                        /* 
                                            foreach ($busi_indi as $category) {
                                                $team = get_field('team', 'category_' . $category->term_id);
                                                $category_teams[$category->name] = wp_list_pluck($team, 'post_title');
                                                ?>
                                                    <option value="<?php echo $category->name; ?>"><?php echo $category->name; ?></option>
                                                <?php 
                                            } */
                                        ?>
                                    </select>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>  
        <?php 
    }
    
?>


<!-- ******************* Hero Banner Area End ******************* -->


<!-- ******************* Breadcrumb Area Start ******************* -->
<?php if(!empty(get_the_post_thumbnail_url())){ ?>
<div class="breadcrumb-section">
    
</div>
<?php } ?>
<!-- ******************* Breadcrumb Area End ******************* -->

<div class="realestate-team pt-55 pb-55 overflow-hidden" id="team">
    <div class="container-one">
        <div class="row">
            <div class="section-title2">
                <h1>Our People</h1>
            </div>
        </div>
        
        <div class="people-inline-filter">
            <div class="people-inline-filter-posts">
                <div class="row">
                    <?php 
                        
                        $args = [
                            'post_type' => 'people',
                            'posts_per_page' => '-1',
                            'post_status' => 'publish',
                            'orderby' => 'title',
                            // 'orderby' => 'meta_value',
                            // 'meta_key' => 'last_name',
                            'order' => 'ASC',
                        ];

                        $q = new WP_Query($args);
                        if($q->have_posts()){
                            $letter_now = '';
                            $letter = '';
                            $letter_change = true;
                            while($q->have_posts()){
                                $q->the_post();
                                ?>

                                    <div class="col-lg-3 col-md-4 col-sm-6 filter_item" data-title="<?php the_title(); ?>" data-role="<?php the_field('role'); ?>" data-expertise="<?php  ?>">
                                        <div class="team-item style-two style-two">
                                            <?php 
                                                $letter = substr(get_the_title(), 0, 1);
                                                if($letter_change && $letter_now != $letter){
                                                    ?>
                                                        <span class="team-tag letter letter-<?php echo $letter; ?>"><?php echo $letter; ?></span>
                                                    <?php 
                                                    $letter_now = $letter;
                                                }
                                            ?>
                                            <?php 
                                                $transformScale = '';
                                                if(!empty(get_field('transform_css', get_the_ID()))){
                                                    $transformScale = get_field('transform_css', get_the_ID());
                                                }else{
                                                    $transformScale = '1.4';
                                                }
                                                $transformScale = '1';
                                            ?>
                                            <div class="team-image">
                                                <a hreflang="<?php echo esc_attr($pll); ?>" href="<?php the_permalink(); ?>">
                                                    <img style="transform: scale(<?php echo $transformScale;?>);" src="<?php echo get_field('_1')['sizes']['medium_large']; ?>" alt="<?php the_title(); ?>">
                                                </a>
                                            </div>
                                            <div class="team-content">
                                                <h6><a hreflang="<?php echo esc_attr($pll); ?>" href="<?php the_permalink(); ?>"><?php the_title(); ?></a></h6>
                                                <span>
                                                <?php 
                                                    /*$job_title_terms = get_the_terms( get_the_ID() , 'Job title' );
                                                    if(!empty($job_title_terms)){
                                                        echo $job_title_terms[0]->name;
                                                    }*/
                                                ?>
                                                </span>
                                            </div>
                                        </div>
                                    </div>
                                    <?php /*
                                    <div class="col-sm-3 filter_item" data-title="<?php the_title(); ?>" data-role="<?php the_field('role'); ?>" data-expertise="<?php  ?>">
                                        <div class="letter-wrapper">
                                            <?php 
                                                $letter = substr(get_field('last_name'), 0, 1);
                                                if($letter_change && $letter_now != $letter){
                                                    ?>
                                                        <span class="letter letter-<?php echo $letter; ?>">
                                                            <?php echo $letter; ?>
                                                        </span>
                                                    <?php 
                                                    $letter_now = $letter;
                                                }
                                            ?>
                                            <a href="<?php the_permalink(); ?>">
                                                <img src="<?php echo get_field('thumbnail_image')['url']; ?>">
                                                <?php // echo $image_tag = apply_filters('post_thumbnail_html', $image_tag ); ?>
                                                <h2><?php the_title(); ?></h2>
                                            </a>
                                        </div>
                                    </div> */?>
                                <?php 
                            }
                        } 
                    ?>
                </div>
            </div>
        </div>
    </div>
</div>


<?php get_footer();

?>
<script type="text/javascript">
    jQuery(function($){
        /*$('#people-inline-filter-roles').multiselect({
            columns: 1,
            placeholder: 'Role',
        });
        $('#people-inline-filter-expertise').multiselect({
            columns: 1,
            placeholder: 'Sector',
        });
        $('#people-inline-filter-service').multiselect({
            columns: 1,
            placeholder: 'Service',
        });*/

        $(document).on('submit', '.people-inline-filter-form', function(e){
            e.preventDefault();

            var this_form = $(this);

            var roles = this_form.find('[name="roles[]"]').val();
            var expertises = this_form.find('[name="expertises[]"]').val();
            var service = this_form.find('[name="service[]"]').val();
            var search_for = this_form.find('[name="s"]').val();
            search_for = $.trim(search_for);
            search_for = search_for.toLowerCase();

            $('.people-inline-filter-posts .filter_item').each(function(){
                var item = $(this);
                if(search_for.length == 0 && roles.length == 0 && expertises.length == 0 && service.length == 0){
                    item.show();
                    return;
                }

                item.hide();

                if(roles.length > 0 && roles.includes(item.data('role'))){
                    // show item
                    item.show();
                }
                else if(expertises.length > 0){
                    var category_teams = <?php echo json_encode($category_teams); ?>;
                    var team_names = [];

                    for(var i = 0; i < expertises.length ; i++){
                        var exp = expertises[i];
                        exp = exp.replace('&', '&amp;');

                        for(var j = 0; j < category_teams[exp].length; j++){
                            team_names.push(category_teams[exp][j]);
                        }
                    }

                    if(team_names.includes(item.data('title'))){
                        item.show();
                    }
                }else if(service.length > 0){
                    var category_teams = <?php echo json_encode($category_teams); ?>;
                    var team_names = [];

                    for(var i = 0; i < service.length ; i++){
                        var exp = service[i];
                        exp = exp.replace('&', '&amp;');

                        for(var j = 0; j < category_teams[exp].length; j++){
                            team_names.push(category_teams[exp][j]);
                        }
                    }

                    if(team_names.includes(item.data('title'))){
                        item.show();
                    }
                }
                else if(search_for.length > 0){
                    var title = item.data('title');
                    title = title.toLowerCase();
                    if(title.includes(search_for)){
                        // show item
                        item.show();
                    }
                }
            });
        });

        $(document).on('change', '#people-inline-filter-roles, #people-inline-filter-expertise, #people-inline-filter-service', function(e){
            // e.preventDefault();

            $(this).closest('form').trigger('submit');
        });

        $(document).on('keyup', '.people-name_1', function(e){
            $(this).closest('form').trigger('submit');
        });
    });
</script>