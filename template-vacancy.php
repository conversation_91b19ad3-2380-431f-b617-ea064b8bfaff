<?php 
/*
* Template Name:  Vacancy
*/ 
// Exit if accessed directly.
defined( 'ABSPATH' ) || exit;

get_header();
?>
<style>
    .publication-block {
        border-bottom: 1px solid var(--border);
        margin-bottom: 10px;
    }
    .vacancy-page .publication-text h6 {
        font-size: 15px;
        font-weight: 700;
        margin-bottom: 0;
    }
    .about-people-card.vacancy-page .designation {
        background-color: transparent;;
        padding: 0;
        position: relative;
    }
    .vacancy-page .about-people-card .designation h1, .about-people-card .designation h2 {
        font-size: 40px;
        margin-bottom: 0;
        color: var(--text-primary);
        font-weight: 300;
        line-height: 1.1;
        }
        .vacancy-page .about-people-card .designation h2 {
        color: #777;
        }
    .vacancy-page .publication-text h6 a {
        color: rgb(54, 169, 225);
        border-bottom: 1px solid rgb(54, 169, 225);
    }
    .vacancy-page .publication-text p a {
        color: rgb(54, 169, 225);
    }
    .about-people-card.vacancy-page {
        padding: 40px 50px 300px;
        margin-bottom: 0;
    }
    .about-people-card.vacancy-page .designation h1 {
        font-size: 35px;
        margin-bottom: 5px;
        color: var(--text-primary);
        font-weight: 600;
        line-height: 1.1;
    }
    .about-people-card.vacancy-page .designation h4 {
        font-weight: 400;
        color: var(--text-primary);
        line-height: 1.1;
    }

    .vacancy-page .about-people-card {
        background-color: rgba(255, 255, 255, 0.5);
    }
    @media (max-width: 1199px) {
        .vacancy-page .about-people-card {
            margin-top: -200px;
        }
    }
    @media (max-width: 576px) {
        .vacancy-page .about-people-card {
            padding: 30px 15px 200px;
        }
    }

    .vacancy-aply-area .quote-box {
        background-color: var(--primary-red);
        padding: 20px 30px;
        width: 100%;
        width: 80%;
        color: #fff;
        font-weight: bold;
        min-height: auto;
        margin-top: -150px;
        margin-left: 20%;
        position: relative;
        z-index: 9;
        min-height: 300px;
        margin-bottom: 50px;
    }
    @media (min-width: 1400px) and (max-width: 1599px) {
        .vacancy-aply-area .quote-box {
            margin-left: 10px;
        }
    }
    @media (max-width: 1399px) {
        .vacancy-aply-area .quote-box {
            margin-left: 0px;
            width: 100%;
        }
    }
    .vacancy-aply-area .quote-box h5 {
        color: var(--white);
        font-weight: 600;
        margin-bottom: 0;
    }
    .vacancy-aply-area .quote-box p {
        color: var(--white);
        font-size: 20px;
        font-weight: 300;
        line-height: 1.3;
        margin-bottom: 10px;
    }
    @media (max-width: 576px) {
        .vacancy-aply-area .quote-box p {
            font-size: 17px;
        }
    }
    .vacancy-aply-area .quote-box p a {
        color: var(--white);
        border-bottom: 1px solid var(--white);
    }
    .vacancy-aply-area .quote-box span {
        font-size: 12px;
        font-weight: 700;
        color: var(--white);
    }

    .vacancy-orange-box {
        position: relative;
        padding: 70px 70px;
        z-index: 1;
    }
    @media (max-width: 991px) {
        .vacancy-orange-box {
            display: none;
            visibility: hidden;
        }
    }
    .vacancy-orange-box::before {
        content: "";
        position: absolute;
        left: 0;
        top: 0;
        background-color: rgb(245, 250, 253);
        height: 250px;
        width: 75%;
        z-index: -1;
    }
    .vacancy-orange-box:after {
        content: "";
        position: absolute;
        right: 0;
        bottom: 0px;
        border: 1px solid var(--text-primary);
        height: 150px;
        width: 240px;
        z-index: -1;
    }
    .vacancy-orange-box .box {
        width: 100%;
        height: 375px;
        background-color: rgb(249, 178, 52);
    }/*# sourceMappingURL=style.css.map */
    

    @media (max-width: 576px){
        .vacancy-page .about-people-card {
            margin-top: -260px;
        }

        .vacancy-page .about-people-card .box {
            height: 110px;
        }

        .vacancy-aply-area .quote-box 
        {
            margin-top: -190px;
            min-height: 150px;
        }
    }


</style>
<!-- ========== banner-section start============= -->
<?php 
    $vacancy_info = get_field('vacancy_info');
?>
<div class="banner-section d-flex flex-column align-items-staer justify-content-center position-relative"
    style="background-image: url('<?php echo get_the_post_thumbnail_url(get_the_ID(), 'full'); ?>')">
    <div class="inner-overlay"></div>
    <!-- style="background-image:linear-gradient(to bottom, rgba(255,255,255,0.05) 0%, rgba(0,0,0,0.15) 50%), radial-gradient(at top center, rgba(255,255,255,0.20) 50%, rgba(0,0,0,0.40) 190%), url('<?php echo get_the_post_thumbnail_url(get_the_ID(), 'full'); ?>')"> -->
    <!-- style="background-image:linear-gradient(to bottom, rgba(255,255,255,0.05) 0%, rgba(0,0,0,0.15) 100%), radial-gradient(at top center, rgba(255,255,255,0.40) 0%, rgba(0,0,0,0.40) 120%), url('<?php echo get_the_post_thumbnail_url(get_the_ID(), 'full'); ?>')"> -->
    <div class="container text-center">
        <div class="banner-content">
            <h1 ><?php echo $vacancy_info['banner_title'] ? $vacancy_info['banner_title'] : get_the_title(); ?></h1>
        </div>
    </div>
</div>

<!-- ========== banner-section end============= -->

<!-- ==========people-info section start ============= -->

<div class="people-info-section vacancy-page pr-container pt-100">
    <div class="container-fluid">
        <div class="row">
            <div class="col-lg-6">
                <div class="about-people-card vacancy-page">
                    <div class="designation">
                        <h1><?php the_title();?></h1>
                        <?php if(!empty($vacancy_info['department'])) :?>
                            <h4><?php echo $vacancy_info['department'];?></h4>
                        <?php endif; ?>
                        <?php if(!empty($vacancy_info['location'])) :?>
                            <h4><?php echo $vacancy_info['location'];?></h4>
                        <?php endif; ?>
                    </div>
                    <div class="box"></div>
                </div>
                <?php if(!empty($vacancy_info['apply_now_info'])) :?>
                <div class="vacancy-aply-area">
                    <div class="quote-box">
                        <?php echo $vacancy_info['apply_now_info'];?>
                    </div>
                </div>
                <?php endif; ?>
                <div class="vacancy-orange-box">
                    <div class="box"></div>
                </div>
            </div>
            <div class="col-lg-6">
                <?php the_content(); ?>
            </div>
        </div>
    </div>
</div>

<!-- ========== people-info section start ============= -->

<?php get_footer(); ?>