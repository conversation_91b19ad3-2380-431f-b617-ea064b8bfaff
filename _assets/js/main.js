(function ($) {
  "use strict";

  $(window).scroll(function() {
	$('.animated-section').each(function() {
	   var elementTop = $(this).offset().top;
	   var elementBottom = elementTop + $(this).outerHeight();
	//    var viewportTop = $(window).scrollTop();
	   var viewportTop = $(window).scrollTop();
	   var viewportBottom = viewportTop + $(window).height();
	   
	   if (elementBottom > viewportTop && elementTop < viewportBottom) {
		  $(this).addClass('animated');
		  
	   } else {
		  $(this).removeClass('animated');
	   }
	});
 });


    // mobile-menu
	$('.mobile-menu-btn').on("click",function(){
		$('.main-nav').addClass('show-menu');
	  });
	
	  $('.menu-close-btn').on("click",function(){
		$('.main-nav').removeClass('show-menu');
	  });
	
	  // mobile-drop-down
	
	  $(".main-nav .bi").on('click', function (event) {
		var $fl = $(this);
		$(this).parent().siblings().find('.sub-menu').slideUp();
		$(this).parent().siblings().find('.bi').addClass('bi-chevron-down');
		if ($fl.hasClass('bi-chevron-down')) {
			$fl.removeClass('bi-chevron-down').addClass('bi-chevron-up');
		} else {
			$fl.removeClass('bi-chevron-up').addClass('bi-chevron-down');
		}
		$fl.next(".sub-menu").slideToggle();
	  });
 



  /* ---------------------------------------------
     Mobile-search-area
     --------------------------------------------- */

$('.search-btn').on("click", function(){
	$('.mobile-search').addClass('slide');
  });
  
  $('.search-cross-btn').on("click", function(){
	$('.mobile-search').removeClass('slide');
  });
  


  /* ---------------------------------------------
     Sliders
     --------------------------------------------- */

	 $('select').niceSelect();

  
// Event Banner Slider Slider
var swiper = new Swiper(".event-banner-slider", {
	slidesPerView: 1,
	spaceBetween: 12,
	// effect: 'fade',
	loop: true,
	speed:1500,
	autoplay: {
	  delay:3000,
	},
	navigation: {
	  nextEl: ".next-btn-121",
	},
	pagination: {
	  el: ".swiper-pagination121",
	  clickable: true,
	},
  });

  // Testimonial Slider
var swiper = new Swiper(".testimonial-slider", {
	slidesPerView: 1,
	spaceBetween: 12,
	effect: 'fade',
	fadeEffect: {
		crossFade: true
	},
	loop: true,
	speed:1500,
	autoplay: {
	  delay:3000,
	},
	navigation: {
        nextEl: ".testimonial-next",
        prevEl: ".testimonial-prev",
      },
	pagination: {
	  el: ".testimonial-pagination",
	  clickable: true,
	},
  });

//   banner-slider
var swiper = new Swiper(".banner-slider", {
	slidesPerView: 1,
	spaceBetween: 12,
	effect: 'fade',
	fadeEffect: {
		crossFade: true
	},
	loop: true,
	speed:1500,
	autoplay: {
	  delay:3000,
	},
	pagination: {
	  el: ".banner-pagination",
	  clickable: true,
	},
  });

    // Magnific Popup video
$('.video-popup').magnificPopup({
	type: 'iframe'
  });

// Odometer Counter

$(".counter-single").each(function () {
	$(this).isInViewport(function (status) {
	if (status === "entered") {
		for (var i = 0; i < document.querySelectorAll(".odometer").length; i++) {
		var el = document.querySelectorAll('.odometer')[i];
		el.innerHTML = el.getAttribute("data-odometer-final");
		}
	}
	});
  });

  

// =================================
})(jQuery);



