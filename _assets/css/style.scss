@import url('https://fonts.googleapis.com/css2?family=Open+Sans:ital,wght@0,300;0,400;0,500;0,600;0,700;0,800;1,300;1,400;1,500;1,600;1,700&display=swap');

* {
  margin: 0;
  padding: 0;
  scroll-behavior: smooth;
}

:root {
  --text-primary: rgb(29, 29, 27);
  --border: rgba(29, 29, 27,0.3);
  --text-secondary: #575756;
  --white:#ffffff;
  --dark-bg1: #3C3C3B;
  --dark-bg2: #1D1D1B;

  --primary-red: rgb(227, 6, 19);
  --primary-red-light: rgba(227, 6, 19, 0.04);
  --primary-yellow:   rgba(249,178,51);
  --primary-yellow-light:   rgb(254,243,224);
  --primary-yellow-light2:   #f9b23314;
  --primary-blue: #36a9e1;
  --primary-blue-light: #F1F9FD;
  --primary-green:  rgb(147, 213, 0);
  --primary-green-dark:  rgba(58,170,53,1);
  // --primary-green-dark-light:  rgba(58, 170, 53, 0.06);
  --primary-green-dark-light:  #F3FAF3;
  --primary-green-light:  rgba(147, 213, 0,0.04);
  
  --font-open: 'Open Sans', sans-serif;
}

/*================================================
1. Mixins Css
=================================================*/

// flex-box
@mixin flex($position) {
  display: flex;
  justify-content: $position;
  flex-wrap: wrap;
  align-items: center;
}

@mixin img-adjust {
  background-size: cover;
  background-repeat: no-repeat;
}

@mixin font($font-size, $font-weight, $color) {
  font-size: $font-size;
  font-weight: $font-weight;
  color: $color;
}


@mixin listunstyle() {
  list-style: none;
  margin: 0;
  padding: 0;
}

@mixin display() {
  display: $none;
  visibility: $hidden;
}


// xl-device=====

@mixin xxl-down-device {
  @media (max-width: 1399px) {
    @content;
  }
}

@mixin xl-down-device {
  @media (max-width: 1199px) {
    @content;
  }
}

@mixin xxl-device {
  @media (min-width: 1400px) and (max-width: 1599px) {
    @content;
  }
}

@mixin xl-device {
  @media (min-width: 1200px) and (max-width: 1399px) {
    @content;
  }
}

@mixin lg-device {
  @media (min-width: 992px) and (max-width: 1199px) {
    @content;
  }
}

@mixin lg-up-device {
  @media (min-width: 992px) {
    @content;
  }
}

@mixin lg-down-device {
  @media (max-width: 991px) {
    @content;
  }
}

// md-device============
@mixin md-device {
  @media (min-width: 768px) and (max-width: 991px) {
    @content;
  }
}

@mixin xxl-up-device {
  @media (min-width: 1600px) {
    @content;
  }
}

@mixin md-up-device {
  @media (min-width: 768px) {
    @content;
  }
}

@mixin md-down-device {
  @media (max-width: 767px) {
    @content;
  }
}

// sm-device
@mixin sm-device {
  @media (min-width: 576px) and (max-width: 768px) {
    @content;
  }
}

@mixin sm-down-device {
  @media (max-width: 576px) {
    @content;
  }
}

@mixin threefifty-down-device() {
  @media (max-width: 350px) {
    @content;
  }
}


/*================================================
2. Global Css
=================================================*/
html {
  font-size: 100%;
  scroll-behavior: smooth;
}

body {
  margin: 0;
  padding: 0;
  font-family: var(--font-open);
  color: var(--text-primary);
  font-size: 15px;
  font-weight: 200;
  line-height: 1.5;
  box-sizing: border-box;
}

h1,h2,h3,h4,h5,h6{
  font-family: var(--font-open);
  font-weight: 600;
  line-height: 1.4;
  color: var(--text-primary);
}

button {
  outline: none;
  border: none;
}

i.bx {
  vertical-align: middle;
}

img{
  max-width: 100%;
  height: auto;
}

a{
  text-decoration: none;
}

.pt-40{
  padding-top: 40px;
}
.pb-40{
  padding-bottom: 40px;
}
.mb-140{
  margin-bottom: 140px;

  @include lg-down-device(){
    margin-bottom: 30px;
  }
}

.pt-50 {
  padding-top: 50px;
}
.pt-60 {
  padding-top: 60px;
}
.pb-60 {
  padding-bottom: 60px;
}
.pt-100 {
  padding-top: 100px;
  @include md-device(){
    padding-top: 70px;
  }
  @include md-down-device(){
    padding-top: 60px;
  }
}
.pb-100 {
  padding-bottom: 100px;
  @include md-device(){
    padding-bottom: 70px;
  }
  @include md-down-device(){
    padding-bottom: 60px;
  }
}
.pt-120 {
  padding-top: 120px;
}
.pb-120 {
  padding-bottom: 120px;
}
.pb-70 {
  padding-bottom: 70px;
}
.mb-100 {
  margin-bottom: 100px;

  @include md-down-device(){
    margin-bottom: 40px;
  }
}
.mb-60 {
  margin-bottom: 60px;
  @include md-down-device() {
    margin-bottom: 40px;
  }
}
.mb-65 {
  margin-bottom: 65px;
  @include md-down-device() {
    margin-bottom: 40px;
  }
}
.mt-120 {
  margin-top: 120px;

  @include lg-device() {
    margin-top: 100px;
  }

  @include lg-down-device() {
    margin-top: 90px;
  }
}
.mb-120 {
  margin-bottom: 120px;
}
.mb-100 {
  margin-bottom: 100px;
  @include md-down-device(){
    margin-bottom: 40px;
  }
}
.mt-100 {
  margin-top: 100px;
  @include md-down-device(){
    margin-top: 40px;
  }
}
.mb-90 {
  margin-bottom: 90px;
  @include md-down-device(){
    margin-bottom: 50px;
  }
}
.mb-15 {
  margin-bottom: 15px;
}

.mb-70 {
  margin-bottom: 70px;
  @include md-down-device() {
    margin-bottom: 40px;
  }
}
.mb-50 {
  margin-bottom: 50px;
}

.mb-45 {
  margin-bottom: 45px !important;
}

.mb-45 {
  margin-bottom: 45px;
}
.mb-35 {
  margin-bottom: 35px;
}
.mb-20 {
  margin-bottom: 20px;
}
.mt-15 {
  margin-top: 15px;
}

.mt-40 {
  margin-top: 40px;
}
.mt-40 {
  margin-top: 40px;
}

.mb-40 {
  margin-bottom: 40px;
}

.mb-30 {
  margin-bottom: 30px;
}
.mb-25 {
  margin-bottom: 25px;
}

.mt-50 {
  margin-top: 50px;
}

.mt-25 {
  margin-top: 25px;
}

.mb-50 {
  margin-bottom: 50px;
}

.mt-60 {
  margin-top: 60px;
  @include md-down-device() {
    margin-top: 40px;
  }
}

.mt-65 {
  margin-top: 65px;
  @include md-down-device() {
    margin-top: 45px;
  }
}

.mt-70 {
  margin-top: 70px;
  @include md-down-device() {
    margin-top: 40px;
  }
}
.title-pb-150{
  padding-bottom: 150px;
  @include xxl-device(){
    padding-bottom: 100px;
  }
  @include xl-device(){
    padding-bottom: 80px;
  }
  @include lg-device(){
    padding-bottom: 50px;
  }
  @include lg-down-device(){
    padding-bottom: 0px;
  }
}

.pl-container{
  padding-left: calc((100% - 1040px)/2);
  @include xl-device(){
    padding-left: calc((100% - 1020px)/2);
  }
  @include lg-device(){
    padding-left: calc((100% - 950px)/2);
  }
  @include md-device(){
    padding-left: calc((100% -	720px)/2);
    padding-right: calc((100% -	720px)/2);
  }
  @include sm-device(){
    padding-left: calc((100% - 540px)/2);
    padding-right: calc((100% - 540px)/2);
  }
  @include sm-down-device(){
    padding-left: 0;
    padding-right: 0;
  }
}
.pr-container{
  padding-right: calc((100% - 1040px)/2);
  @include xl-device(){
    padding-right: calc((100% - 1020px)/2);
  }
  @include lg-device(){
    padding-right: calc((100% - 950px)/2);
  }
  @include md-device(){
    padding-left: calc((100% -	720px)/2);
    padding-right: calc((100% -	720px)/2);
  }
  @include sm-device(){
    padding-left: calc((100% - 540px)/2);
    padding-right: calc((100% - 540px)/2);
  }
  @include sm-down-device(){
    padding-left: 0;
    padding-right: 0;
  }
}

.container-one{
  width: 100%;
  max-width: 1040px;
  margin-left: auto;
  margin-right: auto;
  padding-left: 15px;
  padding-right: 15px;

  @include xxl-device(){
    max-width: 1000px;
  }
  @include xl-device(){
    max-width: 1020px;
  }
  @include lg-device(){
    max-width: 950px;
  }
  @include md-device(){
    max-width: 720px;
  }
  @include sm-device(){
    max-width: 540px;
  }
  @include sm-down-device(){
    max-width: 100%;
  }
}

.image-adjust{
  @include img-adjust();
}

.mobile-search {
  input {
    border: none;
    border-radius: unset;
    width: 100%;
    background: transparent;
    transition: 0.3s ease-in-out;
    color: var(--text-primary);
  border-bottom: 1px solid var(--white);
    padding: 5px 20px;
    &:focus{
    border: none;
    outline: none;
  
    }
    &::placeholder{
        color: var(--white);
    }
    &::placeholder{
      font-size: 14px;
      color: var(--text-secondary);
    }
  }
}

.bg-green-light{
  background-color: var(--primary-green-dark-light);
}

/*=======================================
 06. Section-title
=======================================*/
.section-title-one{
  p{
    font-size: 18px;
    margin-bottom: 0;
    font-weight: 300;
    line-height: 1.3;
    @include md-down-device(){
      font-size: 16px;
    }
  }
  &.style-yellow{
    h2{
      &::after{
        background-color: var(--primary-yellow);
      }
    }
  }
  &.style-blue{
    h2{
      &::after{
        background-color: var(--primary-blue);
      }
    }
  }
  &.style-green{
    h2{
      &::after{
        background-color: var(--primary-green);
      }
    }
  }
  &.style-red{
    h2{
      &::after{
        background-color: var(--primary-red);
      }
    }
  }
  &.style-white{
    h2{
      color: var(--white);
      &::after{
        background-color: var(--white);
      }
    }
  }
  h2{
    font-size: 32px;
    font-weight: 300;
    margin-bottom: 0px;
    line-height: 1;
    position: relative;
    padding-bottom: 15px;
    margin-bottom: 20px;
    @include md-down-device(){
      font-size: 28px;
    }
    display: inline-block;
      position: relative;
      &::after{
        content: '';
        position: absolute;
        bottom: 0;
        left: 0;
        width: 100%;
        height: 2px;
        display: block;
      }
  }

}

.section-title-two{
  margin-bottom: 30px;
  h4{
    position: relative;
    font-size: 22px;
    font-weight: 600;
    padding-top: 10px;
    @include md-down-device(){
      font-size: 20px;
    }
    &::before{
      content: '';
      width: 100%;
      max-width: 310px;
      height: 1px;
      background-color: var(--text-primary);
      position: absolute;
      top: 0px;
      left: 0px;
    }
  }
}
.section-title-two-borderless{
  margin-bottom: 30px;
  h4{
    position: relative;
    font-size: 24px;
    font-weight: 600;
  }
}
.section-title-three{
  margin-bottom: 30px;
  h5{
    position: relative;
    font-size: 18px;
    font-weight: 800;
    // padding-top: 10px;
  }
}
.section-title-four{
  margin-bottom: 15px;
  h4{
    font-size: 20px;
    font-weight: 600;
    margin-bottom: 0px;
    line-height: 1;
    position: relative;
    padding-bottom: 15px;
    color: var(--white);
    @include md-down-device(){
      font-size: 30px;
    }
    display: inline-block;
      position: relative;
      &::after{
        content: '';
        position: absolute;
        bottom: 0;
        left: 0;
        width: 100%;
        height: 2px;
        display: block;
        background-color: var(--white);
      }
  }
}
.subtitle{
  position: relative;
  h6{
    font-size: 15px;
    font-weight: 700;
    line-height: 1;
  }
}


/*=======================================
 09. Buttons 
=======================================*/
.eg-btn {
  text-align: center;
  display: inline-flex;
  text-decoration: none;
  transition: all 0.45s ease-in-out;
  text-transform: capitalize;
  cursor: pointer;
  font-family: var(--font-open);
  border-radius: 0px;
}

.btn--primary-blue {
  color: var(--white);
  border-radius: 0px;
  position: relative;
  z-index: 1;
  display: inline-flex;
  justify-content: center;
  white-space: nowrap;

  background: transparent;
  transition: all 0.5s;
  border-radius: 0px;
  // overflow: hidden;
  background: var(--primary-blue);
  text-align: left;

  &::before {
    content: "";
    width: 0%;
    height: 100%;
    background: var(--text-primary);
    position: absolute;
    left: 0;
    top: 0;
    transition: all 0.5s;
    z-index: -1;
    opacity: 1;
  }
  &:hover{
    color: var(--white);
    i{
      right: 0;
      color: var(--white);
    }
    &::before{
      width: 100%;
    }
  }

}
.btn--primary-red {
  color: var(--white);
  border-radius: 0px;
  position: relative;
  z-index: 1;
  display: inline-flex;
  justify-content: center;
  white-space: nowrap;

  background: transparent;
  transition: all 0.5s;
  border-radius: 0px;
  // overflow: hidden;
  background: var(--primary-red);
  text-align: left;

  &::before {
    content: "";
    width: 0%;
    height: 100%;
    background: var(--text-primary);
    position: absolute;
    left: 0;
    top: 0;
    transition: all 0.5s;
    z-index: -1;
    opacity: 1;
  }
  &:hover{
    color: var(--white);
    i{
      right: 0;
      color: var(--white);
    }
    &::before{
      width: 100%;
    }
  }
}
.btn--primary-black {
  color: var(--white);
  border-radius: 0px;
  position: relative;
  z-index: 1;
  display: inline-flex;
  justify-content: center;
  white-space: nowrap;
  background: transparent;
  transition: all 0.5s;
  border-radius: 0px;
  // overflow: hidden;
  background: var(--text-primary);
  text-align: left;

  &.style-two{
    &:hover{
      .bi{
        color: var(--text-primary);
      }
    }
    .bi{
      color: var(--white);
    }
  }

  &::before {
    content: "";
    width: 0%;
    height: 100%;
    background: var(--white);
    position: absolute;
    left: 0;
    top: 0;
    transition: all 0.5s;
    z-index: -1;
    opacity: 1;
  }
  &:hover{
    color: var(--text-primary);
    i{
      right: 0;
      color: var(--white);
    }
    &::before{
      width: 100%;
    }
  }
}
.btn--primary-green {
  color: var(--white);
  border-radius: 0px;
  position: relative;
  z-index: 1;
  display: inline-flex;
  justify-content: center;
  white-space: nowrap;

  background: transparent;
  transition: all 0.5s;
  border-radius: 0px;
  // overflow: hidden;
  background: var(--primary-green-dark);
  text-align: left;

  &::before {
    content: "";
    width: 0%;
    height: 100%;
    background: var(--text-primary);
    position: absolute;
    left: 0;
    top: 0;
    transition: all 0.5s;
    z-index: -1;
    opacity: 1;
  }
  &:hover{
    color: var(--white);
    i{
      right: 0;
      color: var(--white);
    }
    &::before{
      width: 100%;
    }
  }
}
.btn--primary-yellow {
  color: var(--white);
  border-radius: 0px;
  position: relative;
  z-index: 1;
  display: inline-flex;
  justify-content: center;
  white-space: nowrap;

  background: transparent;
  transition: all 0.5s;
  border-radius: 0px;
  // overflow: hidden;
  background: var(--primary-yellow);
  text-align: left;

  &::before {
    content: "";
    width: 0%;
    height: 100%;
    background: var(--text-primary);
    position: absolute;
    left: 0;
    top: 0;
    transition: all 0.5s;
    z-index: -1;
    opacity: 1;
  }
  &:hover{
    color: var(--white);
    i{
      right: 0;
      color: var(--white);
    }
    &::before{
      width: 100%;
    }
  }
}

.arrow-button{
  i{
    font-size: 60px;
    color: var(--text-secondary);
    opacity: 0.7;
  }
}

.btn--lg {
  font-size: 13px;
  font-weight: 600;
  text-transform: capitalize;
  padding: 6px 30px;
  // min-width: 100px;

  @include md-down-device() {
    padding: 8px 25px;
    font-size: 12px;
  }
}

.btn--lg2 {
  font-size: 13px;
  font-weight: 600;
  text-transform: capitalize;
  padding: 8px 35px 8px 10px;
  position: relative;
  @include md-down-device() {
    padding: 4px 30px 4px 8px;
    font-size: 11px;
  }
  i{
    position: absolute;
    right: -15px;
    top: 50%;
    transform: translateY(-50%);
    color: var(--text-primary);
    font-size: 30px;
    display: inline-block;
    transition: 0.4s;
    @include md-down-device(){
      font-size: 25px;
      right: -10px;
    }
  }
}

.blog-btn{
  min-width: 142px;
}

.btn--md {
  padding: 10px 35px;
  font-size: 15px;
  font-weight: 700;

  @include md-down-device() {
    padding: 10px 40px;
  }
}

.btn--sm {
  font-size: 12px;
  font-weight: 600;
  padding: 6px 18px;
}
.btn--sm2 {
  font-size: 10px;
  font-weight: 600;
  padding: 3px 15px 3px 5px;
}

.bg-primary-blue-light{
  background-color: var(--primary-blue-light1);
}
.bg-primary-blue-light2{
  background-color: var(--primary-blue-light2);
}
.bg-primary-green-light{
  background-color: var(--primary-green-light);
}

.bg-primary-yellow-light{
  background-color: var(--primary-yellow-light);
}
.bg-primary-pink-light{
  background-color: var(--primary-pink-light);
}
.bg-primary-pink-light2{
  background-color: var(--primary-pink-light2);
}

/*=======================================
  28. search area start
=======================================*/

.mobile-search {
  background: rgba(#000, 0.85);
  box-shadow: 0px 0px 10px rgba(0, 0, 0, 0.09);
  width: 100%;
  height: 100%;
  border-radius: 4px;
  display: flex;
  justify-content: center;
  flex-wrap: wrap;
  align-items: center;
  align-items: center;
  position: fixed;
  cursor: pointer;
  transform: scale(0.7);
  top: 0;
  left: 0;
  z-index: 9999;
  opacity: 0;
  visibility: hidden;
  transition: .65s ease;
  padding: 35px 100px;

  @include md-down-device(){
      padding: 20px 20px;
  }

  label {
      color: #fff;
      margin-bottom: 20px;
      font-family: var(--font-nunito);
      
  }
  
 &.slide {
  // transform: translateY(0);
  transform: scale(1);
  opacity: 1;
  visibility: visible;
} 
.search-cross-btn {
  color: #fff;
  cursor: pointer;
  background: rgba(var(--white), 0.6);
  border-radius: 50%;
  height: 40px;
  width: 40px;
  line-height: 40px;
  text-align: center;
  line-height: 43px;
  transition: 0.5s ease;
  &:hover{
      // background: var(--white);
      transform: scale(1.1);
  }
}
.search-cross-btn i {
  font-size: 25px;
}
} 

/*=======================================
 11 .Header Start
=======================================*/

header.style-1 {
  background-color: transparent;
  width: 100%;
  z-index: 99;
  display: flex;
  justify-content: center;
  align-items: center;
  position: absolute;
  padding-left: calc((100% - 1040px)/2);
  padding-right: 12%;

  @include xl-device(){
    padding-left: calc((100% - 1020px)/2);
    padding-right: 5%;
  }
  @include lg-device(){
    padding-left: calc((100% - 950px)/2);
  }
  @include md-device(){
    padding-left: calc((100% -	720px)/2);
  }
  @include sm-device(){
    padding-left: calc((100% - 540px)/2);
  }
  @include sm-down-device(){
    padding-left: 0;
  }

  @include lg-down-device(){
    padding-right: 10px;
  }

  .mobile-logo-wrap{
    max-width: 100px;
  }

  .header-icons{
    position: absolute;
    top: 30px;
    right: -25px;
    width: 100%;
    max-width: 90px;
    min-width: 90px;

    &.style-white{
      ul{
        li{
          &::after{
            background-color: var(--white);
          }
          i{
            color: var(--white);
          }
        }
      }
    }

    @include lg-device(){
      right: -20px;
    }
    ul{
      @include listunstyle();
      display: flex;
      justify-content: flex-end;
      align-items: center;
      gap: 16px;
      li{
        position: relative;
        cursor: pointer;

        &:last-child{
          &::after{
            content: unset;
          }
        }

        &::after{
          content: '';
          position: absolute;
          top: 50%;
          transform: translateY(-50%);
          right: -8px;
          width: 1px;
          height: 20px;
          background-color: var(--text-primary);
        }
        
        i{
          color: var(--text-primary);
          font-size: 22px;
          line-height: 1;
        }
      }
    }
  }

  &.sticky {
    position: fixed;
    top: 0px;
    left: 0;
    z-index: 999;
    box-shadow: 5px 3px 40px rgba(100, 100, 100, 0.1);
    animation: smooth-header 0.65s linear;

    @keyframes smooth-header {
      0% {
        transform: translateY(-30px);
      }
      100% {
        transform: translateY(0px);
      }
    }
  }

  .main-nav {
    .mobile-menu-logo {
        display: none;
    }
  >  ul {
        list-style: none;
        margin: 0;
        padding: 0;
        padding-right: 40px;
       > li {
            display: inline-block;
            position: relative;
            padding: 0 10px;

            &:last-child{
              &::after{
                content: none;
              }
            }

             a {
                font-size: 14px;
                color: var(--text-primary);
                text-decoration: none;
                font-weight: 600;
                display: block;
                text-transform: uppercase;
                padding: 30px 0;
                position: relative;
                font-family: var(--font-work-sans);
                -webkit-transition: all .5s ease-out 0s;
                -moz-transition: all .5s ease-out 0s;
                -ms-transition: all .5s ease-out 0s;
                -o-transition: all .5s ease-out 0s;
                transition: all .5s ease-out 0s;
                position: relative;
                &:hover {
                  color: var(--primary-red);
                    &:after{
                        opacity: 1;
                        width: 100%;
                    }
                }
                &::after{
                    content: '';
                    position: absolute;
                    bottom: 25px;
                    left:0;
                    width: 0%;
                    height: 2px;
                    border-radius: 30px;
                    display: block;
                    background: var(--text-primary);
                    opacity: 0;
                    transition: all .5s ease-out 0s;
                }
                &.active{
                  color: var(--primary-red);
                  position: relative;
                  display: inline-block;
              }
            }
            i {
                width: 30px;
                font-size: 14px;
                text-align: center;
                color: var(--text-primary);
                font-style: normal;
                position: absolute;
                right: -8px;
                top: 31px;
                z-index: 999;
                cursor: pointer;
                display: none;
                opacity: 0;

                @include lg-down-device(){
                  color: var(--text-primary);
                  opacity: 1;
                }
            }

            ul.sub-menu {
                position: absolute;
                left: 0;
                right: 0;
                top: auto;
                margin: 0;
                min-width: 165px;
                border-radius: 0px;
                padding: 5px 0;
                opacity: 0;
                visibility: hidden;
                background: var(--text-primary);
                text-align: left;
                transition: all .55s ease-in-out;
                transform: translateY(20px);

                >li {
                    i {
                        position: absolute;
                        top: 10px;
                        right: 6px;
                        display: block;
                        color: var(--text-primary);
                    }

                    padding: 0;
                    display: block;
                    // border-bottom: 1px solid rgba(#383838, 0.1);
                    position: relative;

                    a {
                        display: block;
                        padding: 10px 17px;
                        color: var(--white);
                        font-weight: 400;
                        text-transform: uppercase;
                        font-size: 12px;
                        line-height: 1;
                        -webkit-transition: all .4s ease-out 0s;
                        transition: all .4s ease-out 0s;
                        position: relative;
                        &::after{
                            content: '';
                            position: absolute;
                            bottom: 0px;
                            left: 0px;
                            width: 0px;
                            height: 1px;
                            border-radius: 30px;
                            display: block;
                            background: linear-gradient(90deg, var(--primary-color1),transparent);
                            transition: all 0.5s ease-in-out; 
                        }
                        &:hover {
                            color: var(--primary-red);
                            &:after{
                                width: 100%;
                            }
                        }
                    }

                    a.active {
                        color: var(--primary-color1);
                    }

                    .sub-menu {
                        left: 215px;
                        position: absolute;
                        background: #0B0F14;
                        top: 0;

                        @media only screen and (max-width: 1199px) {
                            margin-left: 10px;
                            position: unset;
                            max-width: 230px;
                            min-width: 215px;
                            background: transparent;
                            top: 0;
                        }

                        li i {
                            display: block;
                        }
                    }

                    &:last-child {
                        border-bottom: none;
                    }
                }
            }

            &:hover {
                >ul.sub-menu {
                    visibility: visible;
                    opacity: 1;
                    transform: translateY(0);
                }
            }

        }

        li.menu-item-has-children>i {
            display: block;
        }

        &.style-white{
         @include lg-up-device(){
          li{
            a{
              color: var(--white);
            }
            &:last-child{
              &::after{
                content: none;
              }
            }
            &::after{
              content: '';
              background-color: var(--white);

              @include lg-down-device(){
                content: none;
              }
            }
          }
         }
        }
    }
}

@media only screen and (max-width: 991px) {
    .main-nav {
        position: fixed;
        top: 0;
        left: 0;
        width: 280px;
        padding: 30px 20px !important;
        z-index: 99999;
        height: 100%;
        overflow: auto;
        background: var(--white);
        -webkit-transform: translateX(-260px);
        transform: translateX(-100%);
        -webkit-transition: -webkit-transform .6s;
        transition: -webkit-transform .6s;
        transition: transform .6s;
        transition: transform .6s , -webkit-transform .6s;
        box-shadow: 0px 2px 20px rgba(#000, 0.03);

        &.show-menu {
            transform: translateX(0);
        }

        .mobile-menu-logo {
            text-align: left;
            padding-top: 20px;
            display: block;
            padding-bottom: 8px;
        }

        ul {
            float: none;
            text-align: left;
            padding: 35px 10px 10px 0;

            li {
                display: block;
                position: relative;
                padding: 0 5px;

                i {
                    display: block;
                }

                a {
                    padding: 10px 0;
                    display: block;
                    font-weight: 400;
                    font-size: 14px;
                }

                ul.sub-menu {
                    position: static;
                    min-width: 200px;
                    background: 0 0;
                    border: none;
                    opacity: 1;
                    visibility: visible;
                    -webkit-box-shadow: none;
                    box-shadow: none;
                    -webkit-transform: none;
                    transform: none;
                    -webkit-transition: none;
                    transition: none;
                    display: none;
                    margin-top: 0 !important;
                    transform: translateY(0px);
                    >li {
                        border-bottom: 1px solid transparent;

                        a {
                            color: var(--text-primary);
                            font-size: 12px;
                            font-weight: 500;

                            &:hover {
                                color: var(--primary-color1);
                                margin-left: 10px;
                            }
                        }

                        a.active {
                            color: var(--primary-color1);
                        }
                        i{
                            color: var(--text-primary);
                            right: -13px;
                        }
                    }
                }

                .bi {
                    top: 12px;
                    font-size: 12px;
                }
            }
        }
    }

    .mobile-menu {
        position: relative;
        top: 2px;
        padding: 0 5px;
        border-radius: 50%;
        display: inline-block;
    }

    .mobile-menu-btn{
      i{
        font-size: 35px;
      }
    }

    .cross-btn {
        display: inline-block !important;
        position: relative;
        width: 30px !important;
        height: 22px !important;
        cursor: pointer;
        border: 3px solid transparent !important;
        span {
            width: 100%;
            height: 2px;
            background: var(--primary-color1);
            display: block;
            position: absolute;
            right: 0;
            -webkit-transition: all .3s;
            transition: all .3s;
        }

        .cross-top {
            top: 0;
        }

        .cross-middle {
            top: 50%;
            -webkit-transform: translateY(-50%);
            transform: translateY(-50%);
            width: 100%;
        }

        .cross-bottom {
            bottom: 0;
            width: 100%;
        }
    }

    .cross-btn.h-active {
        span.cross-top {
            -webkit-transform: rotate(45deg);
            transform: rotate(45deg);
            top: 50%;
            margin-top: -1px;
        }

        span.cross-middle {
            -webkit-transform: translateX(-30px);
            transform: translateX(-30px);
            opacity: 0;
        }

        span.cross-bottom {
            -webkit-transform: rotate(-45deg);
            transform: rotate(-45deg);
            bottom: 50%;
            margin-bottom: -1px;
        }
    }
}
  .sidebar-button {
    display: flex;
    flex-direction: column;
    gap: 7px;
    cursor: pointer;
    align-items: flex-end;
    text-align: right;
    z-index: 9;
    position: relative;
    span{
      display: inline-block;
      width: 40px;
      height: 2px;
      border-radius: 3px;
      background-color: var(--text-primary);
      transition: all 0.5s ease ;
      &:nth-child(2){
        width: 25px;
      }
      &:last-child{
        width: 30px;
      }
    }
    &:hover{
      span{
        &:nth-child(2){
          width: 40px;
        }
        &:nth-child(3){
          width: 40px;
        }
      }
    }
  }
}
.logo-area{
  transform: translateY(65px);

  @include sm-down-device(){
      transform: translateY(0px);
  }
  img{
    max-width: 280px;
    @include sm-down-device(){
        max-width: 100px;
    }
  }
}
.logo-section{
  .logo-area{
    transform: translateY(0px);
  }
}
ul.language-list{
  list-style: none;
  margin: 0;
  padding: 0;
  padding-right: 60px;
  @include xl-down-device(){
    display: none;
    visibility: hidden;
  }
 > li {
      display: inline-block;
      position: relative;
      padding: 0 10px;

      &:last-child{
        &::after{
          content: none;
        }
      }

      &::after{
        content: '';
        position: absolute;
        top: 35px;
        right: 0;
        width: 1px;
        height: 12px;
        background-color: var(--text-primary);

        @include lg-down-device(){
          content: none;
        }
      }
       a {
          font-size: 13px;
          color: var(--text-primary);
          text-decoration: none;
          font-weight: 600;
          display: block;
          text-transform: uppercase;
          padding: 30px 0;
          position: relative;
          font-family: var(--font-work-sans);
          -webkit-transition: all .5s ease-out 0s;
          -moz-transition: all .5s ease-out 0s;
          -ms-transition: all .5s ease-out 0s;
          -o-transition: all .5s ease-out 0s;
          transition: all .5s ease-out 0s;
          position: relative;
          &:hover {
              color: var(--primary-one);
              &:after{
                  opacity: 1;
                  width: 100%;
              }
          }
          &::after{
              content: '';
              position: absolute;
              bottom: 25px;
              left:0;
              width: 0%;
              height: 2px;
              border-radius: 30px;
              display: block;
              background: linear-gradient(90deg, var(--primary-color1),transparent);
              opacity: 0;
              transition: all .5s ease-out 0s;
          }
      }

      a.active {
          color: var(--primary-one);
      }

      i {
          width: 30px;
          font-size: 14px;
          text-align: center;
          color: var(--text-primary);
          font-style: normal;
          position: absolute;
          right: -8px;
          top: 31px;
          z-index: 999;
          cursor: pointer;
          display: none;
          opacity: 0;

          @include lg-down-device(){
            color: var(--text-primary);
            opacity: 1;
          }
      }
  }
  &.style-white{
    li{
      a{
        color: var(--white);
      }
      &::after{
        background-color: var(--white);
    }
    }
  }
}

/*=======================================
 12 .Banner-section
=======================================*/
.banner-section{
  position: relative;
  @include img-adjust();
  height: 100vh;
  position: relative;
  background-position: top center;
  z-index: 1;
  // overflow: hidden;
  @include lg-device(){
    height: 700px;
  }
  @include md-device(){
    height: 600px;
    overflow: hidden;
  }
  @include md-down-device(){
    overflow: hidden;
    height: 550px;
  }
  .swiper-slide{
    position: relative;
    img{
      max-height: auto;
    }
  }
}
.swiper-pagination-bullets.swiper-pagination-horizontal {
  bottom: 0px;
  left: 59%;
  width: 100%;
  max-width: 450px;
  background-color: red;
  z-index: 3;
  position: absolute;
  height: 200px;
  padding: 70px 35px;
  @include lg-down-device(){
    height: 100px;
    padding: 25px 35px;
    width: 50%;
    left: 50%;
  }
  &::after{
    content: '';
    position: absolute;
    top: 100%;
    right: 0%;
    width: 100%;
    max-width: 450px;
    height: 270px;
    background-color: red;
    display: block;
    z-index: 9;
  }

  .swiper-pagination-bullet{
    width: 16px;
    height: 16px;
    border-radius: 0px;
    background: #fff;
    opacity: 1;
    border: 1px solid #fff;
  }
  .swiper-pagination-bullet-active{
    @extend .swiper-pagination-bullet;
    background-color: transparent;
  }
}
.banner-slider-content{
  // position: absolute;
  // left: 50%;
  // top: 40%;
  // transform: translate(-50%,-50%);
  position: relative;
  z-index: 2;
  height: 100vh;
  display: flex;
  justify-content: center;
  align-items: center;
  h1{
    color: var(--white);
    font-size: 100px;
    font-weight: 300;
    @include sm-down-device(){
      font-size: 50px;
    }
    @include sm-device(){
      font-size: 55px;
    }
    @include md-device(){
      font-size: 60px;
    }
    @include lg-device(){
      font-size: 70px;
    }
    @include xl-device(){
      font-size: 85px;
    }
  }
}
.banner-content{
  &.style-dark{
    h1{
      color: var(--text-primary);
    }
  }
  h1{
    color: var(--white);
    font-size: 100px;
    font-weight: 300;
    @include sm-down-device(){
      font-size: 50px;
    }
    @include sm-device(){
      font-size: 55px;
    }
    @include md-device(){
      font-size: 60px;
    }
    @include lg-device(){
      font-size: 70px;
    }
    @include xl-device(){
      font-size: 85px;
    }
  }
}

.right-box{
  position: relative;
  height: 135px;
  width: 100%;
  border: 1px solid var(--border);
  position: relative;
  z-index: 1;
  @include lg-down-device(){
    display: none;
    visibility: hidden;
  }
  &::after{
    content: '';
    position: absolute;
    left: 60px;
    bottom: 60px;
    width: 100%;
    background-color: var(--primary-yellow);
    height: 270px;
    @include lg-device(){
      left: 25px;
    }
    @include xl-device(){
      left: 25px;
    }
  }
}
.right-box-two{
  position: relative;
  height: 470px;
  background-color: var(--primary-yellow);
  width: 100%;
  max-width: 570px;
  position: relative;
  z-index: 1;
  transform: translate(60px,-200px);
  @include lg-device(){
    transform: translate(20px,-150px);
  }
  @include lg-down-device(){
    transform: translate(0px,0px);
    margin-top: 50px;
    height: 360px;
  }
  &.style-blue{
    height: 270px;
    background-color: var(--primary-blue);
  }
}

.banner-video-section{
  position: relative;
  .scroll-down{
    position: absolute;
    left: 50%;
    transform: translateX(-50%);
    bottom: 80px;
    z-index: 2;
    animation: jump 3s linear infinite alternate;
    @include sm-down-device(){
      bottom: 30px;
      max-width: 25px;
    }
    &:hover{
      animation-play-state: paused;
    }

    @keyframes jump {
      0%{
        transform: translate(-50%,0);
      }
      25%{
        transform: translate(-50%,10px);
      }
      50%{
        transform: translate(-50%,0);
      }
      75%{
        transform: translate(-50%,-10px);
      }
      100%{
        transform: translate(-50%,0);
      }
    }
  }
}

/*=======================================
 12 .Content-section
=======================================*/
.content-section{
  .content{
    width: 100%;
    max-width: 500px;
    @include lg-down-device(){
      max-width: 100%;
    }
    h6{
      margin-bottom: 0px;
      font-size: 16px;
      font-weight: 600;
    }
  }
}
/*=======================================
 12 .Team-section
=======================================*/
.team-item{
  // margin-bottom: 20px;
  position: relative;

  &.style-two{
    .team-image{
      overflow: hidden;
      height: 155px;
      @include sm-down-device(){
        height: auto;
      }
      img{
        transition: all 0.5s;
        transform: scale(1);
        width: 100%;
        object-fit: cover;
      }
    }
    .team-content{
      padding-top: 8px;
      h6{
        font-weight: 700;
        a{
          color: inherit;
        }
      }
      span{
        font-weight: 300;
        line-height: 1.4;
      }
    }
  }
  .team-tag{
    position: absolute;
    display: inline-block;
    left: 0;
    top: 0;
    min-width: 30px;
    padding: 5px;
    background-color: var(--text-primary);
    color: var(--white);
    font-size: 12px;
    font-weight: 700;
    text-align: center;
    z-index: 1;
  }
  .team-image{
    overflow: hidden;
    @include sm-down-device(){
      height: auto;
    }
    img{
      transition: all 0.5s;
      transform: scale(1);
      width: 100%;
      object-fit: cover;
      height: 155px;
      
      @include sm-down-device(){
        height: auto;
      }

    }
  }
  &:hover{
    .team-image{
      img{
        transform: scale(1.1);
      }
    }
  }
  .team-content{
    padding-top: 8px;
    h6{
      font-size: 14px;
      color: var(--text-primary);
      line-height: 1.2;
      margin-bottom: 3px;
      @include md-device(){
        font-size: 12px;
      }
      a{
        color: inherit;
      }
    }
    span{
      display: block;
      font-size: 9px;
      color: var(--text-primary);
      text-transform: uppercase;
      @include md-down-device(){
        font-size: 9px;
      }
    }
  }
  &.team-sidebar{
    width: 100%;
    max-width: 260px;
  }
}

/*=======================================
 13 .sector-section
=======================================*/
.sector-section-v{
  position: relative;
  @include sm-down-device(){
    padding-bottom: 0px;
  }
}
.sector-item{
  background-color: var(--primary-green);
  position: relative;
  padding: 15px;
  text-align: left;
  min-height: 270px;
  transition: 0.5s;

  @include lg-device(){
    min-height: 290px;
  }

  @include sm-down-device(){
    padding: 10px;
    min-height: 230px;
  }
  .arrow-btn{
    position: absolute;
    left: 20px;
    bottom: 20px;
    z-index: 2;
    transition: 0.4s ease;
    @include sm-down-device(){
      max-width: 25px;
    }
    &:hover{
      transform: rotate(-45deg);
    }
  }
  &:hover{
    background-color: #3AAA35;
  }
  .title{
    font-size: 16px;
    font-weight: 700;
    color: var(--white);
    margin-bottom: 0px;
    padding-right: 10%;
    @include sm-down-device(){
      font-size: 13px;
    }
  }
  p{
    font-size: 15px;
    font-weight: 300;
    color: var(--white);
    margin-bottom: 50px;
    @include sm-down-device(){
      font-size: 13px;
    }
  }
  &.style-yellow{
    background-color: var(--primary-yellow);
    &:hover{
      background-color: #000;
    }
  }
  &.style-blue{
    background-color: var(--primary-blue);
    &:hover{
      background-color: #000;
    }
  }
}
.sector-item-two{
  background-color: var(--primary-green);
  position: relative;
  padding: 15px;
  text-align: left;
  min-height: 270px;
  transition: 0.5s;

  @include lg-device(){
    min-height: 290px;
  }

  @include sm-down-device(){
    padding: 10px;
    min-height: 230px;
  }
  .arrow-btn{
    position: absolute;
    left: 20px;
    bottom: 20px;
    z-index: 2;
    transition: 0.4s ease;
    @include sm-down-device(){
      max-width: 25px;
    }
    &:hover{
      transform: rotate(-45deg);
    }
  }
  &:hover{
    background-color: #3AAA35;
  }
  .title{
    font-size: 15px;
    font-weight: 700;
    color: var(--white);
    margin-bottom: 0px;
    padding-right: 10%;
    @include sm-down-device(){
      font-size: 13px;
    }
  }
  p{
    font-size: 15px;
    font-weight: 300;
    color: var(--white);
    margin-bottom: 20px;
    @include sm-down-device(){
      font-size: 13px;
    }
  }
  span.date{
    font-size: 13px;
    text-transform: uppercase;
    font-weight: 600;
    color: var(--white);
    display: inline-block;
    position: relative;
    padding-top: 20px;
    margin-bottom: 60px;
    &::before{
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      width: 90px;
      height: 1px;
      background-color: var(--white);
      display: block;
    }
  }
  &.style-yellow{
    background-color: var(--primary-yellow);
    &:hover{
      background-color: #000;
    }
  }
  &.style-blue{
    background-color: var(--primary-blue);
    &:hover{
      background-color: #000;
    }
  }
}
/*=======================================
 13 .Service-section
=======================================*/
.service-section-1{
  overflow: hidden;
  position: relative;
  z-index: 1;
  &::before{
    content: '';
    position: absolute;
    top: 30px;
    right: 100px;
    width: 100%;
    max-width: 730px;
    height: 340px;
    background-color: var(--primary-blue);
    display: block;
    z-index: -1;

  }
}
.service-item{
  position: relative;
  width: 100%;
  max-width: 320px;
  &:hover{
    .image{
      img{
        transform: scale(1.2);
      }
    }
  }
.image{
  margin-bottom: 15px;
  overflow: hidden;
  img{
    transition: 0.62s;
    height: 260px;
    object-fit: cover;
    width: 100%;
    @include xl-device(){
      height: 250px;
    }
    @include lg-device(){
      height: 240px;
    }
    @include md-device(){
      height: 220px;
    }
    @include md-down-device(){
      height: 160px;
    }
  }
}
  .content{
    text-align: left;
    min-height: 100px;
    h6{
      margin-bottom: 0px;
      a{
        font-size: 16px;
        color: var(--primary-red);
        text-transform: uppercase;
        font-weight: 700;
        @include md-down-device(){
          font-size: 14px;
        }
      }
    }
    p{
      font-size: 15px;
      font-weight: 300;
      color: var(--text-primary);
      margin-bottom: 20px;
      @include md-down-device(){
        font-size: 14px;
      }
    }
   > a{
      display: inline-block;
      font-size: 14px;
      text-transform: uppercase;
      color: var(--text-primary);
      font-weight: 700;
      position: relative;
      padding-top: 12px;
      transition: 0.45s ease;
      @include md-down-device(){
        font-size: 12px;
      }
      &:hover{
        letter-spacing: 1px;
        color: var(--primary-red);
      }
      &::before{
        content: '';
        position: absolute;
        left: 0;
        top: 0;
        width: 50px;
        height: 1px;
        background-color: var(--text-primary);
      }
      i{
        font-size: 12px;
        font-weight: 600;
        display: inline-block;
        margin-right: 5px;
      }
    }
  }
}
.service-details-wrap{
  background-color: var(--primary-blue);
  padding: 60px 100px;
  @include md-down-device(){
    padding: 20px 15px;
  }
  h6{
    color: var(--white);
    margin-bottom: 0px;
  }
  P{
    color: var(--white);
  }
}
.service-details-section{
  padding-right: 80px;
  @include lg-down-device(){
    padding-right: unset;
  }
}
/*=======================================
 13 .insight-section
=======================================*/
.bg-light-box{
  position: relative;
  z-index: 1;
  &::before{
    content: "";
    position: absolute;
    bottom: -100px;
    right: 0px;
    width: 75%;
    height: 370px;
    background-color: var(--primary-yellow-light2);
    display: block;
    z-index: -1;
  }
}
.insight-item{
  position: relative;
  width: 100%;
  max-width: 320px;
  &.style-blue{
    .content{
      h6{
        a{
          color: var(--primary-blue);
        }
      }
      > a{
        &:hover{
          color: var(--primary-blue);
        }
      }
    }
  }
  &.style-green{
    .content{
      h6{
        a{
          color: var(--primary-green-dark);
        }
      }
      > a{
        &:hover{
          color: var(--primary-green-dark);
        }
      }
    }
  }
  &.style-yellow{
    .content{
      h6{
        a{
          color: var(--primary-yellow);
        }
      }
      > a{
        &:hover{
          color: var(--primary-yellow);
        }
      }
    }
  }
  &:hover{
    .image{
      img{
        transform: scale(1.2);
      }
    }
  }
.image{
  margin-bottom: 15px;
  overflow: hidden;
  img{
    transition: 0.62s;
    height: 210px;
    object-fit: cover;
    @include lg-device(){
      height: 220px;
    }
    @include sm-down-device(){
      height: 140px;
    }
  }
}
  .content{
    text-align: left;
    padding-right: 40px;
    @include md-down-device(){
      padding-right: 0px;
    }
    h6{
      margin-bottom: 5px;
      a{
        font-size: 16px;
        color: var(--primary-red);
        text-transform: uppercase;
        font-weight: 700;
        @include md-down-device(){
          font-size: 14px;
        }
      }
    }
    p{
      font-size: 15px;
      font-weight: 300;
      color: var(--text-primary);
      margin-bottom: 20px;
      @include md-down-device(){
        font-style: 14px;
      }
    }
   > a{
      display: inline-block;
      font-size: 14px;
      text-transform: uppercase;
      color: var(--text-primary);
      font-weight: 700;
      position: relative;
      padding-top: 12px;
      transition: 0.45s ease;
      @include md-down-device(){
        font-size: 12px;
      }
      &:hover{
        letter-spacing: 1px;
        color: var(--primary-red);
      }
      &::before{
        content: '';
        position: absolute;
        left: 0;
        top: 0;
        width: 50px;
        height: 1px;
        background-color: var(--text-primary);
      }
    }
  }
}

/*=======================================
 13 Blog-section
=======================================*/
.blog-item{
  border: 1px solid var(--border);

  &:hover{
    .image{
      img{
        transform: scale(1.2);
      }
    }
  }
  .content{
    padding: 15px;
    background-color: var(--white);
    @include md-down-device(){
      padding: 15px 10px;
    }
    h5{
      a{
        color: var(--primary-red);
        font-size: 17px;
        font-weight: 300;
        @include md-down-device(){
          font-size: 15px;
        }
      }
    }
    P{
      font-size: 14px;
    }
  }
  header{
    span{
      display: block;
      font-weight: 400;
      &:first-child{
        font-size: 13px;
        color: var(--primary-red);
      }
      &:last-child{
        font-size: 12px;
        color: var(--text-primary);
      }
    }
  }
  ul.blog-icon-list{
    @include listunstyle();
    display: flex;
    flex-direction: row;
    justify-content: flex-end;
    gap: 5px;
    position: relative;
    z-index: 1;
    padding-right: 5px;
    margin-bottom: 5px;

    &::after{
      content: '';
      position: absolute;
      top: 15px;
      right: 0;
      display: block;
      width: 100%;
      height: 1px;
      background-color: var(--border);
      z-index: -1;
      @include md-down-device(){
        top: 13px;
      }
    }
    li{
      width: 30px;
      height: 30px;
      line-height: 25px;
      border: 1px solid  var(--border);
      border-radius: 50%;
      text-align: center;
      background-color: var(--white);
      transition: 0.4s;

      @include md-down-device(){
        width: 25px;
        height: 25px;
        line-height: 20px;
      }

      &:hover{
        border: 1px solid var(--primary-red);
        background-color: var(--primary-red);
        i{
          color: var(--white);
        }
      }
      a{
        i{
          font-size: 21px;
          transition: 0.4s;
          color: var(--text-primary);
          @include md-down-device(){
            font-size: 16px;
          }
        }
      }
    }
  }
  .image{
    overflow: hidden;
    img{
      transition: 0.62s;
      height: 270px;
      object-fit: cover;
      @include lg-device(){
        height: 220px;
      }
      @include sm-down-device(){
        height: 140px;
      }
    }
  }
}

/*=======================================
 13 .software-section
=======================================*/
.software-section{
  padding-left: 7%;
  padding-right: 7%;

  @include lg-down-device(){
    padding-left: 0;
    padding-right:0;
  }

  // .blue-box{
  //   position: relative;
  //   width: 100%;
  //   max-width: 640px;
  //   height: 360px;
  //   background-color: var(--primary-blue);
  //   display: block;
  //   margin-left: auto;
  //   margin-right: auto;
  // }
}
.software-content{
  width: 100%;
  max-width: 520px;
  margin-left: 0;
  margin-right: auto;
}
/*=======================================
 13 .Footer-section
=======================================*/
footer{
  background-color: var(--text-primary);
  padding: 20px 10px;
}
.footer-list{
  @include listunstyle();
  li{
    transition: 0.4s ease;
    &:hover{
      a{
        color: var(--primary-red);
        padding-left: 3px;
        opacity: 1;
      }
    }
  }
  a{
    font-size: 12px;
    font-weight: 300;
    color: #dddddd;
    transition: 0.4s;
  }
  .footer-social{
    margin-top: 15px;
    &:hover{
      i{
        background-color: var(--primary-blue);
        color: var(--white);
      }
    }
    i{
      font-size: 18px;
      width: 25px;
      height: 25px;
      line-height: 25px;
      border-radius: 50%;
      background-color: var(--white);
      color: var(--text-primary);
      text-align: center;
    }
  }
}
.footer-address{
  h6{
    font-size: 12px;
    font-weight: 700;
    color: var(--white);
    margin-bottom: 3px;
  }
  p{
    margin-bottom: 0;
    font-size: 12px;
    font-weight: 300;
    color: #dddddd;
    transition: 0.4s;

  }
}
.contact-list{
  @include listunstyle();
  margin-top: 15px;
  li{
    a{
      font-size: 12px;
      font-weight: 300;
      color: var(--white);
      transition: 0.4s;
    }
  }
}
.footer-col-wrap{
  display: grid;
  grid-template-columns: repeat(8, 1fr);
  grid-gap: 20px;

  @include lg-device(){
    grid-template-columns: repeat(4, 1fr);
  }
  @include md-device(){
    grid-template-columns: repeat(4, 1fr);
  }
  @include sm-device(){
    grid-template-columns: repeat(3, 1fr);
  }
  @include sm-down-device(){
    grid-template-columns: repeat(2, 1fr);
  }
}
.footer-bottom{
  margin-top: 45px;
  font-size: 12px;
  font-weight: 300;
  color: #dddddd;
  p{
    font-size: 11px;
  }
}
.footer-top-design{
  padding-top: 110px;
  margin-top: 30px;

  &.sibling-two{
    .footer-box{
      &::after{
        content: '';
        position: absolute;
        right: -80%;
        bottom: 0;
        width: 100%;
        height: 200px;
        background-color: var(--primary-green-light);
        display: block;
        @include lg-device(){
          right: -70%;
        }
      }
    }  
  }
  .footer-box{
    width: 100%;
    max-width: 480px;
    height: 70px;
    background-color: var(--primary-red);
    display: block;
    margin-left: 100px;
    margin-right: auto;
    position: relative;
    @include sm-down-device(){
      max-width: 150px;
      height: 150px;
      margin-left: 0px;
    }
    &::before{
      content: '';
      position: absolute;
      left: 50%;
      bottom: 0;
      width: 100%;
      max-width: 480px;
      height: 160px;
      background-color: transparent;
      border: 1px solid var(--border);
      display: block;
    }
    &::after{
      content: '';
      position: absolute;
      right: -80%;
      bottom: 0;
      width: 100%;
      max-width: 480px;
      height: 200px;
      background-color: var(--primary-red-light);
      display: block;
      @include lg-device(){
        right: -70%;
      }
    }
  }
}
.footer-top-design-two{
  overflow: hidden;
  padding-top: 100px;
  .footer-box{
    width: 100%;
    max-width: 640px;
    height: 180px;
    background-color: var(--primary-red);
    display: block;
    margin-left: 100px;
    margin-right: auto;
    position: relative;
    z-index: 1;
    @include sm-down-device(){
      max-width: 150px;
      height: 150px;
      margin-left: 0px;
    }
    @include sm-device(){
      max-width: 400px;
      margin-left: 0px;
    }
    @include md-device(){
      max-width: 440px;
      margin-left: 0px;
    }
    @include lg-device(){
      max-width: 520px;
      margin-left: 0px;
    }
    &::before{
      content: '';
      position: absolute;
      left: 80%;
      bottom: 0;
      width: 100%;
      max-width: 980px;
      height: 70px;
      background-color: transparent;
      border: 1px solid var(--border);
      display: block;
      z-index: -1;
      @include sm-device(){
        left: 50%;
        max-width: 500px;
      }
    }
  }
}
.footer-top-design-three{
  // overflow: hidden;
  padding-top: 100px;
  margin-top: 100px;
  .footer-box{
    width: 100%;
    max-width: 640px;
    height: 100px;
    background-color: var(--primary-red);
    display: block;
    margin-left: 100px;
    margin-right: auto;
    position: relative;
    z-index: 2;
    @include md-device(){
      max-width: 500px;
    }
    @include sm-device(){
      max-width: 440px;
      height: 100px;
      background-color: var(--primary-red);
      margin-left: 30px;
    }
    @include sm-down-device(){
      max-width: 250px;
      margin-left: 0px;
    }
    
    &::before{
      content: '';
      position: absolute;
      left: 80%;
      bottom: 0;
      width: 100%;
      max-width: 980px;
      height: 280px;
      background-color: transparent;
      border: 1px solid var(--border);
      display: block;
      z-index: 1;
      @include xxl-device(){
        left: 65%;
      }
      @include xl-device(){
        max-width: 530px;
        left: 60%;
      }
      @include lg-device(){
        max-width: 340px;
      }
      @include md-device(){
        left: 45%;
        max-width: 340px;
      }
      @include sm-device(){
        left: 40%;
        bottom: 0;
        width: 100%;
        max-width: 320px;
      }
      @include sm-down-device(){
        max-width: 150px;
        left: 50%;
        height: 200px;
      }
    }
  }
}
.footer-top-design-four{
  position: relative;
  padding-left: 10%;
  padding-right: 10%;
  @include md-down-device(){
    padding-left: 5%;
    padding-right: 5%;
  }
  .main-box{
    width: 100%;
    max-width: 670px;
    height: 280px;
    background-color: var(--primary-red-light);
    &::before{
      content: '';
      position: absolute;
      right: 10%;
      bottom: 0;
      border: 1px solid var(--border);
      width: 100%;
      max-width: 60%;
      height: 190px;
    }
  }
  .small-box{
    width: 37%;
    height: 90px;
    background-color: var(--primary-red);
    position: absolute;
    bottom: 0;
    left: 20%;
    z-index: 2;
  }
}
.footer-top-design-five{
  position: relative;
  padding-left: 10%;
  padding-right: 10%;
  @include md-down-device(){
    padding-left: 5%;
    padding-right: 5%;
  }
  .main-box{
    width: 100%;
    max-width: 670px;
    height: 190px;
    &::before{
      content: '';
      position: absolute;
      right: 10%;
      bottom: 0;
      border: 1px solid var(--border);
      width: 100%;
      max-width: 50%;
      height: 190px;
    }
  }
  .small-box{
    width: 37%;
    height: 90px;
    background-color: var(--primary-red);
    position: absolute;
    bottom: 0;
    left: 20%;
    z-index: 2;
  }
}
.footer-top-design-six{
  .footer-box{
    width: 28%;
    height: 180px;
    display: block;
    margin-left: 27%;
    margin-right: auto;
    position: relative;
    z-index: 1;
    background-color: transparent;
    border-left: 1px solid var(--border);
    border-right: 1px solid var(--border);

    @include lg-down-device(){
      margin-left: 10%;
      width: 50%;
    }
    &::before{
      content: '';
      position: absolute;
      left: 80%;
      bottom: 0;
      width: 100%;
      max-width: 980px;
      height: 70px;
    background-color: var(--primary-red);
      display: block;
      z-index: -1;
      // @include sm-device(){
      //   left: 50%;
      //   max-width: 500px;
      // }
    }
  }
}
.footer-top-design-seven{
  .footer-box{
    width: 28%;
    height: 180px;
    display: block;
    margin-left: 27%;
    margin-right: auto;
    position: relative;
    z-index: 1;
    background-color: transparent;
    border-left: 1px solid var(--border);
    border-right: 1px solid var(--border);

    @include lg-down-device(){
      margin-left: 10%;
      width: 50%;
    }
    &::before{
      content: '';
      position: absolute;
      left: 80%;
      bottom: 0;
      width: 100%;
      max-width: 980px;
      height: 70px;
    background-color: var(--primary-red);
      display: block;
      z-index: -1;
      // @include sm-device(){
      //   left: 50%;
      //   max-width: 500px;
      // }
    }
  }
}
/*=======================================
 13 .Expertise-section
=======================================*/
.expertise-section-1{
  margin-top: 200px;
  @include md-device(){
    margin-top: 70px;
  }
  @include md-down-device(){
    margin-top: 60px;
  }
  .right-box-blue{
    position: relative;
    height: 200px;
    background-color: var(--primary-blue);
    width: 100%;
    max-width: 340px;
    left: 0;
    margin-left: 40px;
    z-index: 1;
    margin-top: -90px;
    @include lg-down-device(){
      margin-top: 110px;
      margin-bottom: 110px;
      margin-left: 0;
      max-width: 240px;
    }
    &::before{
      content: '';
      position: absolute;
      top: 50%;
      right: -100px;
      transform: translateY(-50%);
      width: 100%;
      max-width: 270px;
      height: 340px;
      background-color: var(--primary-blue-light);
      display: block;
      z-index: -1;
      @include lg-device(){
        right: -20px;
      }
      @include lg-down-device(){
        right: -50px;
      }
    }
  }
}
.search-section{
  background-color: var(--primary-yellow-light2);
  padding: 90px 10px;
  @include md-device(){
    padding: 70px 10px;
  }
  @include md-down-device(){
    padding: 60px 10px;
  }
  &.style-blue{
    background-color: var(--primary-blue-light);
  }
  &.style-green{
    background-color: var(--primary-green-dark-light);
  }
  &.with-box{
    position: relative;
    &::before{
      content: '';
      position: absolute;
      top: -180px;
      left: 27%;
      height: 180px;
      width: 28%;
      border: 1px solid var(--border);
      border-bottom: unset;
      @include lg-down-device(){
        left: 10%;
        width: 50%;
      }
    }
  }
}
.search-block{
  background-color: var(--white);
  padding: 20px 30px;
  display: flex;
  justify-content: flex-start;
  align-items: center;
  @include md-down-device(){
    padding: 10px 15px;
  }

  button{
    background-color: transparent;
    &:hover{
      i{
        color: var(--primary-red);
      }
    }
    i{
      font-size: 25px;
      color: var(--text-primary);
      margin-right: 10px;
      transition:  0.4s;
      @include md-down-device(){
        font-size: 20px;
      }
    }
  }
  input{
    outline: none;
    border: none;
    background-color: transparent;
    width: 100%;
    &::placeholder{
      font-size: 10px;
      text-transform: uppercase;
      font-weight: 800;
      color: var(--text-primary);
    }
  }
}
.box-with-border{
  width: 100%;
  max-width: 360px;
  height: 270px;
  background-color: transparent;
  border: 1px solid var(--border);
  position: relative;
  margin-top: 135px;
  @include lg-down-device(){
    margin-top: 65px;
    margin-bottom: 40px;
    margin-left: 30px;
    max-width: 260px;
  }
  &::after{
    content: '';
    position: absolute;
    left: -150px;
    top: -90px;
    width: 100%;
    max-width: 340px;
    height: 270px;
    background-color: var(--primary-green-light);
    @include xl-device(){
      left: -90px;
    }
    @include lg-device(){
      left: -50px;
      top: -50px;
    }
    @include lg-down-device(){
      left: -30px;
      top: -90px;
      margin-top: 65px;
      margin-bottom: 40px;
    }
  }
}

.focus-list-area{
  background-color: var(--primary-yellow-light2);
  padding: 40px 45px;
  width: 100%;
  max-width: 750px;
  margin-left: 0;
  margin-right: auto;
  margin-top: 100px;
  @include md-down-device(){
    padding: 25px 20px;
    margin-top: 30px;
  }
  @include md-device(){
    padding: 25px 20px;
    margin-top: 30px;

  }
  @include lg-device(){
    padding: 35px 30px;
  }
  @include xl-device(){
    padding: 35px 30px;
  }
  h6{
    font-weight: 700;
    margin-bottom: 5px;
  }
  ul{
    @include listunstyle();
    li{
      position: relative;
      margin-bottom: 3px;
      &:hover{
        &::before{
          opacity: 1;
        }
      }
      &:last-child{
        margin-bottom: 0px;
      }
      &::before{
        content: '\F285';
        font-family: 'Bootstrap-icons';
        position: absolute;
        left: -15px;
        top: 5px;
        color: var(--text-primary);
        font-weight: 700;
        font-size: 12px;
        opacity: 0;
        transition: 0.35focus-list-areas ease;
      }
    }
    a{
      color: var(--text-primary);
      font-size: 15px;
      font-weight: 500;
    }
  }
}

.box-design-one{
  width: 100%;
  max-width: 860px;
  background-color: var(--white);
  border: 1px solid var(--border);
  height: 190px;
  margin-left: auto;
  margin-right: auto;
  position: relative;
  z-index: 2;
  display: block;
  @include lg-down-device(){
    margin-left: auto;
    margin-right: auto;
    max-width: 80%;
    height: 80px;
    margin-top: 40px;
    margin-bottom: 55px;
  }

  &::before{
    content: '';
    position: absolute;
    left: -25%;
    top: -320px;
    width: 100%;
    max-width: 480px;
    height: 380px;
    background-color: var(--primary-green-dark);
    display: block;

    @include xxl-device(){
      left: -15%;
    }
    @include xl-device(){
      left: -15%;
    }
    @include lg-device(){
      left: -7%;
      max-width: 400px;
    }
    @include lg-down-device(){
      left: -30px;
      top: -90px;
      width: 100%;
      max-width: 120px;
      height: 120px;
    }
  }
  &::after{
    content: '';
    position: absolute;
    right: -30%;
    bottom: -390px;
    width: 100%;
    max-width: 580px;
    height: 480px;
    background-color: var(--primary-green-light);
    display: block;
    @include xxl-device(){
      right: -15%;
    }
    @include xl-device(){
      right: -15%;
    }
    @include lg-device(){
      right: -7%;
      max-width: 400px;
    }
    @include lg-down-device(){
      right: -20px;
      bottom: -55px;
      width: 100%;
      max-width: 130px;
      height: 150px;
    }
  }
}
.box-design-two{
  width: 380px;
  height: 280px;
  border: 1px solid var(--border);
  background-color: transparent;
  position: relative;
  margin-top: 190px;
  @include lg-device(){
    margin-top: 140px;
  }
  @include xl-device(){
    margin-top: 150px;
  }
  @include md-device(){
    margin-left: 44px;
    margin-bottom: 50px;
  }
  @include sm-device(){
    margin-left: 44px;
    margin-bottom: 50px;

  }
  
  @include sm-down-device(){
    width: 270px;
    height: 230px;
    margin-top: 100px;
    margin-left: 10px;
    margin-bottom: 50px;
  }
  &::after{
    content: '';
    position: absolute;
    width: 100%;
    width: 400px;
    height: 380px;
    background-color: var(--primary-yellow);
    bottom: 90px;
    right: 90px;
    @include xxl-device(){
      width: 330px;
    }
    @include xl-device(){
      width: 360px;
      right: 50px;
      bottom: 50px;
    }
    @include lg-device(){
      width: 350px;
      right: 40px;
      bottom: 40px;
    }
    @include md-device(){
      width: 380px;
      right: 40px;
      bottom: 40px;
    }
    @include sm-device(){
      width: 380px;
      right: 40px;
      bottom: 40px;
    }
    @include sm-down-device(){
      width: 100%;
      width: 250px;
      height: 260px;
      bottom: 30px;
      right: 30px;
    }
  }
}
.box-design-three{
  width: 100%;
  max-width: 660px;
  height: 460px;
  background-color: var(--primary-blue-light);
  position: absolute;
  top: -250px;
  right: 7%;
  z-index: -1;
  @include md-down-device(){
    max-width: 300px;
    right: 3%;
  }
}
.box-design-four{
  width: 100%;
  max-width: 660px;
  height: 270px;
  background-color: var(--primary-red-light);
  position: absolute;
  top: 0px;
  left: 7%;
  z-index: -1;
  @include md-down-device(){
    max-width: 300px;
  }
}
.box-design-five{
  width: 100%;
  max-width: 660px;
  height: 270px;
  background-color: var(--primary-blue-light);
  position: absolute;
  top: 0px;
  left: 7%;
  z-index: -1;
  height: 75%;
  @include md-down-device(){
    max-width: 300px;
  }
}
.box-design-six{
  width: 100%;
  max-width: 540px;
  height: 370px;
  background-color: var(--primary-yellow-light2);
  position: absolute;
  top: 45px;
  right: 7%;
  z-index: -1;
  height: 75%;
  @include md-down-device(){
    max-width: 300px;
  }
}
.box-design-seven{
  width: 100%;
  max-width: 960px;
  height: 370px;
  background-color: var(--primary-blue-light);
  position: absolute;
  top: 0px;
  right: 7%;
  z-index: -1;
  @include md-down-device(){
    max-width: 300px;
  }
}
.box-design-eight{
  max-width: 90%;
  width: 100%;
  height: 60%;
  background-color: var(--primary-green-light);
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  left: 0%;
  z-index: -1;
  @include md-down-device(){
    max-width: 300px;
  }
}
.box-design-nine{
  max-width: 60%;
  width: 100%;
  height: 100%;
  background-color: var(--primary-blue-light);
  position: absolute; 
  top: 0;
  bottom: 0;
  right: 0%;
  z-index: -1;
  display: block;
  @include md-down-device(){
    max-width: 300px;
  }
}
.box-design-ten{
  width: 100%;
  max-width: 50%;
  height: 270px;
  background-color: var(--primary-yellow-light2);
  position: absolute; 
  bottom: 0;
  right: 10%;
  z-index: -1;
  display: block;
  @include md-down-device(){
    max-width: 300px;
  }
}
.box-design-11{
  height: 180px;
  width: 100%;
  max-width: 470px;
  background-color: var(--white);
  border: 1px solid var(--border);
  position: relative;
  z-index: 1;
  margin-left: 70px;
  margin-bottom: 90px;
  @include lg-device(){
    margin-left: 0px;
  }
  @include md-device(){
    margin-left: 0px;
    margin-top: 150px;
  }
  @include sm-device(){
    margin-left: 0px;
    margin-top: 150px;
  }
  @include sm-down-device(){
    margin-left: 0px;
    margin-top: 150px;
  }
  &::before{
    content: '';
    position: absolute;
    left: 90px;
    bottom: 90px;
    width: 100%;
    max-width: 470px;
    height: 370px;
    background-color: var(--primary-green-dark);
    display: block;
    @include md-device(){
      left: 50px;
      bottom: 50px;
      height: 250px;
    }
    @include sm-device(){
      left: 50px;
      bottom: 50px;
      height: 250px;
    }
    @include sm-down-device(){
      left: 10px;
      bottom: 10px;
      height: 250px;
      max-width: 100%; 
    }
    
  }
}
.box-design-12{
  width: 100%;
  max-width: 60%;
  height: 380px;
  background-color: var(--primary-yellow-light2);
  position: absolute;
  top: -180px;
  left: 0;
  z-index: -1;
}
.box-design-13{
  width: 100%;
  max-width: 50%;
  height: 270px;
  background-color: var(--primary-green-dark-light);
  position: absolute;
  top: 0px;
  right: 5%;
  z-index: -1;
}
.box-design-14{
  position: absolute;
  top: 0;
  right: 5%;
  width: 100%;
  max-width: 370px;
  height: 270px;
  border: 1px solid var(--border);
  background-color: var(--white);
  @include lg-device(){
    max-width: 270px;
  }
  @include md-device(){
    max-width: 220px;
  }
  @include md-down-device(){
    display: none;
    visibility: hidden;
  }
  &::after{
    content: '';
    position: absolute;
    bottom: 90px;
    right: 90px;
    background-color: var(--primary-blue-light);
    height: 270px;
    width: 150%;
    @include xxl-device(){
      width: 130%;
    }
    @include xl-device(){
      width: 130%;
    }
    @include xl-down-device(){
      width: 100%;
      right: 60px;
    }
    @include md-device(){
      bottom: 60px;
    }
  }
}
.box-design-15{
  height: 180px;
  width: 100%;
  max-width: 470px;
  background-color: var(--white);
  border: 1px solid var(--border);
  position: relative;
  z-index: 1;
  margin-right: 70px;
  margin-left: auto;
  margin-bottom: 90px;
  @include lg-device(){
    margin-left: auto;
    margin-right: 50px;
    max-width: 400px;

  }
  @include md-device(){
    margin-left: 0px;
    margin-top: 150px;
  }
  @include sm-device(){
    margin-left: 0px;
    margin-top: 150px;
  }
  @include sm-down-device(){
    margin-left: 0px;
    margin-top: 150px;
  }
  &::before{
    content: '';
    position: absolute;
    right: 90px;
    bottom: 90px;
    width: 100%;
    max-width: 470px;
    height: 370px;
    background-color: var(--primary-yellow);
    display: block;
    @include md-device(){
      left: 50px;
      bottom: 50px;
      height: 250px;
    }
    @include sm-device(){
      left: 50px;
      bottom: 50px;
      height: 250px;
    }
    @include sm-down-device(){
      left: 10px;
      bottom: 30px;
      height: 250px;
      max-width: 100%; 
    }
    
  }
}
.box-design-16{
  width: 100%;
  max-width: 470px;
  height: 380px;
  border: 1px solid var(--border);
  margin-left: 180px;
  transform: translateY(100px);
  @include xl-device(){
    max-width: 430px;
  }
  @include lg-device(){
    max-width: 390px;
  }
  @include lg-down-device(){
    display: none;
    visibility: hidden;
  }
}
/*=======================================
 13 .Guide-section
=======================================*/
.guide-single{
  border: 1px solid var(--border);
  overflow: hidden;
  img{
    transition: 0.65s ease;
  }
  &:hover{
    img{
      transform: scale(1.2);
    }
  }
}

.company-vdo{
  background-size: cover;
  background-repeat: no-repeat;
  background-position: center;
  width: 100%;
  max-width: 100%;
  min-height: 100vh;
  margin-left: auto;
  margin-right: auto;
  position: relative;
  z-index: 1;
  display: flex;
  justify-content: center;
  align-items: center;
 flex-direction: column;
 text-align: center;

  @include sm-down-device(){
    min-height: 450px;
  }

  @include sm-device(){
    height: 450px;
  }
  @include md-device(){
    height: 480px;
  }

  @include lg-device(){
    height: 630px;
  }

  .play-icon{
    width: 65px;
    height: 45px;
    display: flex;
    justify-content: center;
    align-items: center;
  img{
    z-index: 99;
  }
  }
  h3{
    color: var(--white);
    font-size: 70px;
    font-weight: 300;
    margin-bottom: 40px;

    @include sm-down-device(){
      font-size: 40px;
    }
    @include sm-device(){
      font-size: 50px;
    }
    @include md-device(){
      font-size: 55px;
    }
    @include lg-device(){
      font-size: 60px;
    }
    @include xl-device(){
      font-size: 65px;
    }
  }
}

/*================================================
2. Contact Css
=================================================*/
.contact-form{
  .form-inner{
    margin-bottom: 30px;
    p{
      margin-bottom: 0px;
      font-size: 10px;
      line-height: 1.3;
    }
  }
  label{
    font-size: 11px;
    color: var(--text-primary);
    font-weight: 700;
    margin-bottom: 5px;
    padding-left: 15px;
  }
  input{
    width: 100%;
    background-color: var(--white);
    outline: none;
    border: 1px solid transparent;
    padding: 12px 15px;
    line-height: 1;
    transition: 0.4s ease;
    font-size: 12px;
    &:focus{
      border: 1px solid var(--primary-green-dark);
    }
    &::placeholder{
      font-size: 10px;
      color: var(--text-secondary);
      line-height: 1
    }
  }
  textarea{
    @extend input;
    min-height: 144px;
  }
  .submit-btn{
    font-size: 11px;
    font-weight: 600;
    padding: 8px 15px;
  }
}

.location-card-section{
  margin-top: -100px;
  @include lg-down-device(){
    margin-top: 0px;
  }
}
/*================================================
2. Faq Css
=================================================*/
.faq-area{
  background-color: var(--primary-blue);
  padding: 40px 70px;
  width: 100%;
  max-width: 650px;
  position: relative;
  margin-top: 180px;
  @include lg-down-device(){
    max-width: 100%;
  }
  @include lg-device(){
    margin-top: 100px;
  }
  @include md-device(){
    margin-top: 70px;
    padding: 40px;
  }
  @include md-down-device(){
    margin-top: 60px;
    padding: 20px;
  }
  &::before{
    content: '';
    position: absolute;
    right: -90px;
    top: -90px;
    display: block;
    width: 100%;
    max-width: 560px;
    background-color: var(--primary-blue-light);
    height: 100%;
    @include xxl-device(){
      right: -50px;
    }
    @include xl-device(){
      right: 0px;
    }
    @include lg-device(){
      right: 0px;
    }
    @include lg-down-device(){
      display: none;
      visibility: hidden;
    }
  }
}
.faq-wrap {
  .faq-item {
    border: none;
    &:last-child {
      margin-bottom: 0;
    }
  }

  .accordion-button {
    font-weight: 600;
    font-size: 16px;
    border-radius: 0px;
    color: var(--white);
    cursor: pointer;
    transition: 0.4s ease-in-out;
    padding: 8px 0px;
    padding-right: 60px;
    margin-bottom: 0px;
    line-height: 1.4;
    background-color: var(--primary-blue);

    @include md-down-device(){
      padding-left: 22px;
      font-size: 14px;
    }
    @include lg-device(){
      padding-left: 22px;
    }

    &:hover{
      padding-left: 22px;
      @include lg-down-device(){
        padding-left: 22px;
      }
    }

    &:focus {
      color: var(--primary-blue);
      z-index: unset;
      border-color: unset;
      outline: 0;
    }

    &::after {
      flex-shrink: 0;
      margin-left: auto;
      background: var(--primary-blue);
      font-family: bootstrap-icons !important;
      position: absolute;
      left: -18px;
      top: 11px;
      content: "\f4fe";
      transition: unset;
      font-size: 20px;
      width: 15px;
      height: 15px;
      display: flex;
      align-items: center;
      justify-content: center;
      color: var(--white);

      @include lg-down-device(){
        left: 0;
        top: 12px;
      }
    }

    &:not(.collapsed)::after {
      transform: unset;
      font-family: bootstrap-icons !important;
      content: "\f2ea";
      color: var(--white);
      background-image: none;
    }
  }

  .accordion-button:not(.collapsed) {
    color: var(--white) !important;
    // background-color: rgb(255, 255, 255);
    border-radius: 0px !important;
    box-shadow: none ;
}

  .faq-body {
    font-weight: 400;
    font-size: 14px;
    color: var(--white);
    border-top: none;
    padding: 0px 0px 15px 0px;
    line-height: 1.3;
    text-align: left;
  }
  .accordion-button:not(.collapsed) { 
    border-radius: 5px 5px 0px 0px;
    color: var(--text-primary);
  }
}

/*================================================
2. people-info page
=================================================*/
.experience-block{
  margin-bottom: 20px;
  .experice-text{
    border-top: 1px solid var(--border);
    border-bottom: 1px solid var(--border);
    padding-top: 10px;
    padding-bottom: 15px;
  }
}
.testimonial-block{
  border-bottom: 1px solid var(--border);
  margin-bottom: 20px;

  .testi-single{
    margin-bottom: 15px;
    p{
      font-style: italic;
      margin-bottom: 0;
      font-size: 16px;
    }
    span{
      display: inline-block;
      font-size: 10px;
      font-weight: 700;
      color: var(--text-primary);
      line-height: 1;
    }
  }
}
.publication-block{
  border-bottom: 1px solid var(--border);
}
.publication-text{
  h6{
    font-size: 15px;
    font-weight: 700;
    margin-bottom: 0;
  }
}
.people-info-section{
  padding-left: 8%;

  @include md-down-device(){
    padding-left: 0;
  }
}
.testimonial-card{
  background-color: var(--primary-red);
  padding: 35px 40px;
  width: 100%;
  max-width: 550px;
  margin-left: 120px;
  margin-top: -90px;
  p{
    color: var(--white);
    font-style: italic;
    font-size: 16px;
    font-weight: 300;
  }
}
.testimonial-image2{
  max-width: 320px;
  width: 100%;
  margin-left: auto;  
  margin-right: 40px;
}
.tesitmonial-section{
  margin-top: -90px;
  position: relative;
}
.about-people-card{
  border: 1px solid var(--border);
  padding: 40px 100px;
  margin-top: -400px;
  z-index: 2;
  position: relative;
  width: 100%;
  max-width: 630px;
  margin-bottom: 380px;

  @include xl-device(){
    padding: 40px 70px;
  }
  @include lg-device(){
    padding: 40px 50px;
  }

  @include sm-down-device(){
    padding: 30px 15px;
  }

  .designation{
    h1,h2{
      font-size: 40px;
      margin-bottom: 0;
      color: var(--text-primary);
      font-weight: 300;
      line-height: 1.1;
    }
    h2{
      color: #777;
    }
  }
  .box{
    height: 190px;
    width: 100%;
    max-width: 380px;
    background-color: var(--primary-green);
    margin-top: 30px;
    margin-bottom: 30px;
    margin-left: -190px;
    @include md-device(){
      margin-left: -160px;
    }

    @include sm-down-device(){
      margin-left: -25px;
      max-width: 280px;
    }
  }
  h6{
    font-size: 14px;
    font-weight: 700;
    margin-bottom: 0px;
  }
  ul{
    @include listunstyle();
    margin-bottom: 25px;
    li{
      color: var(--text-secondary);
      font-size: 14px;
      &:hover{
        a{
          color: var(--primary-green-dark);
        }
      }
      a{
        color: inherit;
        text-decoration: none;
        transition: 0.4s;
      }
    }
  }
  .social-link{
    margin-top: 15px;
    &:hover{
      i{
        background-color: var(--primary-blue);
        color: var(--white);
      }
    }
    i{
      font-size: 18px;
      width: 25px;
      height: 25px;
      line-height: 25px;
      border-radius: 50%;
      color: var(--white);
      background-color: var(--text-primary);
      text-align: center;
      transition: 0.4s;
    }
  }
  .quote-box{
    background-color: var(--primary-red);
    padding: 20px;
    min-height: 290px;
    width: 100%;
    max-width: 390px;
    position: absolute;
    bottom: -200px;
    right: -40px;

    @include sm-down-device(){
      right: -10px;
      bottom: -260px;
    }
    p{
      color: var(--white);
      font-size: 20px;
      font-weight: 300;
      line-height: 1.3;
      margin-bottom: 10px;
    }
    span{
      font-size: 12px;
      font-weight: 700;
      color: var(--white);
    }
  }
}
.experience-box{
  background-color: var(--primary-blue);
  padding: 30px 70px;
  color: var(--white);
  width: 100%;
  max-width: 540px;
  display: block;
  position: relative;
  @include lg-down-device(){
    padding: 30px 30px;
    margin-bottom: 50px;
  }
  &::after{
    content: '';
    position: absolute;
    display: block;
    right: -90px;
    bottom: -90px;
    height: 180px;
    width: 270px;
    background-color: var(--white);
    border: 1px solid var(--border);
    z-index: -1;
    @include lg-down-device(){
      display: none;
      visibility: hidden;
    }
  }
  &::before{
    content: '';
    position: absolute;
    display: block;
    left: -90px;
    top: -90px;
    height: 85%;
    width: 100%;
    max-width: 540px;
    background-color: var(--primary-blue-light);
    z-index: -1;
  }

  .title{
    font-size: 18px;
    font-weight: 700;
    color: var(--white);
  }
  .subtitle{
    font-size: 15px;
    font-weight: 600;
    color: var(--white);
    margin-bottom: 0px;
    position: relative;
    &::before{
      content: '\F285';
      font-family: 'Bootstrap-icons';
      position: absolute;
      top: 4px;
      left: -20px;
      font-size: 10px;
      color: var(--white);
      @include lg-down-device(){
        left: -15px;
      }
    }
  }
  ul{
    @include listunstyle();
    margin-bottom: 20px;
    li{
      color: var(--white);
      font-size: 14px;
      font-weight: 300;
    }
  }
}

/*================================================
 Career Section
=================================================*/
.career-card{
  position: relative;
  z-index: 2;
  &:hover{
    .image-wrap{
      img{ 
        transform: scale(1.1);
      }
    }
  }
  &.style-blue{
    .content{
      background-color: var(--primary-blue);
    }
  }
  &.style-yellow{
    .content{
      background-color: var(--primary-yellow);
    }
  }
  &.style-green{
    .content{
      background-color: var(--primary-green-dark);
    }
  }
  .image-wrap{
    overflow: hidden;
    img{
      height: 270px;
      width: 100%;
      object-fit: cover;
      transition: all 0.4s;
    }
  }
  .content{
    padding: 15px;
    h5{
      font-size: 16px;
      color: var(--white);
      margin-bottom: 0px;
    }
    p{
      color: var(--white);
      margin-bottom: 35px;
    }
  }
  
}
.career-card-two{
  position: relative;
  padding: 20px;
  z-index: 2;
  @include md-down-device(){
    padding: 15px;
  }
  &.style-blue{
      background-color: var(--primary-blue);
  }
  &.style-yellow{
      background-color: var(--primary-yellow);
  }
  &.style-green{
      background-color: var(--primary-green-dark);
  }
  .title{
    position: relative;
    display: inline-block;
    padding-bottom: 10px;
    margin-bottom: 10px;
    &::after{
      content: '';
      position: absolute;
      bottom: 0;
      left: 0;
      display: block;
      height: 1px;
      width: 100%;
      background-color: var(--white);
    }
    h5{
      font-size: 16px;
      color: var(--white);
      margin-bottom: 0px;

      @include md-down-device(){
        font-size: 14px;
      }
    }
  }
    p{
      color: var(--white);
      margin-bottom: 60px;
      @include md-down-device(){
        font-size: 13px;
      }
    }
}
.career-card-section{
  position: relative;
  .red-box{
    position: absolute;
    top: -100px;
    right: 5%;
    background-color: var(--primary-red);
    height: 380px;
    width: 100%;
    max-width: 670px;
    z-index: 1;
  }
}
.career-box-right{
  width: 100%;
  max-width: 570px;
  border: 1px solid var(--border);
  height: 100%;
  max-height: 770px;
  margin-top: -170px;
  position: relative;
  &::before{
    content: '';
    position: absolute;
    bottom: 90px;
    left: 20%;
    width: 100%;
    max-width: 570px;
    background-color: var(--primary-green-dark-light);
    height: 380px;
  }
}
.career-right-image{
  width: 100%;
  max-width: 570px;
  margin-top: -180px;
  margin-left: 90px;
  z-index: 1;
  position: relative;
  margin-bottom: 190px;
}
.career-right-image2{
  width: 100%;
  max-width: 280px;
}
.career-right-quote{
  background-color: var(--primary-red);
  padding: 20px;
  min-height: 300px;
  width: 100%;
  max-width: 480px;
  // margin-top: -180px;
  p{
    color: var(--white);
    font-size: 20px;
    font-weight: 300;
    line-height: 1.3;
    margin-bottom: 10px;
  }
  span{
    font-size: 12px;
    font-weight: 700;
    color: var(--white);
  }
}
.career-box-right2{
  width: 100%;
  max-width: 570px;
  background-color: var(--primary-yellow-light2);
  min-height: 570px;
  // position: relative;
  display: block;
  margin-top: -100px;
  margin-left: 100px;
}
.career-right-quote2{
  background-color: var(--primary-red);
  padding: 40px 35px 110px 35px;
  p{
    font-size: 16px;
    font-style: italic;
    color: var(--white);
  }
}

.skill-card{
  background-color: var(--primary-blue);
  padding: 20px;
  min-height: 320px;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  transition: 0.45s;

  @include md-down-device(){
    padding: 10px;
    min-height: 300px;
  }

  &:hover{
    background-color: var(--text-primary);
    .icon{
      transform: rotate(5deg);
      svg{
        fill: var(--white);
      }
      &.communicate{
        svg{
          stroke: var(--white);
        }
      }
    }
  }
  .content{
    h5{
      font-size: 17px;
      font-weight: 600;
      color: var(--white);

      @include md-down-device(){
        font-size: 14px;
      }
    }
    p{
      color: var(--white);
      margin-bottom: 20px;
      font-size: 15px;
      @include md-down-device(){
        font-size: 12px;
      }
    }
  }
  .icon{
    max-width: 90px;
    margin-left: auto;
    margin-right: 0;
    transition: 0.45s;
    transition-delay: 0.3s ;

    &.communicate{
      svg{
        fill: none;
        stroke: var(--text-primary);
        max-width: 90px;
        @include md-down-device(){
          max-width: 45px;
        }
      }
    }
    
    svg{
      fill: var(--text-primary);
      transition: 0.45s;
      max-width: 90px;

      @include md-down-device(){
        max-width: 45px;
      }
    }
  }
}

.content-italic{
  p{
    font-style: italic;
  }
  h6{
    font-style: italic;
  }
}

.testimonial-section{
  padding-left: 90px;
  position: relative;
  padding-top: 180px;
  margin-top: 210px;
  @include lg-device(){
    padding-left: 30px;
  }
  @include md-device(){
    padding-left: calc((100% -	720px)/2);
  }
  @include sm-device(){
    padding-left: calc((100% - 540px)/2);
  }
  @include sm-down-device(){
    padding-left: 0;
  }
  &::before{
    content: '';
    position: absolute;
    right: 15%;
    top: 0;
    width: 100%;
    max-width: 42%;
    height: 71%;
    border: 1px solid var(--border);
    display: block;
    z-index: -2;
  }
  .testi-top-image{
    max-width: 460px;
    position: absolute;
    right: 10%;
    top: -275px;
    @include xxl-device(){
      max-width: 380px;
    }
    @include xl-device(){
      max-width: 290px;
    }
    @include lg-device(){
      max-width: 290px;
      right: 5%;
    }
    @include lg-down-device(){
      top: -120px;
    }
    img{
      width: 100%;
      height: 365px;
      object-fit: cover;
    }
  }
}
.big-card{
  padding: 90px 90px 80px 180px;
  display: flex;
  gap: 50px;
  height: auto;
  width: 100%;
  max-width: 1260px;
  z-index: 2;
  position: relative;

  @include xl-device(){
    padding: 70px 70px 60px 120px;
  }
  @include lg-device(){
    padding: 50px 50px 40px 90px;
  }
  @include md-device(){
    padding: 40px 40px 30px 40px;
  }
  @include md-down-device(){
    padding: 25px 25px;
    gap: 20px;
    flex-wrap: wrap;
  }
  
  &.style-blue{
    background-color: var(--primary-blue-light);
    .body{
      border-top: 1px solid var(--primary-blue);
      border-bottom: 1px solid var(--primary-blue);
    }
  }
  &.style-yellow{
    background-color: var(--primary-yellow-light);
    .body{
      border-top: 1px solid var(--primary-yellow);
      border-bottom: 1px solid var(--primary-yellow);
    }
  }
  &.style-red{
    background-color: var(--primary-red-light);
    .body{
      border-top: 1px solid var(--primary-red);
      border-bottom: 1px solid var(--primary-red);
    }
  }

  .content{
    .title{
      h4{
        font-size: 24px;
        font-weight: 300;
        @include md-down-device(){
          font-size: 22px;
        }
      }
    }
    .body{
      padding: 20px 0px;
      p{
        font-size: 17px;
        font-style: italic;
        @include md-down-device(){
          font-size: 15px;
        }
      }
    }
  }
  .author{
    width: 100%;
    max-width: 190px;
    min-width: 120px;
    margin-top: 42px;
    @include md-down-device(){
      margin-top: 10px;
      max-width: 150px;
    }
    img{
      margin-bottom: 10px;
    }
    h6{
      margin-bottom: 0px;
      font-size: 13px;
      font-weight: 600;
    }
    span{
      display: block;
      font-size: 12px;
      line-height: 1.3;
    }
  }
}

.skill-section{
  position: relative;
  .skill-box-design{
    position: absolute;
    bottom: 0;
    right: 5%;
    max-width: 460px;
    width: 100%;
    background-color: var(--primary-green-dark-light);
    height: 60%;
    z-index: -1;
  }
}

.choose-us-card{
  background-color: var(--primary-red);
  padding: 140px 70px 90px 70px;
  width: 100%;
  max-width: 640px;
  margin-left: auto;
  margin-right: 0;
  margin-top: -300px;
  position: relative;
  z-index: 2;

  @include xxl-device(){
    max-width: 540px;
  }
  @include xl-device(){
    max-width: 500px;
    padding: 120px 50px 70px 50px;
  }
  @include lg-device(){
    max-width: 450px;
    padding: 120px 50px 70px 50px;
  }
  @include lg-down-device(){
    padding: 50px 30px 40px 30px;
    margin-top: -150px;
  }
  
  h2{
    font-size: 40px;
    font-weight: 700;
    margin-bottom: 0px;
    line-height: 1;
    position: relative;
    color: var(--white);
    padding-bottom: 55px;
    margin-bottom: 30px;
    @include md-down-device(){
      font-size: 30px;
    }
    &::after{
      content: '';
      position: absolute;
      left: 0;
      bottom: 0;
      width: 80px;
      background-color: var(--white);
      height: 1px;
    }
  }
  ul{
    @include listunstyle();
    li{
      font-size: 35px;
      font-weight: 300;
      color: var(--white);
      line-height: 1.3;
      &:first-child{
        font-style: italic;
      }

      @include md-down-device(){
        font-size: 24px;
      }
    }
  }
}
.choose-card-box{
  width: 100%;
  max-width: 475px;
  margin-left: 0px;
  margin-right: auto;
  margin-top: -180px;
  height: 570px;
  border: 1px solid var(--border);

  @include lg-down-device(){
    height: 370px;
  }
}
.career-details-section{
  padding-left: 90px;

  @include lg-device(){
    padding-left: calc((100% - 950px)/2);
  }
  @include md-device(){
    padding-left: calc((100% -	720px)/2);
  }
  @include sm-device(){
    padding-left: calc((100% - 540px)/2);
  }
  @include sm-down-device(){
    padding-left: 0;
  }
}
.career-left-image{
  max-width: 360px;
  margin-left: auto;
  position: relative;
  margin-top: -90px;
  &::before{
    content: '';
    position: absolute;
    bottom: 90px;
    right: 35%;
    width: 460px;
    height: 100%;
    background-color: var(--primary-red-light);
    z-index: -1;
    @include xl-device(){
      width: 360px;
    }
    @include lg-device(){
      width: 340px;
    }
    @include lg-down-device(){
      width: 100%;
      right: 10%;
    }
  }
  img{
    max-width: 100%;
  }
}

.eligibility-card{
  background-color: var(--primary-yellow);
  padding: 30px 60px;
  width: 100%;
  max-width: 670px;
  margin-left: auto;
  margin-right: -90px;
  margin-top: -90px;
  position: relative;

  &.style-green{
    background-color: var(--primary-green-dark);
    margin-left: -90px;
    margin-right: auto;
    margin-top: 90px;
    padding: 30px 50px;
    max-width: 100%;
    &::before{
      content: '';
      width: 100%;
      max-width: 770px;
      height: 380px;
      display: block;
      background-color: var(--primary-green-dark-light);
      position: absolute;
      left: -90px;
      bottom: 160px;
      z-index: -1;

    }
  }
  &.style-blue{
    background-color: var(--primary-blue);
    padding: 30px 60px;
    width: 100%;
    max-width: 670px;
    margin-left: -180px;
    margin-right: auto;
    margin-top: 130px;
    position: relative;
    &::before{
      content: '';
      content: '';
      width: 100%;
      max-width: 670px;
      height: 100%;
      display: block;
      background-color: var(--primary-blue-light);
      position: absolute;
      left: -90px;
      bottom: 160px;
      z-index: -1;
      
    @include lg-down-device(){
      left: -50px;
      bottom: 50px;
    }
    }
  }

  @include lg-down-device(){
    max-width: 500px;
    margin-top: 0px;
    margin-left: auto;
    margin-right: auto;
    padding: 30px 25px;
  }
  &::before{
    content: '';
    width: 100%;
    max-width: 670px;
    height: 100%;
    display: block;
    background-color: var(--primary-yellow-light);
    position: absolute;
    left: -90px;
    bottom: 160px;
    z-index: -1;
    
  @include lg-down-device(){
    left: -50px;
    bottom: 50px;
  }
  }
  .title{
    position: relative;
    padding-bottom: 5px;
    display: inline-block;
    margin-bottom: 18px;
    h6{
      color: var(--white);
    }
    &::after{
      content: '';
      position: absolute;
      left: 0;
      bottom: 0;
      width: 100%;
      height: 1px;
      background-color: var(--white);
    }
    h6{
      font-size: 18px;
      text-transform: uppercase;
    }
  }
  p{
    font-size: 15px;
    font-weight: 600;
    color: var(--white);
  }
  .date{
    margin-bottom: 20px;
    p{
      margin-bottom: 0px;
    }
    span{
      display: inline-block;
      color: var(--white);
      font-size: 15px;
    }
  }
}
.case-author-single{
  position: relative;
  width: 100%;
  max-width: 290px;
  margin-left: auto;
  margin-right: -90px;
  margin-top: 190px;
  @include lg-down-device(){
    margin-right: 0;
    margin-left: auto;
    margin-top: 70px;
    margin-bottom: 20px;
  }
  .image-wrap{
    margin-bottom: 8px;
  }
  .content{
    h6{
      margin-bottom: 0px;
      font-size: 15px;
    }
    span{
      display: block;
      font-size: 15px;
      line-height: 1.3;
    }
  }
}
.business-parnter-single{
  position: relative;
  width: 100%;
  max-width: 380px;
  margin-left: auto;
  margin-right: 0px;
  margin-top: 100px;
  &.style-blue{
    &::before{
      background-color: var(--primary-blue-light);
    }
  }
  @include lg-down-device(){
    margin-left: 0px;
    margin-right:  auto;
    margin-bottom: 20px;
  }
  &::before{
    content: '';
    position: absolute;
    right: 45%;
    bottom: 155px;
    width: 100%;
    min-width: 580px;
    height: 480px;
    background-color: var(--primary-yellow-light2);
    z-index: -1;
    @include xxl-device(){
      min-width: 470px;
    }
    @include xl-device(){
      min-width: 400px;
      right: 30%;
      bottom: 100px;
    }
    @include lg-device(){
      min-width: 370px;
      right: 15%;
      bottom: 100px;
    }
    @include md-device(){
      min-width: 370px;
      right: 30px;
      bottom: 100px;
      height: 380px;
    }
    @include sm-device(){
      min-width: 370px;
      right: 30px;
      bottom: 100px;
      height: 380px;
    }
    @include sm-down-device(){
      min-width: 370px;
      right: 30px;
      bottom: 100px;
      height: 350px;
    }
  }
  .image-wrap{
    margin-bottom: 8px;
  }
  .content{
    h6{
      margin-bottom: 0px;
      font-size: 15px;
    }
    span{
      display: block;
      font-size: 15px;
      line-height: 1.3;
    }
  }
}
.senior-parnter-single{
  h6{
    font-size: 12px;
    margin-bottom: 0px;
    line-height: 1.3;
    font-weight: 600;
  }
  span{
    display: block;
    line-height: 1.2;
    font-size: 12px;
  }
}

.apply-area{
  position: relative;
  display: block;
  &::before{
    content: '';
    position: absolute;
    left: 40%;
    bottom: 90px;
    background-color: var(--primary-yellow-light);
    width: 100%;
    max-width: 670px;
    height: 280px;
    z-index: -1;
  }
}
.apply-card{
  background-color: var(--primary-blue);
  min-height: 270px;
  width: 100%;
  max-width: 400px;
  padding: 30px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  margin-top: 120px;
  @include lg-down-device(){
    margin-top: 50px;
  }
  &.style-yellow{
    background-color: var(--primary-yellow);
    margin-top: 90px;
  }
  &.style-green{
    background-color: var(--primary-green-dark);
    margin-top: 90px;
  }
  h5{ 
    color: var(--white);
    margin-bottom: 0px;
    line-height: 1;
    font-size: 18px;
  }
  .h-line{
    width: 100%;
    display: block;
    max-width: 240px;
    height: 1px;
    background-color: var(--white);
    margin: 30px 0px;
  }
}
.apply-image{
  width: 100%;
  max-width: 380px;
  height: 380px;
  margin-left: auto;
  margin-right: auto;
  position: relative;
  margin-top: 200px;
  @include lg-down-device(){
    margin-bottom: 90px;
  }
  &::before{
    content: '';
    position: absolute;
    width: 100%;
    max-width: 380px;
    height: 280px;
    border: 1px solid var(--border);
    left: -90px;
    top: -180px;
    z-index: -1;
    @include md-device(){
      left: -160px;
      top: -90px;
    }
    @include md-down-device(){
      left: -70px;
      top: -90px;
      height: 180px;
    }
  }
  &::after{
    content: '';
    position: absolute;
    width: 100%;
    min-width: 680px;
    height: 280px;
    background-color: var(--primary-green-dark-light);
    right: -90px;
    bottom: -180px;
    z-index: -1;
    @include lg-down-device(){
      bottom: -90px;
      height: 180px;
      max-width: 100%;
      right: -10px;
    }
  }
  img{
    object-fit: cover;
  }
}
.trainee-placement-image{
  position: relative;
  margin-top: 90px;
  &::before{
    content: '';
    position: absolute;
    width: 100%;
    height: 280px;
    border: 1px solid var(--border);
    left: -90px;
    bottom: -180px;
    z-index: -1;
  }
}
.facility-image{
  position: relative;
  width: 100%;
  height: 390px;
  border: 1px solid var(--border);
  @include lg-down-device(){
    height: 300px;
    width: 90%;
    margin-left: 30px;
    margin-bottom: 40px;
    margin-top: 40px;
  }
  img{
    position: absolute;
    right: 90px;
    bottom: 90px;
    z-index: 1;
    width: 570px;
    height: 470px;
    object-fit: cover;
    @include lg-device(){
      right: 35px;
    }
    @include lg-down-device(){
      height: 300px;
      bottom: 30px;
      right: 30px;
    }
  }
}
.attorny-left-image{
  position: relative;
  margin-top: 190px;
  @include lg-down-device(){
      margin-bottom: 40px;
      margin-left: 30px;
      margin-top: 60px;
    }
  &::before{
    content: '';
    position: absolute;
    background-color: var(--primary-yellow-light2);
    width: 100%;
    height: 380px;
    left: -90px;
    top: -190px;
    z-index: -1;
    @include lg-device(){
      left: -50px;
    }
    @include lg-down-device(){
      left: -30px;
      top: -60px;
    }
    
  }
  img{
    z-index: 1;
    height: 390px;
    object-fit: cover;
    width: 100%;
  }
}
.offer-card{
  background-color: var(--primary-green-dark);
  padding: 50px;
  width: 100%;
  min-width: 670px;
  margin-left: -212px;
  margin-top: 270px;
  z-index: 3;
  position: relative;

  @include xxl-device(){
    min-width: 600px;
    margin-left: -162px;
  }
  @include xxl-down-device(){
    min-width: 100%;
    margin-left: 0;
  }
  @include xl-device(){
    padding: 35px;
  }
  @include lg-device(){
    padding: 35px;
  }
  @include lg-down-device(){
    padding: 25px;
    margin-bottom: 40px;
    margin-top: 120px;
  }

  .title{
    h4{
      font-size: 20px;
      color: var(--white);
      text-transform: uppercase;
    }
  }
  .list-block{
    margin-bottom: 25px;
    &:last-child{
      margin-bottom: 0px;
    }
    h6{
      color: var(--white);
      font-size: 16px;
      font-weight: 600;
      margin-bottom: 0px;
      @include md-down-device(){
        font-size: 15px;
      }
    }
    ul{
      list-style: none;
      padding: 0;
      margin: 0;
      li{
        font-size: 15px;
        color: var(--white);
        margin-bottom: 5px;
        @include md-down-device(){
          font-size: 14px;
        }
      }
    }
  }
}
.recruitment-image{
  position: relative;
  margin-bottom: 180px;
  @include lg-down-device(){
    margin-top: 40px;
    margin-bottom: 0px;
  }
  &::after{
    content: '';
    position: absolute;
    bottom: -180px;
    right: 90px;
    min-width: 650px;
    width: 100%;
    height: 400px;
    background-color: var(--primary-green-dark-light);
    z-index: -1;
    @include xxl-device(){
      min-width: 580px;
    }
    @include xl-device(){
      min-width: 450px;
    }
    @include lg-device(){
      min-width: 100%;
    }
    @include lg-down-device(){
      min-width: 100%;
      right: 50px;
      height: 300px;
      bottom: -50px;
    }
  }
}
.tip-left-box{
  width: 100%;
  max-width: 570px;
  height: 470px;
  border: 1px solid var(--border);
  margin-top: -90px;
  z-index: 2;
  position: relative;
}
.tips-section{
  margin-top: -90px;
  padding-left: 3%;
  @include md-device(){
    margin-top: 0px;
    padding-left: 0px;
    padding-top: 70px;
    padding-bottom: 70px;
  }
  @include md-down-device(){
    margin-top: 0px;
    padding-left: 0px;
    padding-top: 60px;
    padding-bottom: 60px;
  }
}


/*================================================
 About-section 
=================================================*/
.about-section{
  .quote-box{
    background-color: var(--primary-red);
    padding:  35px 45px;
    min-height: 380px;
    width: 100%;
    max-width: 450px;
    margin-left: auto;
    margin-right: auto;
    position: relative;
    @include lg-down-device(){
      margin-top: 30px;
      padding: 25px;
      min-height: 280px;
    }
    &::before{
      content: '';
      position: absolute;
      left: 50%;
      transform: translateX(-50%);
      height: 380px;
      width: 130%;
      background-color: var(--primary-red-light);
      top: -190px;
      z-index: 1;
      @include lg-down-device(){
        display: none;
        visibility: hidden;
      }
    }

    // @include sm-down-device(){
    //   right: -10px;
    //   bottom: -260px;
    // }
    p{
      color: var(--white);
      font-size: 20px;
      font-weight: 300;
      line-height: 1.3;
      margin-bottom: 10px;
      @include md-down-device(){
        font-size: 17px;
      }
    }
    span{
      font-size: 12px;
      font-weight: 700;
      color: var(--white);
    }
  }
  .author-image{
    max-width: 380px;
    margin-top: 190px;
    position: relative;
    @include sm-down-device(){
      max-width: 280px;
      margin-top: 100px;
    }
    &::before{
      content: '';
      position: absolute;
      border: 1px solid var(--border);
      width: 100%;
      max-width: 380px;
      height: 380px;
      z-index: -1;
      left: 82%;
      bottom: 75%;
      @include xl-device(){
        left: 70%;
      }
      @include lg-device(){
        left: 45%;
      }
      @include md-device(){
        left: 45%;
      }
      @include md-down-device(){
        left: 5%;
        max-width: 280px;
      }
    }
    img{
      width: 100%;
      object-fit: cover;
      margin-left: 90px;
      @include lg-down-device(){
        margin-left: 0px;
      }
    }
  }
}

/*================================================
Counter-section
=================================================*/
.counter-section{
  background-color: var(--primary-green-dark-light);
}
.counter-single{
  text-align: center;
  &.style-green{
    h3{
      color: var(--primary-green-dark);
    }
  }
  &.style-yellow{
    h3{
      color: var(--primary-yellow);
      position: relative;
      &::after{
        content: '\F4D1';
        font-family: 'Bootstrap-icons';
        position: absolute;
        right: -25px;
        top: 32px;
        font-size: 32px;
        color: var(--primary-yellow);
      }
    }
  } 
  &.style-red{
    h3{
      color: var(--primary-red);
    }
  }
  &.style-blue{
    h3{
      color: var(--primary-blue);
      position: relative;
      &::after{
        content: '\F64D';
        font-family: 'Bootstrap-icons';
        position: absolute;
        right: -30px;
        top: 50%;
        transform: translateY(-50%);
        font-size: 32px;
        color: var(--primary-blue);
      }
    }
  }
  h3{
    font-size: 130px;
    font-weight: 300;
    line-height: 1;
    letter-spacing: -8px;
    margin-bottom: 15px;

    @include lg-device(){
      font-size: 100px;
      letter-spacing: -5px;
    }
    @include md-device(){
    letter-spacing: -3px;
    font-size: 80px;
    }
    @include md-down-device(){
      font-size: 50px;
      letter-spacing: 0px;
    }
  }
  p{
    margin-bottom: 0px;
    font-size: 15px;
    line-height: 1.4;
  }
}
.sponsor-wrapper{
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 15px;
  flex-wrap: nowrap;
  a{
    max-width: auto;
    @include sm-down-device(){
      max-width: 80px;
    }
  }
  @include sm-down-device(){
    flex-wrap: wrap;
  }
}
/*================================================
 Culture-section 
=================================================*/
.culture-section{
  position: relative;
  .bottom-box{
    position: absolute;
    width: 100%;
    max-width: 380px;
    height: 300px;
    background-color: var(--primary-red-light);
    right: 15%;
    bottom: 0px;
    @include lg-down-device(){
      display: none;
      visibility: hidden;
    }
  }
  .quote-box{
    background-color: var(--primary-green-dark-light);
    padding:  35px 45px;
    min-height: 380px;
    width: 100%;
    max-width: 450px;
    margin-left: auto;
    margin-right: auto;
    position: relative;

    @include lg-down-device(){
      margin-top: 30px;
      padding: 25px;
      min-height: 290px;
    }
    p{
      color: var(--text-primary);
      font-size: 20px;
      font-weight: 300;
      line-height: 1.3;
      margin-bottom: 10px;
      @include md-down-device(){
        font-size: 17px;
      }
    }
    span{
      font-size: 12px;
      font-weight: 700;
      color: var(--text-primary);
    }
  }
  .value-card{
    max-width: 350px;
    margin-top: 90px;
    position: relative;
    background-color: var(--primary-red);
    padding: 45px;
    min-height: 350px;
    @include lg-down-device(){
      margin-bottom: 30px;
      padding: 25px;
      min-height: 250px;
    }
    h6{
      color: var(--white);
      font-weight: 600;
      margin-bottom: 0px;
      font-size: 19px;
      @include md-down-device(){
        font-size: 17px;
      }
    }
    ul{
      @include listunstyle();
      li{
        color: var(--white);
        font-size: 19px;
        line-height: 1.2;
        @include md-down-device(){
          font-size: 17px;
        }
      }
    }
    &::before{
      content: '';
      position: absolute;
      border: 1px solid var(--border);
      width: 100%;
      max-width: 380px;
      height: 380px;
      z-index: -1;
      left: 90%;
      bottom: 75%;
      @include xl-device(){
        left: 65%;
      }
      @include lg-device(){
        left: 60%;
      }
      @include md-device(){
        left: 49%;
      }
      @include md-down-device(){
        left: 5%;
        max-width: 280px;
      }
    }
    img{
      width: 100%;
      object-fit: cover;
      margin-left: 90px;
    }
  }
}
.about-news-box{
  position: absolute;
  width: 30%;
  right: 30%;
  height: 300px;
  border: 1px solid var(--border);
  top: -90px;
  @include lg-device(){
    right: 20%;
  }
  @include lg-down-device(){
    right: 10%;
    width: 35%;
  }
}
.trending-right-box{
  position: absolute;
  top: 0px;
  right: 5%;
  width: 100%;
  max-width: 40%;
  height: 380px;
  background-color: var(--primary-red-light);
}
.un-logo-wrap{
  margin-top: 90px;
  display: grid;
  grid-template-columns: repeat(9, 1fr);
  grid-gap: 15px;
  position: relative;
  padding-bottom: 180px;
  &::after{
    content: '';
    position: absolute;
    right: -90px;
    top: -90px;
    background-color: var(--primary-red-light);
    width: 80%;
    height: 485px;
    @include lg-device(){
      right: -30px;
    }
    @include lg-down-device(){
      right: 0px;
    }
  }
  @include md-device(){
    grid-template-columns: repeat(6, 1fr);
    grid-gap: 10px;
    margin-top: 70px;
  }
  @include md-down-device(){
    grid-gap: 5px;
    grid-template-columns: repeat(6, 1fr);
    margin-top: 60px;
  }
  .logo-wrap{
    max-width: 130px;
  }
}
.business-card-section{
  position: relative;
  .business-card-box{
    position: absolute;
    width: 20%;
    height: 210px;
    border: 1px solid var(--border);
    bottom: 150px;
    left: 12%;
    @include md-device(){
      width: 10%;
      left: 2%;
    }
    @include md-down-device(){
      display: none;
      visibility: hidden;
      left: 4%;
    }
  }
  &::before{
    content: '';
    position: absolute;
    top: 40%;
    left: calc((100% - 1040px)/2);
    transform: translateY(-50%);
    width: 28.8%;
    height: 55%;
    background-color: var(--primary-blue-light);
    z-index: -1;
    @include xl-device(){
      left: calc((100% - 1020px)/2);
    }
    @include lg-device(){
      left: calc((100% - 950px)/2);
    }
    @include md-device(){
      left: 0;
    }
    @include sm-device(){
      left: 0;
    }
    @include sm-down-device(){
      content: none;
    }
  }
}
.address-section{
  background-color: var(--primary-yellow-light2);
}
.address-single{
  h6{
    font-size: 14px;
    margin-bottom: 0px;
  }
  .details{
    min-height: 350px;
    margin-bottom: 25px;
    @include lg-down-device(){
      min-height: auto;
    }
    p{
      font-size: 14px;
    }
  }
}
.address-list{
  list-style: none;
  padding: 0;
  margin: 0;
  position: relative;
  padding-top: 15px;
  margin-bottom: 30px;
  &::before{
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    max-width: 90px;
    height: 1px;
    background-color: var(--text-primary);
  }
  li{
    font-size: 14px;
    font-weight: 600;
    line-height: 1.3;
    &:first-child{
      margin-bottom: 15px;
    }
    &:last-child{
      margin-bottom: 0px;
    }
    a{
      color: inherit;
    }
  }
}
.key-contact-list{
  list-style: none;
  padding: 0;
  margin: 0;
  position: relative;
  padding-top: 15px;
  &::before{
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    max-width: 90px;
    height: 1px;
    background-color: var(--text-primary);
  }
  li{
    font-size: 14px;
    font-weight: 600;
    line-height: 1.3;
    margin-bottom: 15px;
    &:last-child{
      margin-bottom: 0px;
    }
    a{
      color: inherit;
    }
    span{
      display: block;
      font-size: 14px;
      font-weight: 300;
      line-height: 1;
    }
  }
}
.openday-section{
  padding-left: 8%;
  padding-right: 8%;

  .box-right{
    width: 100%;
    max-width: 550px;
    height: 320px;
    border: 1px solid var(--border);
    margin-top: -90px;
  }

  .openday-apply{
    margin-left: -90px;
    margin-top: -90px;
  }
  .attendents{
    max-width: 280px;
    width: 100%;
    margin-right: auto;
    text-align: right;
    h2{
      font-size: 180px;
      margin-bottom: 0px;
      font-weight: 300;
      color: var(--primary-green-dark);
      letter-spacing: -8px;position: relative;
      display: inline-block;
      line-height: 1;
      &::after{
        content: '\F4D1';
        font-family: 'Bootstrap-icons';
        position: absolute;
        right: -35px;
        top: 55px;
        font-size: 45px;
        color: vvar(--primary-green-dark);
      }
    }
    span{
      display: block;
      width: 100%;
      max-width: 170px;
      margin-left: auto;
      font-size: 12px;
      font-weight: 700;
      text-align: left;
    }
  }
}

