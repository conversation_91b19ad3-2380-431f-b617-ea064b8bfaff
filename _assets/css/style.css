@import url("https://fonts.googleapis.com/css2?family=Open+Sans:ital,wght@0,300;0,400;0,500;0,600;0,700;0,800;1,300;1,400;1,500;1,600;1,700&display=swap");
* {
  margin: 0;
  padding: 0;
  scroll-behavior: smooth;
}

:root {
  --text-primary: rgb(29, 29, 27);
  --border: rgba(29, 29, 27,0.3);
  --text-secondary: #575756;
  --white:#ffffff;
  --dark-bg1: #3C3C3B;
  --dark-bg2: #1D1D1B;
  --primary-red: rgb(227, 6, 19);
  --primary-red-light: rgba(227, 6, 19, 0.04);
  --primary-yellow: rgba(249,178,51);
  --primary-yellow-light: rgb(254,243,224);
  --primary-yellow-light2: #f9b23314;
  --primary-blue: #36a9e1;
  --primary-blue-light: #F1F9FD;
  --primary-green: rgb(147, 213, 0);
  --primary-green-dark: rgba(58,170,53,1);
  --primary-green-dark-light: #F3FAF3;
  --primary-green-light: rgba(147, 213, 0,0.04);
  --font-open: "Open Sans", sans-serif;
}

/*================================================
1. Mixins Css
=================================================*/
/*================================================
2. Global Css
=================================================*/
html {
  font-size: 100%;
  scroll-behavior: smooth;
}

body {
  margin: 0;
  padding: 0;
  font-family: var(--font-open);
  color: var(--text-primary);
  font-size: 15px;
  font-weight: 200;
  line-height: 1.5;
  box-sizing: border-box;
}

h1, h2, h3, h4, h5, h6 {
  font-family: var(--font-open);
  font-weight: 600;
  line-height: 1.4;
  color: var(--text-primary);
}

button {
  outline: none;
  border: none;
}

i.bx {
  vertical-align: middle;
}

img {
  max-width: 100%;
  height: auto;
}

a {
  text-decoration: none;
}

.pt-40 {
  padding-top: 40px;
}

.pb-40 {
  padding-bottom: 40px;
}

.mb-140 {
  margin-bottom: 140px;
}
@media (max-width: 991px) {
  .mb-140 {
    margin-bottom: 30px;
  }
}

.pt-50 {
  padding-top: 50px;
}

.pt-60 {
  padding-top: 60px;
}

.pb-60 {
  padding-bottom: 60px;
}

.pt-100 {
  padding-top: 100px;
}
@media (min-width: 768px) and (max-width: 991px) {
  .pt-100 {
    padding-top: 70px;
  }
}
@media (max-width: 767px) {
  .pt-100 {
    padding-top: 60px;
  }
}

.pb-100 {
  padding-bottom: 100px;
}
@media (min-width: 768px) and (max-width: 991px) {
  .pb-100 {
    padding-bottom: 70px;
  }
}
@media (max-width: 767px) {
  .pb-100 {
    padding-bottom: 60px;
  }
}

.pt-120 {
  padding-top: 120px;
}

.pb-120 {
  padding-bottom: 120px;
}

.pb-70 {
  padding-bottom: 70px;
}

.mb-100 {
  margin-bottom: 100px;
}
@media (max-width: 767px) {
  .mb-100 {
    margin-bottom: 40px;
  }
}

.mb-60 {
  margin-bottom: 60px;
}
@media (max-width: 767px) {
  .mb-60 {
    margin-bottom: 40px;
  }
}

.mb-65 {
  margin-bottom: 65px;
}
@media (max-width: 767px) {
  .mb-65 {
    margin-bottom: 40px;
  }
}

.mt-120 {
  margin-top: 120px;
}
@media (min-width: 992px) and (max-width: 1199px) {
  .mt-120 {
    margin-top: 100px;
  }
}
@media (max-width: 991px) {
  .mt-120 {
    margin-top: 90px;
  }
}

.mb-120 {
  margin-bottom: 120px;
}

.mb-100 {
  margin-bottom: 100px;
}
@media (max-width: 767px) {
  .mb-100 {
    margin-bottom: 40px;
  }
}

.mt-100 {
  margin-top: 100px;
}
@media (max-width: 767px) {
  .mt-100 {
    margin-top: 40px;
  }
}

.mb-90 {
  margin-bottom: 90px;
}
@media (max-width: 767px) {
  .mb-90 {
    margin-bottom: 50px;
  }
}

.mb-15 {
  margin-bottom: 15px;
}

.mb-70 {
  margin-bottom: 70px;
}
@media (max-width: 767px) {
  .mb-70 {
    margin-bottom: 40px;
  }
}

.mb-50 {
  margin-bottom: 50px;
}

.mb-45 {
  margin-bottom: 45px !important;
}

.mb-45 {
  margin-bottom: 45px;
}

.mb-35 {
  margin-bottom: 35px;
}

.mb-20 {
  margin-bottom: 20px;
}

.mt-15 {
  margin-top: 15px;
}

.mt-40 {
  margin-top: 40px;
}

.mt-40 {
  margin-top: 40px;
}

.mb-40 {
  margin-bottom: 40px;
}

.mb-30 {
  margin-bottom: 30px;
}

.mb-25 {
  margin-bottom: 25px;
}

.mt-50 {
  margin-top: 50px;
}

.mt-25 {
  margin-top: 25px;
}

.mb-50 {
  margin-bottom: 50px;
}

.mt-60 {
  margin-top: 60px;
}
@media (max-width: 767px) {
  .mt-60 {
    margin-top: 40px;
  }
}

.mt-65 {
  margin-top: 65px;
}
@media (max-width: 767px) {
  .mt-65 {
    margin-top: 45px;
  }
}

.mt-70 {
  margin-top: 70px;
}
@media (max-width: 767px) {
  .mt-70 {
    margin-top: 40px;
  }
}

.title-pb-150 {
  padding-bottom: 150px;
}
@media (min-width: 1400px) and (max-width: 1599px) {
  .title-pb-150 {
    padding-bottom: 100px;
  }
}
@media (min-width: 1200px) and (max-width: 1399px) {
  .title-pb-150 {
    padding-bottom: 80px;
  }
}
@media (min-width: 992px) and (max-width: 1199px) {
  .title-pb-150 {
    padding-bottom: 50px;
  }
}
@media (max-width: 991px) {
  .title-pb-150 {
    padding-bottom: 0px;
  }
}

.pl-container {
  padding-left: calc((100% - 1040px) / 2);
}
@media (min-width: 1200px) and (max-width: 1399px) {
  .pl-container {
    padding-left: calc((100% - 1020px) / 2);
  }
}
@media (min-width: 992px) and (max-width: 1199px) {
  .pl-container {
    padding-left: calc((100% - 950px) / 2);
  }
}
@media (min-width: 768px) and (max-width: 991px) {
  .pl-container {
    padding-left: calc((100% - 720px) / 2);
    padding-right: calc((100% - 720px) / 2);
  }
}
@media (min-width: 576px) and (max-width: 768px) {
  .pl-container {
    padding-left: calc((100% - 540px) / 2);
    padding-right: calc((100% - 540px) / 2);
  }
}
@media (max-width: 576px) {
  .pl-container {
    padding-left: 0;
    padding-right: 0;
  }
}

.pr-container {
  padding-right: calc((100% - 1040px) / 2);
}
@media (min-width: 1200px) and (max-width: 1399px) {
  .pr-container {
    padding-right: calc((100% - 1020px) / 2);
  }
}
@media (min-width: 992px) and (max-width: 1199px) {
  .pr-container {
    padding-right: calc((100% - 950px) / 2);
  }
}
@media (min-width: 768px) and (max-width: 991px) {
  .pr-container {
    padding-left: calc((100% - 720px) / 2);
    padding-right: calc((100% - 720px) / 2);
  }
}
@media (min-width: 576px) and (max-width: 768px) {
  .pr-container {
    padding-left: calc((100% - 540px) / 2);
    padding-right: calc((100% - 540px) / 2);
  }
}
@media (max-width: 576px) {
  .pr-container {
    padding-left: 0;
    padding-right: 0;
  }
}

.container-one {
  width: 100%;
  max-width: 1040px;
  margin-left: auto;
  margin-right: auto;
  padding-left: 15px;
  padding-right: 15px;
}
@media (min-width: 1400px) and (max-width: 1599px) {
  .container-one {
    max-width: 1000px;
  }
}
@media (min-width: 1200px) and (max-width: 1399px) {
  .container-one {
    max-width: 1020px;
  }
}
@media (min-width: 992px) and (max-width: 1199px) {
  .container-one {
    max-width: 950px;
  }
}
@media (min-width: 768px) and (max-width: 991px) {
  .container-one {
    max-width: 720px;
  }
}
@media (min-width: 576px) and (max-width: 768px) {
  .container-one {
    max-width: 540px;
  }
}
@media (max-width: 576px) {
  .container-one {
    max-width: 100%;
  }
}

.image-adjust {
  background-size: cover;
  background-repeat: no-repeat;
}

.mobile-search input, .mobile-search .contact-form textarea, .contact-form .mobile-search textarea {
  border: none;
  border-radius: unset;
  width: 100%;
  background: transparent;
  transition: 0.3s ease-in-out;
  color: var(--text-primary);
  border-bottom: 1px solid var(--white);
  padding: 5px 20px;
}
.mobile-search input:focus, .mobile-search .contact-form textarea:focus, .contact-form .mobile-search textarea:focus {
  border: none;
  outline: none;
}
.mobile-search input::-moz-placeholder, .mobile-search .contact-form textarea::-moz-placeholder, .contact-form .mobile-search textarea::-moz-placeholder {
  color: var(--white);
}
.mobile-search input::placeholder, .mobile-search .contact-form textarea::placeholder, .contact-form .mobile-search textarea::placeholder {
  color: var(--white);
}
.mobile-search input::-moz-placeholder, .mobile-search .contact-form textarea::-moz-placeholder, .contact-form .mobile-search textarea::-moz-placeholder {
  font-size: 14px;
  color: var(--text-secondary);
}
.mobile-search input::placeholder, .mobile-search .contact-form textarea::placeholder, .contact-form .mobile-search textarea::placeholder {
  font-size: 14px;
  color: var(--text-secondary);
}

.bg-green-light {
  background-color: var(--primary-green-dark-light);
}

/*=======================================
 06. Section-title
=======================================*/
.section-title-one p {
  font-size: 18px;
  margin-bottom: 0;
  font-weight: 300;
  line-height: 1.3;
}
@media (max-width: 767px) {
  .section-title-one p {
    font-size: 16px;
  }
}
.section-title-one.style-yellow h2::after {
  background-color: var(--primary-yellow);
}
.section-title-one.style-blue h2::after {
  background-color: var(--primary-blue);
}
.section-title-one.style-green h2::after {
  background-color: var(--primary-green);
}
.section-title-one.style-red h2::after {
  background-color: var(--primary-red);
}
.section-title-one.style-white h2 {
  color: var(--white);
}
.section-title-one.style-white h2::after {
  background-color: var(--white);
}
.section-title-one h2 {
  font-size: 32px;
  font-weight: 300;
  margin-bottom: 0px;
  line-height: 1;
  position: relative;
  padding-bottom: 15px;
  margin-bottom: 20px;
  display: inline-block;
  position: relative;
}
@media (max-width: 767px) {
  .section-title-one h2 {
    font-size: 28px;
  }
}
.section-title-one h2::after {
  content: "";
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 2px;
  display: block;
}

.section-title-two {
  margin-bottom: 30px;
}
.section-title-two h4 {
  position: relative;
  font-size: 22px;
  font-weight: 600;
  padding-top: 10px;
}
@media (max-width: 767px) {
  .section-title-two h4 {
    font-size: 20px;
  }
}
.section-title-two h4::before {
  content: "";
  width: 100%;
  max-width: 310px;
  height: 1px;
  background-color: var(--text-primary);
  position: absolute;
  top: 0px;
  left: 0px;
}

.section-title-two-borderless {
  margin-bottom: 30px;
}
.section-title-two-borderless h4 {
  position: relative;
  font-size: 24px;
  font-weight: 600;
}

.section-title-three {
  margin-bottom: 30px;
}
.section-title-three h5 {
  position: relative;
  font-size: 18px;
  font-weight: 800;
}

.section-title-four {
  margin-bottom: 15px;
}
.section-title-four h4 {
  font-size: 20px;
  font-weight: 600;
  margin-bottom: 0px;
  line-height: 1;
  position: relative;
  padding-bottom: 15px;
  color: var(--white);
  display: inline-block;
  position: relative;
}
@media (max-width: 767px) {
  .section-title-four h4 {
    font-size: 30px;
  }
}
.section-title-four h4::after {
  content: "";
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 2px;
  display: block;
  background-color: var(--white);
}

.subtitle {
  position: relative;
}
.subtitle h6 {
  font-size: 15px;
  font-weight: 700;
  line-height: 1;
}

/*=======================================
 09. Buttons 
=======================================*/
.eg-btn {
  text-align: center;
  display: inline-flex;
  text-decoration: none;
  transition: all 0.45s ease-in-out;
  text-transform: capitalize;
  cursor: pointer;
  font-family: var(--font-open);
  border-radius: 0px;
}

.btn--primary-blue {
  color: var(--white);
  border-radius: 0px;
  position: relative;
  z-index: 1;
  display: inline-flex;
  justify-content: center;
  white-space: nowrap;
  background: transparent;
  transition: all 0.5s;
  border-radius: 0px;
  background: var(--primary-blue);
  text-align: left;
}
.btn--primary-blue::before {
  content: "";
  width: 0%;
  height: 100%;
  background: var(--text-primary);
  position: absolute;
  left: 0;
  top: 0;
  transition: all 0.5s;
  z-index: -1;
  opacity: 1;
}
.btn--primary-blue:hover {
  color: var(--white);
}
.btn--primary-blue:hover i {
  right: 0;
  color: var(--white);
}
.btn--primary-blue:hover::before {
  width: 100%;
}

.btn--primary-red {
  color: var(--white);
  border-radius: 0px;
  position: relative;
  z-index: 1;
  display: inline-flex;
  justify-content: center;
  white-space: nowrap;
  background: transparent;
  transition: all 0.5s;
  border-radius: 0px;
  background: var(--primary-red);
  text-align: left;
}
.btn--primary-red::before {
  content: "";
  width: 0%;
  height: 100%;
  background: var(--text-primary);
  position: absolute;
  left: 0;
  top: 0;
  transition: all 0.5s;
  z-index: -1;
  opacity: 1;
}
.btn--primary-red:hover {
  color: var(--white);
}
.btn--primary-red:hover i {
  right: 0;
  color: var(--white);
}
.btn--primary-red:hover::before {
  width: 100%;
}

.btn--primary-black {
  color: var(--white);
  border-radius: 0px;
  position: relative;
  z-index: 1;
  display: inline-flex;
  justify-content: center;
  white-space: nowrap;
  background: transparent;
  transition: all 0.5s;
  border-radius: 0px;
  background: var(--text-primary);
  text-align: left;
}
.btn--primary-black.style-two:hover .bi {
  color: var(--text-primary);
}
.btn--primary-black.style-two .bi {
  color: var(--white);
}
.btn--primary-black::before {
  content: "";
  width: 0%;
  height: 100%;
  background: var(--white);
  position: absolute;
  left: 0;
  top: 0;
  transition: all 0.5s;
  z-index: -1;
  opacity: 1;
}
.btn--primary-black:hover {
  color: var(--text-primary);
}
.btn--primary-black:hover i {
  right: 0;
  color: var(--white);
}
.btn--primary-black:hover::before {
  width: 100%;
}

.btn--primary-green {
  color: var(--white);
  border-radius: 0px;
  position: relative;
  z-index: 1;
  display: inline-flex;
  justify-content: center;
  white-space: nowrap;
  background: transparent;
  transition: all 0.5s;
  border-radius: 0px;
  background: var(--primary-green-dark);
  text-align: left;
}
.btn--primary-green::before {
  content: "";
  width: 0%;
  height: 100%;
  background: var(--text-primary);
  position: absolute;
  left: 0;
  top: 0;
  transition: all 0.5s;
  z-index: -1;
  opacity: 1;
}
.btn--primary-green:hover {
  color: var(--white);
}
.btn--primary-green:hover i {
  right: 0;
  color: var(--white);
}
.btn--primary-green:hover::before {
  width: 100%;
}

.btn--primary-yellow {
  color: var(--white);
  border-radius: 0px;
  position: relative;
  z-index: 1;
  display: inline-flex;
  justify-content: center;
  white-space: nowrap;
  background: transparent;
  transition: all 0.5s;
  border-radius: 0px;
  background: var(--primary-yellow);
  text-align: left;
}
.btn--primary-yellow::before {
  content: "";
  width: 0%;
  height: 100%;
  background: var(--text-primary);
  position: absolute;
  left: 0;
  top: 0;
  transition: all 0.5s;
  z-index: -1;
  opacity: 1;
}
.btn--primary-yellow:hover {
  color: var(--white);
}
.btn--primary-yellow:hover i {
  right: 0;
  color: var(--white);
}
.btn--primary-yellow:hover::before {
  width: 100%;
}

.arrow-button i {
  font-size: 60px;
  color: var(--text-secondary);
  opacity: 0.7;
}

.btn--lg {
  font-size: 13px;
  font-weight: 600;
  text-transform: capitalize;
  padding: 6px 30px;
}
@media (max-width: 767px) {
  .btn--lg {
    padding: 8px 25px;
    font-size: 12px;
  }
}

.btn--lg2 {
  font-size: 13px;
  font-weight: 600;
  text-transform: capitalize;
  padding: 8px 35px 8px 10px;
  position: relative;
}
@media (max-width: 767px) {
  .btn--lg2 {
    padding: 4px 30px 4px 8px;
    font-size: 11px;
  }
}
.btn--lg2 i {
  position: absolute;
  right: -15px;
  top: 50%;
  transform: translateY(-50%);
  color: var(--text-primary);
  font-size: 30px;
  display: inline-block;
  transition: 0.4s;
}
@media (max-width: 767px) {
  .btn--lg2 i {
    font-size: 25px;
    right: -10px;
  }
}

.blog-btn {
  min-width: 142px;
}

.btn--md {
  padding: 10px 35px;
  font-size: 15px;
  font-weight: 700;
}
@media (max-width: 767px) {
  .btn--md {
    padding: 10px 40px;
  }
}

.btn--sm {
  font-size: 12px;
  font-weight: 600;
  padding: 6px 18px;
}

.btn--sm2 {
  font-size: 10px;
  font-weight: 600;
  padding: 3px 15px 3px 5px;
}

.bg-primary-blue-light {
  background-color: var(--primary-blue-light1);
}

.bg-primary-blue-light2 {
  background-color: var(--primary-blue-light2);
}

.bg-primary-green-light {
  background-color: var(--primary-green-light);
}

.bg-primary-yellow-light {
  background-color: var(--primary-yellow-light);
}

.bg-primary-pink-light {
  background-color: var(--primary-pink-light);
}

.bg-primary-pink-light2 {
  background-color: var(--primary-pink-light2);
}

/*=======================================
  28. search area start
=======================================*/
.mobile-search {
  background: rgba(0, 0, 0, 0.85);
  box-shadow: 0px 0px 10px rgba(0, 0, 0, 0.09);
  width: 100%;
  height: 100%;
  border-radius: 4px;
  display: flex;
  justify-content: center;
  flex-wrap: wrap;
  align-items: center;
  align-items: center;
  position: fixed;
  cursor: pointer;
  transform: scale(0.7);
  top: 0;
  left: 0;
  z-index: 9999;
  opacity: 0;
  visibility: hidden;
  transition: 0.65s ease;
  padding: 35px 100px;
}
@media (max-width: 767px) {
  .mobile-search {
    padding: 20px 20px;
  }
}
.mobile-search label {
  color: #fff;
  margin-bottom: 20px;
  font-family: var(--font-nunito);
}
.mobile-search.slide {
  transform: scale(1);
  opacity: 1;
  visibility: visible;
}
.mobile-search .search-cross-btn {
  color: #fff;
  cursor: pointer;
  background: rgba(var(--white), 0.6);
  border-radius: 50%;
  height: 40px;
  width: 40px;
  line-height: 40px;
  text-align: center;
  line-height: 43px;
  transition: 0.5s ease;
}
.mobile-search .search-cross-btn:hover {
  transform: scale(1.1);
}
.mobile-search .search-cross-btn i {
  font-size: 25px;
}

/*=======================================
 11 .Header Start
=======================================*/
header.style-1 {
  background-color: transparent;
  width: 100%;
  z-index: 99;
  display: flex;
  justify-content: center;
  align-items: center;
  position: absolute;
  padding-left: calc((100% - 1040px) / 2);
  padding-right: 12%;
}
@media (min-width: 1200px) and (max-width: 1399px) {
  header.style-1 {
    padding-left: calc((100% - 1020px) / 2);
    padding-right: 5%;
  }
}
@media (min-width: 992px) and (max-width: 1199px) {
  header.style-1 {
    padding-left: calc((100% - 950px) / 2);
  }
}
@media (min-width: 768px) and (max-width: 991px) {
  header.style-1 {
    padding-left: calc((100% - 720px) / 2);
  }
}
@media (min-width: 576px) and (max-width: 768px) {
  header.style-1 {
    padding-left: calc((100% - 540px) / 2);
  }
}
@media (max-width: 576px) {
  header.style-1 {
    padding-left: 0;
  }
}
@media (max-width: 991px) {
  header.style-1 {
    padding-right: 10px;
  }
}
header.style-1 .mobile-logo-wrap {
  max-width: 100px;
}
header.style-1 .header-icons {
  position: absolute;
  top: 30px;
  right: -25px;
  width: 100%;
  max-width: 90px;
  min-width: 90px;
}
header.style-1 .header-icons.style-white ul li::after {
  background-color: var(--white);
}
header.style-1 .header-icons.style-white ul li i {
  color: var(--white);
}
@media (min-width: 992px) and (max-width: 1199px) {
  header.style-1 .header-icons {
    right: -20px;
  }
}
header.style-1 .header-icons ul {
  list-style: none;
  margin: 0;
  padding: 0;
  display: flex;
  justify-content: flex-end;
  align-items: center;
  gap: 16px;
}
header.style-1 .header-icons ul li {
  position: relative;
  cursor: pointer;
}
header.style-1 .header-icons ul li:last-child::after {
  content: unset;
}
header.style-1 .header-icons ul li::after {
  content: "";
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  right: -8px;
  width: 1px;
  height: 20px;
  background-color: var(--text-primary);
}
header.style-1 .header-icons ul li i {
  color: var(--text-primary);
  font-size: 22px;
  line-height: 1;
}
header.style-1.sticky {
  position: fixed;
  top: 0px;
  left: 0;
  z-index: 999;
  box-shadow: 5px 3px 40px rgba(100, 100, 100, 0.1);
  animation: smooth-header 0.65s linear;
}
@keyframes smooth-header {
  0% {
    transform: translateY(-30px);
  }
  100% {
    transform: translateY(0px);
  }
}
header.style-1 .main-nav .mobile-menu-logo {
  display: none;
}
header.style-1 .main-nav > ul {
  list-style: none;
  margin: 0;
  padding: 0;
  padding-right: 40px;
}
header.style-1 .main-nav > ul > li {
  display: inline-block;
  position: relative;
  padding: 0 10px;
}
header.style-1 .main-nav > ul > li:last-child::after {
  content: none;
}
header.style-1 .main-nav > ul > li a {
  font-size: 14px;
  color: var(--text-primary);
  text-decoration: none;
  font-weight: 600;
  display: block;
  text-transform: uppercase;
  padding: 30px 0;
  position: relative;
  font-family: var(--font-work-sans);
  transition: all 0.5s ease-out 0s;
  position: relative;
}
header.style-1 .main-nav > ul > li a:hover {
  color: var(--primary-red);
}
header.style-1 .main-nav > ul > li a:hover:after {
  opacity: 1;
  width: 100%;
}
header.style-1 .main-nav > ul > li a::after {
  content: "";
  position: absolute;
  bottom: 25px;
  left: 0;
  width: 0%;
  height: 2px;
  border-radius: 30px;
  display: block;
  background: var(--text-primary);
  opacity: 0;
  transition: all 0.5s ease-out 0s;
}
header.style-1 .main-nav > ul > li a.active {
  color: var(--primary-red);
  position: relative;
  display: inline-block;
}
header.style-1 .main-nav > ul > li i {
  width: 30px;
  font-size: 14px;
  text-align: center;
  color: var(--text-primary);
  font-style: normal;
  position: absolute;
  right: -8px;
  top: 31px;
  z-index: 999;
  cursor: pointer;
  display: none;
  opacity: 0;
}
@media (max-width: 991px) {
  header.style-1 .main-nav > ul > li i {
    color: var(--text-primary);
    opacity: 1;
  }
}
header.style-1 .main-nav > ul > li ul.sub-menu {
  position: absolute;
  left: 0;
  right: 0;
  top: auto;
  margin: 0;
  min-width: 165px;
  border-radius: 0px;
  padding: 5px 0;
  opacity: 0;
  visibility: hidden;
  background: var(--text-primary);
  text-align: left;
  transition: all 0.55s ease-in-out;
  transform: translateY(20px);
}
header.style-1 .main-nav > ul > li ul.sub-menu > li {
  padding: 0;
  display: block;
  position: relative;
}
header.style-1 .main-nav > ul > li ul.sub-menu > li i {
  position: absolute;
  top: 10px;
  right: 6px;
  display: block;
  color: var(--text-primary);
}
header.style-1 .main-nav > ul > li ul.sub-menu > li a {
  display: block;
  padding: 10px 17px;
  color: var(--white);
  font-weight: 400;
  text-transform: uppercase;
  font-size: 12px;
  line-height: 1;
  transition: all 0.4s ease-out 0s;
  position: relative;
}
header.style-1 .main-nav > ul > li ul.sub-menu > li a::after {
  content: "";
  position: absolute;
  bottom: 0px;
  left: 0px;
  width: 0px;
  height: 1px;
  border-radius: 30px;
  display: block;
  background: linear-gradient(90deg, var(--primary-color1), transparent);
  transition: all 0.5s ease-in-out;
}
header.style-1 .main-nav > ul > li ul.sub-menu > li a:hover {
  color: var(--primary-red);
}
header.style-1 .main-nav > ul > li ul.sub-menu > li a:hover:after {
  width: 100%;
}
header.style-1 .main-nav > ul > li ul.sub-menu > li a.active {
  color: var(--primary-color1);
}
header.style-1 .main-nav > ul > li ul.sub-menu > li .sub-menu {
  left: 215px;
  position: absolute;
  background: #0B0F14;
  top: 0;
}
@media only screen and (max-width: 1199px) {
  header.style-1 .main-nav > ul > li ul.sub-menu > li .sub-menu {
    margin-left: 10px;
    position: unset;
    max-width: 230px;
    min-width: 215px;
    background: transparent;
    top: 0;
  }
}
header.style-1 .main-nav > ul > li ul.sub-menu > li .sub-menu li i {
  display: block;
}
header.style-1 .main-nav > ul > li ul.sub-menu > li:last-child {
  border-bottom: none;
}
header.style-1 .main-nav > ul > li:hover > ul.sub-menu {
  visibility: visible;
  opacity: 1;
  transform: translateY(0);
}
header.style-1 .main-nav > ul li.menu-item-has-children > i {
  display: block;
}
@media (min-width: 992px) {
  header.style-1 .main-nav > ul.style-white li a {
    color: var(--white);
  }
  header.style-1 .main-nav > ul.style-white li:last-child::after {
    content: none;
  }
  header.style-1 .main-nav > ul.style-white li::after {
    content: "";
    background-color: var(--white);
  }
}
@media (min-width: 992px) and (max-width: 991px) {
  header.style-1 .main-nav > ul.style-white li::after {
    content: none;
  }
}
@media only screen and (max-width: 991px) {
  header.style-1 .main-nav {
    position: fixed;
    top: 0;
    left: 0;
    width: 280px;
    padding: 30px 20px !important;
    z-index: 99999;
    height: 100%;
    overflow: auto;
    background: var(--white);
    transform: translateX(-100%);
    transition: transform 0.6s;
    box-shadow: 0px 2px 20px rgba(0, 0, 0, 0.03);
  }
  header.style-1 .main-nav.show-menu {
    transform: translateX(0);
  }
  header.style-1 .main-nav .mobile-menu-logo {
    text-align: left;
    padding-top: 20px;
    display: block;
    padding-bottom: 8px;
  }
  header.style-1 .main-nav ul {
    float: none;
    text-align: left;
    padding: 35px 10px 10px 0;
  }
  header.style-1 .main-nav ul li {
    display: block;
    position: relative;
    padding: 0 5px;
  }
  header.style-1 .main-nav ul li i {
    display: block;
  }
  header.style-1 .main-nav ul li a {
    padding: 10px 0;
    display: block;
    font-weight: 400;
    font-size: 14px;
  }
  header.style-1 .main-nav ul li ul.sub-menu {
    position: static;
    min-width: 200px;
    background: 0 0;
    border: none;
    opacity: 1;
    visibility: visible;
    box-shadow: none;
    transform: none;
    transition: none;
    display: none;
    margin-top: 0 !important;
    transform: translateY(0px);
  }
  header.style-1 .main-nav ul li ul.sub-menu > li {
    border-bottom: 1px solid transparent;
  }
  header.style-1 .main-nav ul li ul.sub-menu > li a {
    color: var(--text-primary);
    font-size: 12px;
    font-weight: 500;
  }
  header.style-1 .main-nav ul li ul.sub-menu > li a:hover {
    color: var(--primary-color1);
    margin-left: 10px;
  }
  header.style-1 .main-nav ul li ul.sub-menu > li a.active {
    color: var(--primary-color1);
  }
  header.style-1 .main-nav ul li ul.sub-menu > li i {
    color: var(--text-primary);
    right: -13px;
  }
  header.style-1 .main-nav ul li .bi {
    top: 12px;
    font-size: 12px;
  }
  header.style-1 .mobile-menu {
    position: relative;
    top: 2px;
    padding: 0 5px;
    border-radius: 50%;
    display: inline-block;
  }
  header.style-1 .mobile-menu-btn i {
    font-size: 35px;
  }
  header.style-1 .cross-btn {
    display: inline-block !important;
    position: relative;
    width: 30px !important;
    height: 22px !important;
    cursor: pointer;
    border: 3px solid transparent !important;
  }
  header.style-1 .cross-btn span {
    width: 100%;
    height: 2px;
    background: var(--primary-color1);
    display: block;
    position: absolute;
    right: 0;
    transition: all 0.3s;
  }
  header.style-1 .cross-btn .cross-top {
    top: 0;
  }
  header.style-1 .cross-btn .cross-middle {
    top: 50%;
    transform: translateY(-50%);
    width: 100%;
  }
  header.style-1 .cross-btn .cross-bottom {
    bottom: 0;
    width: 100%;
  }
  header.style-1 .cross-btn.h-active span.cross-top {
    transform: rotate(45deg);
    top: 50%;
    margin-top: -1px;
  }
  header.style-1 .cross-btn.h-active span.cross-middle {
    transform: translateX(-30px);
    opacity: 0;
  }
  header.style-1 .cross-btn.h-active span.cross-bottom {
    transform: rotate(-45deg);
    bottom: 50%;
    margin-bottom: -1px;
  }
}
header.style-1 .sidebar-button {
  display: flex;
  flex-direction: column;
  gap: 7px;
  cursor: pointer;
  align-items: flex-end;
  text-align: right;
  z-index: 9;
  position: relative;
}
header.style-1 .sidebar-button span {
  display: inline-block;
  width: 40px;
  height: 2px;
  border-radius: 3px;
  background-color: var(--text-primary);
  transition: all 0.5s ease;
}
header.style-1 .sidebar-button span:nth-child(2) {
  width: 25px;
}
header.style-1 .sidebar-button span:last-child {
  width: 30px;
}
header.style-1 .sidebar-button:hover span:nth-child(2) {
  width: 40px;
}
header.style-1 .sidebar-button:hover span:nth-child(3) {
  width: 40px;
}

.logo-area {
  transform: translateY(65px);
}
@media (max-width: 576px) {
  .logo-area {
    transform: translateY(0px);
  }
}
.logo-area img {
  max-width: 280px;
}
@media (max-width: 576px) {
  .logo-area img {
    max-width: 100px;
  }
}

.logo-section .logo-area {
  transform: translateY(0px);
}

ul.language-list {
  list-style: none;
  margin: 0;
  padding: 0;
  padding-right: 60px;
}
@media (max-width: 1199px) {
  ul.language-list {
    display: none;
    visibility: hidden;
  }
}
ul.language-list > li {
  display: inline-block;
  position: relative;
  padding: 0 10px;
}
ul.language-list > li:last-child::after {
  content: none;
}
ul.language-list > li::after {
  content: "";
  position: absolute;
  top: 35px;
  right: 0;
  width: 1px;
  height: 12px;
  background-color: var(--text-primary);
}
@media (max-width: 991px) {
  ul.language-list > li::after {
    content: none;
  }
}
ul.language-list > li a {
  font-size: 13px;
  color: var(--text-primary);
  text-decoration: none;
  font-weight: 600;
  display: block;
  text-transform: uppercase;
  padding: 30px 0;
  position: relative;
  font-family: var(--font-work-sans);
  transition: all 0.5s ease-out 0s;
  position: relative;
}
ul.language-list > li a:hover {
  color: var(--primary-one);
}
ul.language-list > li a:hover:after {
  opacity: 1;
  width: 100%;
}
ul.language-list > li a::after {
  content: "";
  position: absolute;
  bottom: 25px;
  left: 0;
  width: 0%;
  height: 2px;
  border-radius: 30px;
  display: block;
  background: linear-gradient(90deg, var(--primary-color1), transparent);
  opacity: 0;
  transition: all 0.5s ease-out 0s;
}
ul.language-list > li a.active {
  color: var(--primary-one);
}
ul.language-list > li i {
  width: 30px;
  font-size: 14px;
  text-align: center;
  color: var(--text-primary);
  font-style: normal;
  position: absolute;
  right: -8px;
  top: 31px;
  z-index: 999;
  cursor: pointer;
  display: none;
  opacity: 0;
}
@media (max-width: 991px) {
  ul.language-list > li i {
    color: var(--text-primary);
    opacity: 1;
  }
}
ul.language-list.style-white li a {
  color: var(--white);
}
ul.language-list.style-white li::after {
  background-color: var(--white);
}

/*=======================================
 12 .Banner-section
=======================================*/
.banner-section {
  position: relative;
  background-size: cover;
  background-repeat: no-repeat;
  height: 100vh;
  position: relative;
  background-position: top center;
  z-index: 1;
}
@media (min-width: 992px) and (max-width: 1199px) {
  .banner-section {
    height: 700px;
  }
}
@media (min-width: 768px) and (max-width: 991px) {
  .banner-section {
    height: 600px;
    overflow: hidden;
  }
}
@media (max-width: 767px) {
  .banner-section {
    overflow: hidden;
    height: 550px;
  }
}
.banner-section .swiper-slide {
  position: relative;
}
.banner-section .swiper-slide img {
  max-height: auto;
}

.swiper-pagination-bullets.swiper-pagination-horizontal {
  bottom: 0px;
  left: 59%;
  width: 100%;
  max-width: 450px;
  background-color: red;
  z-index: 3;
  position: absolute;
  height: 200px;
  padding: 70px 35px;
}
@media (max-width: 991px) {
  .swiper-pagination-bullets.swiper-pagination-horizontal {
    height: 100px;
    padding: 25px 35px;
    width: 50%;
    left: 50%;
  }
}
.swiper-pagination-bullets.swiper-pagination-horizontal::after {
  content: "";
  position: absolute;
  top: 100%;
  right: 0%;
  width: 100%;
  max-width: 450px;
  height: 270px;
  background-color: red;
  display: block;
  z-index: 9;
}
.swiper-pagination-bullets.swiper-pagination-horizontal .swiper-pagination-bullet, .swiper-pagination-bullets.swiper-pagination-horizontal .swiper-pagination-bullet-active {
  width: 16px;
  height: 16px;
  border-radius: 0px;
  background: #fff;
  opacity: 1;
  border: 1px solid #fff;
}
.swiper-pagination-bullets.swiper-pagination-horizontal .swiper-pagination-bullet-active {
  background-color: transparent;
}

.banner-slider-content {
  position: relative;
  z-index: 2;
  height: 100vh;
  display: flex;
  justify-content: center;
  align-items: center;
}
.banner-slider-content h1 {
  color: var(--white);
  font-size: 100px;
  font-weight: 300;
}
@media (max-width: 576px) {
  .banner-slider-content h1 {
    font-size: 50px;
  }
}
@media (min-width: 576px) and (max-width: 768px) {
  .banner-slider-content h1 {
    font-size: 55px;
  }
}
@media (min-width: 768px) and (max-width: 991px) {
  .banner-slider-content h1 {
    font-size: 60px;
  }
}
@media (min-width: 992px) and (max-width: 1199px) {
  .banner-slider-content h1 {
    font-size: 70px;
  }
}
@media (min-width: 1200px) and (max-width: 1399px) {
  .banner-slider-content h1 {
    font-size: 85px;
  }
}

.banner-content.style-dark h1 {
  color: var(--text-primary);
}
.banner-content h1 {
  color: var(--white);
  font-size: 100px;
  font-weight: 300;
}
@media (max-width: 576px) {
  .banner-content h1 {
    font-size: 50px;
  }
}
@media (min-width: 576px) and (max-width: 768px) {
  .banner-content h1 {
    font-size: 55px;
  }
}
@media (min-width: 768px) and (max-width: 991px) {
  .banner-content h1 {
    font-size: 60px;
  }
}
@media (min-width: 992px) and (max-width: 1199px) {
  .banner-content h1 {
    font-size: 70px;
  }
}
@media (min-width: 1200px) and (max-width: 1399px) {
  .banner-content h1 {
    font-size: 85px;
  }
}

.right-box {
  position: relative;
  height: 135px;
  width: 100%;
  border: 1px solid var(--border);
  position: relative;
  z-index: 1;
}
@media (max-width: 991px) {
  .right-box {
    display: none;
    visibility: hidden;
  }
}
.right-box::after {
  content: "";
  position: absolute;
  left: 60px;
  bottom: 60px;
  width: 100%;
  background-color: var(--primary-yellow);
  height: 270px;
}
@media (min-width: 992px) and (max-width: 1199px) {
  .right-box::after {
    left: 25px;
  }
}
@media (min-width: 1200px) and (max-width: 1399px) {
  .right-box::after {
    left: 25px;
  }
}

.right-box-two {
  position: relative;
  height: 470px;
  background-color: var(--primary-yellow);
  width: 100%;
  max-width: 570px;
  position: relative;
  z-index: 1;
  transform: translate(60px, -200px);
}
@media (min-width: 992px) and (max-width: 1199px) {
  .right-box-two {
    transform: translate(20px, -150px);
  }
}
@media (max-width: 991px) {
  .right-box-two {
    transform: translate(0px, 0px);
    margin-top: 50px;
    height: 360px;
  }
}
.right-box-two.style-blue {
  height: 270px;
  background-color: var(--primary-blue);
}

.banner-video-section {
  position: relative;
}
.banner-video-section .scroll-down {
  position: absolute;
  left: 50%;
  transform: translateX(-50%);
  bottom: 80px;
  z-index: 2;
  animation: jump 3s linear infinite alternate;
}
@media (max-width: 576px) {
  .banner-video-section .scroll-down {
    bottom: 30px;
    max-width: 25px;
  }
}
.banner-video-section .scroll-down:hover {
  animation-play-state: paused;
}
@keyframes jump {
  0% {
    transform: translate(-50%, 0);
  }
  25% {
    transform: translate(-50%, 10px);
  }
  50% {
    transform: translate(-50%, 0);
  }
  75% {
    transform: translate(-50%, -10px);
  }
  100% {
    transform: translate(-50%, 0);
  }
}

/*=======================================
 12 .Content-section
=======================================*/
.content-section .content {
  width: 100%;
  max-width: 500px;
}
@media (max-width: 991px) {
  .content-section .content {
    max-width: 100%;
  }
}
.content-section .content h6 {
  margin-bottom: 0px;
  font-size: 16px;
  font-weight: 600;
}

/*=======================================
 12 .Team-section
=======================================*/
.team-item {
  position: relative;
}
.team-item.style-two .team-image {
  overflow: hidden;
  height: 155px;
}
@media (max-width: 576px) {
  .team-item.style-two .team-image {
    height: auto;
  }
}
.team-item.style-two .team-image img {
  transition: all 0.5s;
  transform: scale(1);
  width: 100%;
  -o-object-fit: cover;
     object-fit: cover;
}
.team-item.style-two .team-content {
  padding-top: 8px;
}
.team-item.style-two .team-content h6 {
  font-weight: 700;
}
.team-item.style-two .team-content h6 a {
  color: inherit;
}
.team-item.style-two .team-content span {
  font-weight: 300;
  line-height: 1.4;
}
.team-item .team-tag {
  position: absolute;
  display: inline-block;
  left: 0;
  top: 0;
  min-width: 30px;
  padding: 5px;
  background-color: var(--text-primary);
  color: var(--white);
  font-size: 12px;
  font-weight: 700;
  text-align: center;
  z-index: 1;
}
.team-item .team-image {
  overflow: hidden;
}
@media (max-width: 576px) {
  .team-item .team-image {
    height: auto;
  }
}
.team-item .team-image img {
  transition: all 0.5s;
  transform: scale(1);
  width: 100%;
  -o-object-fit: cover;
     object-fit: cover;
  height: 155px;
}
@media (max-width: 576px) {
  .team-item .team-image img {
    height: auto;
  }
}
.team-item:hover .team-image img {
  transform: scale(1.1);
}
.team-item .team-content {
  padding-top: 8px;
}
.team-item .team-content h6 {
  font-size: 14px;
  color: var(--text-primary);
  line-height: 1.2;
  margin-bottom: 3px;
}
@media (min-width: 768px) and (max-width: 991px) {
  .team-item .team-content h6 {
    font-size: 12px;
  }
}
.team-item .team-content h6 a {
  color: inherit;
}
.team-item .team-content span {
  display: block;
  font-size: 9px;
  color: var(--text-primary);
  text-transform: uppercase;
}
@media (max-width: 767px) {
  .team-item .team-content span {
    font-size: 9px;
  }
}
.team-item.team-sidebar {
  width: 100%;
  max-width: 260px;
}

/*=======================================
 13 .sector-section
=======================================*/
.sector-section-v {
  position: relative;
}
@media (max-width: 576px) {
  .sector-section-v {
    padding-bottom: 0px;
  }
}

.sector-item {
  background-color: var(--primary-green);
  position: relative;
  padding: 15px;
  text-align: left;
  min-height: 270px;
  transition: 0.5s;
}
@media (min-width: 992px) and (max-width: 1199px) {
  .sector-item {
    min-height: 290px;
  }
}
@media (max-width: 576px) {
  .sector-item {
    padding: 10px;
    min-height: 230px;
  }
}
.sector-item .arrow-btn {
  position: absolute;
  left: 20px;
  bottom: 20px;
  z-index: 2;
  transition: 0.4s ease;
}
@media (max-width: 576px) {
  .sector-item .arrow-btn {
    max-width: 25px;
  }
}
.sector-item .arrow-btn:hover {
  transform: rotate(-45deg);
}
.sector-item:hover {
  background-color: #3AAA35;
}
.sector-item .title {
  font-size: 16px;
  font-weight: 700;
  color: var(--white);
  margin-bottom: 0px;
  padding-right: 10%;
}
@media (max-width: 576px) {
  .sector-item .title {
    font-size: 13px;
  }
}
.sector-item p {
  font-size: 15px;
  font-weight: 300;
  color: var(--white);
  margin-bottom: 50px;
}
@media (max-width: 576px) {
  .sector-item p {
    font-size: 13px;
  }
}
.sector-item.style-yellow {
  background-color: var(--primary-yellow);
}
.sector-item.style-yellow:hover {
  background-color: #000;
}
.sector-item.style-blue {
  background-color: var(--primary-blue);
}
.sector-item.style-blue:hover {
  background-color: #000;
}

.sector-item-two {
  background-color: var(--primary-green);
  position: relative;
  padding: 15px;
  text-align: left;
  min-height: 270px;
  transition: 0.5s;
}
@media (min-width: 992px) and (max-width: 1199px) {
  .sector-item-two {
    min-height: 290px;
  }
}
@media (max-width: 576px) {
  .sector-item-two {
    padding: 10px;
    min-height: 230px;
  }
}
.sector-item-two .arrow-btn {
  position: absolute;
  left: 20px;
  bottom: 20px;
  z-index: 2;
  transition: 0.4s ease;
}
@media (max-width: 576px) {
  .sector-item-two .arrow-btn {
    max-width: 25px;
  }
}
.sector-item-two .arrow-btn:hover {
  transform: rotate(-45deg);
}
.sector-item-two:hover {
  background-color: #3AAA35;
}
.sector-item-two .title {
  font-size: 15px;
  font-weight: 700;
  color: var(--white);
  margin-bottom: 0px;
  padding-right: 10%;
}
@media (max-width: 576px) {
  .sector-item-two .title {
    font-size: 13px;
  }
}
.sector-item-two p {
  font-size: 15px;
  font-weight: 300;
  color: var(--white);
  margin-bottom: 20px;
}
@media (max-width: 576px) {
  .sector-item-two p {
    font-size: 13px;
  }
}
.sector-item-two span.date {
  font-size: 13px;
  text-transform: uppercase;
  font-weight: 600;
  color: var(--white);
  display: inline-block;
  position: relative;
  padding-top: 20px;
  margin-bottom: 60px;
}
.sector-item-two span.date::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 90px;
  height: 1px;
  background-color: var(--white);
  display: block;
}
.sector-item-two.style-yellow {
  background-color: var(--primary-yellow);
}
.sector-item-two.style-yellow:hover {
  background-color: #000;
}
.sector-item-two.style-blue {
  background-color: var(--primary-blue);
}
.sector-item-two.style-blue:hover {
  background-color: #000;
}

/*=======================================
 13 .Service-section
=======================================*/
.service-section-1 {
  overflow: hidden;
  position: relative;
  z-index: 1;
}
.service-section-1::before {
  content: "";
  position: absolute;
  top: 30px;
  right: 100px;
  width: 100%;
  max-width: 730px;
  height: 340px;
  background-color: var(--primary-blue);
  display: block;
  z-index: -1;
}

.service-item {
  position: relative;
  width: 100%;
  max-width: 320px;
}
.service-item:hover .image img {
  transform: scale(1.2);
}
.service-item .image {
  margin-bottom: 15px;
  overflow: hidden;
}
.service-item .image img {
  transition: 0.62s;
  height: 260px;
  -o-object-fit: cover;
     object-fit: cover;
  width: 100%;
}
@media (min-width: 1200px) and (max-width: 1399px) {
  .service-item .image img {
    height: 250px;
  }
}
@media (min-width: 992px) and (max-width: 1199px) {
  .service-item .image img {
    height: 240px;
  }
}
@media (min-width: 768px) and (max-width: 991px) {
  .service-item .image img {
    height: 220px;
  }
}
@media (max-width: 767px) {
  .service-item .image img {
    height: 160px;
  }
}
.service-item .content {
  text-align: left;
  min-height: 100px;
}
.service-item .content h6 {
  margin-bottom: 0px;
}
.service-item .content h6 a {
  font-size: 16px;
  color: var(--primary-red);
  text-transform: uppercase;
  font-weight: 700;
}
@media (max-width: 767px) {
  .service-item .content h6 a {
    font-size: 14px;
  }
}
.service-item .content p {
  font-size: 15px;
  font-weight: 300;
  color: var(--text-primary);
  margin-bottom: 20px;
}
@media (max-width: 767px) {
  .service-item .content p {
    font-size: 14px;
  }
}
.service-item .content > a {
  display: inline-block;
  font-size: 14px;
  text-transform: uppercase;
  color: var(--text-primary);
  font-weight: 700;
  position: relative;
  padding-top: 12px;
  transition: 0.45s ease;
}
@media (max-width: 767px) {
  .service-item .content > a {
    font-size: 12px;
  }
}
.service-item .content > a:hover {
  letter-spacing: 1px;
  color: var(--primary-red);
}
.service-item .content > a::before {
  content: "";
  position: absolute;
  left: 0;
  top: 0;
  width: 50px;
  height: 1px;
  background-color: var(--text-primary);
}
.service-item .content > a i {
  font-size: 12px;
  font-weight: 600;
  display: inline-block;
  margin-right: 5px;
}

.service-details-wrap {
  background-color: var(--primary-blue);
  padding: 60px 100px;
}
@media (max-width: 767px) {
  .service-details-wrap {
    padding: 20px 15px;
  }
}
.service-details-wrap h6 {
  color: var(--white);
  margin-bottom: 0px;
}
.service-details-wrap P {
  color: var(--white);
}

.service-details-section {
  padding-right: 80px;
}
@media (max-width: 991px) {
  .service-details-section {
    padding-right: unset;
  }
}

/*=======================================
 13 .insight-section
=======================================*/
.bg-light-box {
  position: relative;
  z-index: 1;
}
.bg-light-box::before {
  content: "";
  position: absolute;
  bottom: -100px;
  right: 0px;
  width: 75%;
  height: 370px;
  background-color: var(--primary-yellow-light2);
  display: block;
  z-index: -1;
}

.insight-item {
  position: relative;
  width: 100%;
  max-width: 320px;
}
.insight-item.style-blue .content h6 a {
  color: var(--primary-blue);
}
.insight-item.style-blue .content > a:hover {
  color: var(--primary-blue);
}
.insight-item.style-green .content h6 a {
  color: var(--primary-green-dark);
}
.insight-item.style-green .content > a:hover {
  color: var(--primary-green-dark);
}
.insight-item.style-yellow .content h6 a {
  color: var(--primary-yellow);
}
.insight-item.style-yellow .content > a:hover {
  color: var(--primary-yellow);
}
.insight-item:hover .image img {
  transform: scale(1.2);
}
.insight-item .image {
  margin-bottom: 15px;
  overflow: hidden;
}
.insight-item .image img {
  transition: 0.62s;
  height: 210px;
  -o-object-fit: cover;
     object-fit: cover;
}
@media (min-width: 992px) and (max-width: 1199px) {
  .insight-item .image img {
    height: 220px;
  }
}
@media (max-width: 576px) {
  .insight-item .image img {
    height: 140px;
  }
}
.insight-item .content {
  text-align: left;
  padding-right: 40px;
}
@media (max-width: 767px) {
  .insight-item .content {
    padding-right: 0px;
  }
}
.insight-item .content h6 {
  margin-bottom: 5px;
}
.insight-item .content h6 a {
  font-size: 16px;
  color: var(--primary-red);
  text-transform: uppercase;
  font-weight: 700;
}
@media (max-width: 767px) {
  .insight-item .content h6 a {
    font-size: 14px;
  }
}
.insight-item .content p {
  font-size: 15px;
  font-weight: 300;
  color: var(--text-primary);
  margin-bottom: 20px;
}
@media (max-width: 767px) {
  .insight-item .content p {
    font-style: 14px;
  }
}
.insight-item .content > a {
  display: inline-block;
  font-size: 14px;
  text-transform: uppercase;
  color: var(--text-primary);
  font-weight: 700;
  position: relative;
  padding-top: 12px;
  transition: 0.45s ease;
}
@media (max-width: 767px) {
  .insight-item .content > a {
    font-size: 12px;
  }
}
.insight-item .content > a:hover {
  letter-spacing: 1px;
  color: var(--primary-red);
}
.insight-item .content > a::before {
  content: "";
  position: absolute;
  left: 0;
  top: 0;
  width: 50px;
  height: 1px;
  background-color: var(--text-primary);
}

/*=======================================
 13 Blog-section
=======================================*/
.blog-item {
  border: 1px solid var(--border);
}
.blog-item:hover .image img {
  transform: scale(1.2);
}
.blog-item .content {
  padding: 15px;
  background-color: var(--white);
}
@media (max-width: 767px) {
  .blog-item .content {
    padding: 15px 10px;
  }
}
.blog-item .content h5 a {
  color: var(--primary-red);
  font-size: 17px;
  font-weight: 300;
}
@media (max-width: 767px) {
  .blog-item .content h5 a {
    font-size: 15px;
  }
}
.blog-item .content P {
  font-size: 14px;
}
.blog-item header span {
  display: block;
  font-weight: 400;
}
.blog-item header span:first-child {
  font-size: 13px;
  color: var(--primary-red);
}
.blog-item header span:last-child {
  font-size: 12px;
  color: var(--text-primary);
}
.blog-item ul.blog-icon-list {
  list-style: none;
  margin: 0;
  padding: 0;
  display: flex;
  flex-direction: row;
  justify-content: flex-end;
  gap: 5px;
  position: relative;
  z-index: 1;
  padding-right: 5px;
  margin-bottom: 5px;
}
.blog-item ul.blog-icon-list::after {
  content: "";
  position: absolute;
  top: 15px;
  right: 0;
  display: block;
  width: 100%;
  height: 1px;
  background-color: var(--border);
  z-index: -1;
}
@media (max-width: 767px) {
  .blog-item ul.blog-icon-list::after {
    top: 13px;
  }
}
.blog-item ul.blog-icon-list li {
  width: 30px;
  height: 30px;
  line-height: 25px;
  border: 1px solid var(--border);
  border-radius: 50%;
  text-align: center;
  background-color: var(--white);
  transition: 0.4s;
}
@media (max-width: 767px) {
  .blog-item ul.blog-icon-list li {
    width: 25px;
    height: 25px;
    line-height: 20px;
  }
}
.blog-item ul.blog-icon-list li:hover {
  border: 1px solid var(--primary-red);
  background-color: var(--primary-red);
}
.blog-item ul.blog-icon-list li:hover i {
  color: var(--white);
}
.blog-item ul.blog-icon-list li a i {
  font-size: 21px;
  transition: 0.4s;
  color: var(--text-primary);
}
@media (max-width: 767px) {
  .blog-item ul.blog-icon-list li a i {
    font-size: 16px;
  }
}
.blog-item .image {
  overflow: hidden;
}
.blog-item .image img {
  transition: 0.62s;
  height: 270px;
  -o-object-fit: cover;
     object-fit: cover;
}
@media (min-width: 992px) and (max-width: 1199px) {
  .blog-item .image img {
    height: 220px;
  }
}
@media (max-width: 576px) {
  .blog-item .image img {
    height: 140px;
  }
}

/*=======================================
 13 .software-section
=======================================*/
.software-section {
  padding-left: 7%;
  padding-right: 7%;
}
@media (max-width: 991px) {
  .software-section {
    padding-left: 0;
    padding-right: 0;
  }
}

.software-content {
  width: 100%;
  max-width: 520px;
  margin-left: 0;
  margin-right: auto;
}

/*=======================================
 13 .Footer-section
=======================================*/
footer {
  background-color: var(--text-primary);
  padding: 20px 10px;
}

.footer-list {
  list-style: none;
  margin: 0;
  padding: 0;
}
.footer-list li {
  transition: 0.4s ease;
}
.footer-list li:hover a {
  color: var(--primary-red);
  padding-left: 3px;
  opacity: 1;
}
.footer-list a {
  font-size: 12px;
  font-weight: 300;
  color: #dddddd;
  transition: 0.4s;
}
.footer-list .footer-social {
  margin-top: 15px;
}
.footer-list .footer-social:hover i {
  background-color: var(--primary-blue);
  color: var(--white);
}
.footer-list .footer-social i {
  font-size: 18px;
  width: 25px;
  height: 25px;
  line-height: 25px;
  border-radius: 50%;
  background-color: var(--white);
  color: var(--text-primary);
  text-align: center;
}

.footer-address h6 {
  font-size: 12px;
  font-weight: 700;
  color: var(--white);
  margin-bottom: 3px;
}
.footer-address p {
  margin-bottom: 0;
  font-size: 12px;
  font-weight: 300;
  color: #dddddd;
  transition: 0.4s;
}

.contact-list {
  list-style: none;
  margin: 0;
  padding: 0;
  margin-top: 15px;
}
.contact-list li a {
  font-size: 12px;
  font-weight: 300;
  color: var(--white);
  transition: 0.4s;
}

.footer-col-wrap {
  display: grid;
  grid-template-columns: repeat(8, 1fr);
  grid-gap: 20px;
}
@media (min-width: 992px) and (max-width: 1199px) {
  .footer-col-wrap {
    grid-template-columns: repeat(4, 1fr);
  }
}
@media (min-width: 768px) and (max-width: 991px) {
  .footer-col-wrap {
    grid-template-columns: repeat(4, 1fr);
  }
}
@media (min-width: 576px) and (max-width: 768px) {
  .footer-col-wrap {
    grid-template-columns: repeat(3, 1fr);
  }
}
@media (max-width: 576px) {
  .footer-col-wrap {
    grid-template-columns: repeat(2, 1fr);
  }
}

.footer-bottom {
  margin-top: 45px;
  font-size: 12px;
  font-weight: 300;
  color: #dddddd;
}
.footer-bottom p {
  font-size: 11px;
}

.footer-top-design {
  padding-top: 110px;
  margin-top: 30px;
}
.footer-top-design.sibling-two .footer-box::after {
  content: "";
  position: absolute;
  right: -80%;
  bottom: 0;
  width: 100%;
  height: 200px;
  background-color: var(--primary-green-light);
  display: block;
}
@media (min-width: 992px) and (max-width: 1199px) {
  .footer-top-design.sibling-two .footer-box::after {
    right: -70%;
  }
}
.footer-top-design .footer-box {
  width: 100%;
  max-width: 480px;
  height: 70px;
  background-color: var(--primary-red);
  display: block;
  margin-left: 100px;
  margin-right: auto;
  position: relative;
}
@media (max-width: 576px) {
  .footer-top-design .footer-box {
    max-width: 150px;
    height: 150px;
    margin-left: 0px;
  }
}
.footer-top-design .footer-box::before {
  content: "";
  position: absolute;
  left: 50%;
  bottom: 0;
  width: 100%;
  max-width: 480px;
  height: 160px;
  background-color: transparent;
  border: 1px solid var(--border);
  display: block;
}
.footer-top-design .footer-box::after {
  content: "";
  position: absolute;
  right: -80%;
  bottom: 0;
  width: 100%;
  max-width: 480px;
  height: 200px;
  background-color: var(--primary-red-light);
  display: block;
}
@media (min-width: 992px) and (max-width: 1199px) {
  .footer-top-design .footer-box::after {
    right: -70%;
  }
}

.footer-top-design-two {
  overflow: hidden;
  padding-top: 100px;
}
.footer-top-design-two .footer-box {
  width: 100%;
  max-width: 640px;
  height: 180px;
  background-color: var(--primary-red);
  display: block;
  margin-left: 100px;
  margin-right: auto;
  position: relative;
  z-index: 1;
}
@media (max-width: 576px) {
  .footer-top-design-two .footer-box {
    max-width: 150px;
    height: 150px;
    margin-left: 0px;
  }
}
@media (min-width: 576px) and (max-width: 768px) {
  .footer-top-design-two .footer-box {
    max-width: 400px;
    margin-left: 0px;
  }
}
@media (min-width: 768px) and (max-width: 991px) {
  .footer-top-design-two .footer-box {
    max-width: 440px;
    margin-left: 0px;
  }
}
@media (min-width: 992px) and (max-width: 1199px) {
  .footer-top-design-two .footer-box {
    max-width: 520px;
    margin-left: 0px;
  }
}
.footer-top-design-two .footer-box::before {
  content: "";
  position: absolute;
  left: 80%;
  bottom: 0;
  width: 100%;
  max-width: 980px;
  height: 70px;
  background-color: transparent;
  border: 1px solid var(--border);
  display: block;
  z-index: -1;
}
@media (min-width: 576px) and (max-width: 768px) {
  .footer-top-design-two .footer-box::before {
    left: 50%;
    max-width: 500px;
  }
}

.footer-top-design-three {
  padding-top: 100px;
  margin-top: 100px;
}
.footer-top-design-three .footer-box {
  width: 100%;
  max-width: 640px;
  height: 100px;
  background-color: var(--primary-red);
  display: block;
  margin-left: 100px;
  margin-right: auto;
  position: relative;
  z-index: 2;
}
@media (min-width: 768px) and (max-width: 991px) {
  .footer-top-design-three .footer-box {
    max-width: 500px;
  }
}
@media (min-width: 576px) and (max-width: 768px) {
  .footer-top-design-three .footer-box {
    max-width: 440px;
    height: 100px;
    background-color: var(--primary-red);
    margin-left: 30px;
  }
}
@media (max-width: 576px) {
  .footer-top-design-three .footer-box {
    max-width: 250px;
    margin-left: 0px;
  }
}
.footer-top-design-three .footer-box::before {
  content: "";
  position: absolute;
  left: 80%;
  bottom: 0;
  width: 100%;
  max-width: 980px;
  height: 280px;
  background-color: transparent;
  border: 1px solid var(--border);
  display: block;
  z-index: 1;
}
@media (min-width: 1400px) and (max-width: 1599px) {
  .footer-top-design-three .footer-box::before {
    left: 65%;
  }
}
@media (min-width: 1200px) and (max-width: 1399px) {
  .footer-top-design-three .footer-box::before {
    max-width: 530px;
    left: 60%;
  }
}
@media (min-width: 992px) and (max-width: 1199px) {
  .footer-top-design-three .footer-box::before {
    max-width: 340px;
  }
}
@media (min-width: 768px) and (max-width: 991px) {
  .footer-top-design-three .footer-box::before {
    left: 45%;
    max-width: 340px;
  }
}
@media (min-width: 576px) and (max-width: 768px) {
  .footer-top-design-three .footer-box::before {
    left: 40%;
    bottom: 0;
    width: 100%;
    max-width: 320px;
  }
}
@media (max-width: 576px) {
  .footer-top-design-three .footer-box::before {
    max-width: 150px;
    left: 50%;
    height: 200px;
  }
}

.footer-top-design-four {
  position: relative;
  padding-left: 10%;
  padding-right: 10%;
}
@media (max-width: 767px) {
  .footer-top-design-four {
    padding-left: 5%;
    padding-right: 5%;
  }
}
.footer-top-design-four .main-box {
  width: 100%;
  max-width: 670px;
  height: 280px;
  background-color: var(--primary-red-light);
}
.footer-top-design-four .main-box::before {
  content: "";
  position: absolute;
  right: 10%;
  bottom: 0;
  border: 1px solid var(--border);
  width: 100%;
  max-width: 60%;
  height: 190px;
}
.footer-top-design-four .small-box {
  width: 37%;
  height: 90px;
  background-color: var(--primary-red);
  position: absolute;
  bottom: 0;
  left: 20%;
  z-index: 2;
}

.footer-top-design-five {
  position: relative;
  padding-left: 10%;
  padding-right: 10%;
}
@media (max-width: 767px) {
  .footer-top-design-five {
    padding-left: 5%;
    padding-right: 5%;
  }
}
.footer-top-design-five .main-box {
  width: 100%;
  max-width: 670px;
  height: 190px;
}
.footer-top-design-five .main-box::before {
  content: "";
  position: absolute;
  right: 10%;
  bottom: 0;
  border: 1px solid var(--border);
  width: 100%;
  max-width: 50%;
  height: 190px;
}
.footer-top-design-five .small-box {
  width: 37%;
  height: 90px;
  background-color: var(--primary-red);
  position: absolute;
  bottom: 0;
  left: 20%;
  z-index: 2;
}

.footer-top-design-six .footer-box {
  width: 28%;
  height: 180px;
  display: block;
  margin-left: 27%;
  margin-right: auto;
  position: relative;
  z-index: 1;
  background-color: transparent;
  border-left: 1px solid var(--border);
  border-right: 1px solid var(--border);
}
@media (max-width: 991px) {
  .footer-top-design-six .footer-box {
    margin-left: 10%;
    width: 50%;
  }
}
.footer-top-design-six .footer-box::before {
  content: "";
  position: absolute;
  left: 80%;
  bottom: 0;
  width: 100%;
  max-width: 980px;
  height: 70px;
  background-color: var(--primary-red);
  display: block;
  z-index: -1;
}

.footer-top-design-seven .footer-box {
  width: 28%;
  height: 180px;
  display: block;
  margin-left: 27%;
  margin-right: auto;
  position: relative;
  z-index: 1;
  background-color: transparent;
  border-left: 1px solid var(--border);
  border-right: 1px solid var(--border);
}
@media (max-width: 991px) {
  .footer-top-design-seven .footer-box {
    margin-left: 10%;
    width: 50%;
  }
}
.footer-top-design-seven .footer-box::before {
  content: "";
  position: absolute;
  left: 80%;
  bottom: 0;
  width: 100%;
  max-width: 980px;
  height: 70px;
  background-color: var(--primary-red);
  display: block;
  z-index: -1;
}

/*=======================================
 13 .Expertise-section
=======================================*/
.expertise-section-1 {
  margin-top: 200px;
}
@media (min-width: 768px) and (max-width: 991px) {
  .expertise-section-1 {
    margin-top: 70px;
  }
}
@media (max-width: 767px) {
  .expertise-section-1 {
    margin-top: 60px;
  }
}
.expertise-section-1 .right-box-blue {
  position: relative;
  height: 200px;
  background-color: var(--primary-blue);
  width: 100%;
  max-width: 340px;
  left: 0;
  margin-left: 40px;
  z-index: 1;
  margin-top: -90px;
}
@media (max-width: 991px) {
  .expertise-section-1 .right-box-blue {
    margin-top: 110px;
    margin-bottom: 110px;
    margin-left: 0;
    max-width: 240px;
  }
}
.expertise-section-1 .right-box-blue::before {
  content: "";
  position: absolute;
  top: 50%;
  right: -100px;
  transform: translateY(-50%);
  width: 100%;
  max-width: 270px;
  height: 340px;
  background-color: var(--primary-blue-light);
  display: block;
  z-index: -1;
}
@media (min-width: 992px) and (max-width: 1199px) {
  .expertise-section-1 .right-box-blue::before {
    right: -20px;
  }
}
@media (max-width: 991px) {
  .expertise-section-1 .right-box-blue::before {
    right: -50px;
  }
}

.search-section {
  background-color: var(--primary-yellow-light2);
  padding: 90px 10px;
}
@media (min-width: 768px) and (max-width: 991px) {
  .search-section {
    padding: 70px 10px;
  }
}
@media (max-width: 767px) {
  .search-section {
    padding: 60px 10px;
  }
}
.search-section.style-blue {
  background-color: var(--primary-blue-light);
}
.search-section.style-green {
  background-color: var(--primary-green-dark-light);
}
.search-section.with-box {
  position: relative;
}
.search-section.with-box::before {
  content: "";
  position: absolute;
  top: -180px;
  left: 27%;
  height: 180px;
  width: 28%;
  border: 1px solid var(--border);
  border-bottom: unset;
}
@media (max-width: 991px) {
  .search-section.with-box::before {
    left: 10%;
    width: 50%;
  }
}

.search-block {
  background-color: var(--white);
  padding: 20px 30px;
  display: flex;
  justify-content: flex-start;
  align-items: center;
}
@media (max-width: 767px) {
  .search-block {
    padding: 10px 15px;
  }
}
.search-block button {
  background-color: transparent;
}
.search-block button:hover i {
  color: var(--primary-red);
}
.search-block button i {
  font-size: 25px;
  color: var(--text-primary);
  margin-right: 10px;
  transition: 0.4s;
}
@media (max-width: 767px) {
  .search-block button i {
    font-size: 20px;
  }
}
.search-block input, .search-block .contact-form textarea, .contact-form .search-block textarea {
  outline: none;
  border: none;
  background-color: transparent;
  width: 100%;
}
.search-block input::-moz-placeholder, .search-block .contact-form textarea::-moz-placeholder, .contact-form .search-block textarea::-moz-placeholder {
  font-size: 10px;
  text-transform: uppercase;
  font-weight: 800;
  color: var(--text-primary);
}
.search-block input::placeholder, .search-block .contact-form textarea::placeholder, .contact-form .search-block textarea::placeholder {
  font-size: 10px;
  text-transform: uppercase;
  font-weight: 800;
  color: var(--text-primary);
}

.box-with-border {
  width: 100%;
  max-width: 360px;
  height: 270px;
  background-color: transparent;
  border: 1px solid var(--border);
  position: relative;
  margin-top: 135px;
}
@media (max-width: 991px) {
  .box-with-border {
    margin-top: 65px;
    margin-bottom: 40px;
    margin-left: 30px;
    max-width: 260px;
  }
}
.box-with-border::after {
  content: "";
  position: absolute;
  left: -150px;
  top: -90px;
  width: 100%;
  max-width: 340px;
  height: 270px;
  background-color: var(--primary-green-light);
}
@media (min-width: 1200px) and (max-width: 1399px) {
  .box-with-border::after {
    left: -90px;
  }
}
@media (min-width: 992px) and (max-width: 1199px) {
  .box-with-border::after {
    left: -50px;
    top: -50px;
  }
}
@media (max-width: 991px) {
  .box-with-border::after {
    left: -30px;
    top: -90px;
    margin-top: 65px;
    margin-bottom: 40px;
  }
}

.focus-list-area {
  background-color: var(--primary-yellow-light2);
  padding: 40px 45px;
  width: 100%;
  max-width: 750px;
  margin-left: 0;
  margin-right: auto;
  margin-top: 100px;
}
@media (max-width: 767px) {
  .focus-list-area {
    padding: 25px 20px;
    margin-top: 30px;
  }
}
@media (min-width: 768px) and (max-width: 991px) {
  .focus-list-area {
    padding: 25px 20px;
    margin-top: 30px;
  }
}
@media (min-width: 992px) and (max-width: 1199px) {
  .focus-list-area {
    padding: 35px 30px;
  }
}
@media (min-width: 1200px) and (max-width: 1399px) {
  .focus-list-area {
    padding: 35px 30px;
  }
}
.focus-list-area h6 {
  font-weight: 700;
  margin-bottom: 5px;
}
.focus-list-area ul {
  list-style: none;
  margin: 0;
  padding: 0;
}
.focus-list-area ul li {
  position: relative;
  margin-bottom: 3px;
}
.focus-list-area ul li:hover::before {
  opacity: 1;
}
.focus-list-area ul li:last-child {
  margin-bottom: 0px;
}
.focus-list-area ul li::before {
  content: "\f285";
  font-family: "Bootstrap-icons";
  position: absolute;
  left: -15px;
  top: 5px;
  color: var(--text-primary);
  font-weight: 700;
  font-size: 12px;
  opacity: 0;
  transition: 0.35focus-list-areas ease;
}
.focus-list-area ul a {
  color: var(--text-primary);
  font-size: 15px;
  font-weight: 500;
}

.box-design-one {
  width: 100%;
  max-width: 860px;
  background-color: var(--white);
  border: 1px solid var(--border);
  height: 190px;
  margin-left: auto;
  margin-right: auto;
  position: relative;
  z-index: 2;
  display: block;
}
@media (max-width: 991px) {
  .box-design-one {
    margin-left: auto;
    margin-right: auto;
    max-width: 80%;
    height: 80px;
    margin-top: 40px;
    margin-bottom: 55px;
  }
}
.box-design-one::before {
  content: "";
  position: absolute;
  left: -25%;
  top: -320px;
  width: 100%;
  max-width: 480px;
  height: 380px;
  background-color: var(--primary-green-dark);
  display: block;
}
@media (min-width: 1400px) and (max-width: 1599px) {
  .box-design-one::before {
    left: -15%;
  }
}
@media (min-width: 1200px) and (max-width: 1399px) {
  .box-design-one::before {
    left: -15%;
  }
}
@media (min-width: 992px) and (max-width: 1199px) {
  .box-design-one::before {
    left: -7%;
    max-width: 400px;
  }
}
@media (max-width: 991px) {
  .box-design-one::before {
    left: -30px;
    top: -90px;
    width: 100%;
    max-width: 120px;
    height: 120px;
  }
}
.box-design-one::after {
  content: "";
  position: absolute;
  right: -30%;
  bottom: -390px;
  width: 100%;
  max-width: 580px;
  height: 480px;
  background-color: var(--primary-green-light);
  display: block;
}
@media (min-width: 1400px) and (max-width: 1599px) {
  .box-design-one::after {
    right: -15%;
  }
}
@media (min-width: 1200px) and (max-width: 1399px) {
  .box-design-one::after {
    right: -15%;
  }
}
@media (min-width: 992px) and (max-width: 1199px) {
  .box-design-one::after {
    right: -7%;
    max-width: 400px;
  }
}
@media (max-width: 991px) {
  .box-design-one::after {
    right: -20px;
    bottom: -55px;
    width: 100%;
    max-width: 130px;
    height: 150px;
  }
}

.box-design-two {
  width: 380px;
  height: 280px;
  border: 1px solid var(--border);
  background-color: transparent;
  position: relative;
  margin-top: 190px;
}
@media (min-width: 992px) and (max-width: 1199px) {
  .box-design-two {
    margin-top: 140px;
  }
}
@media (min-width: 1200px) and (max-width: 1399px) {
  .box-design-two {
    margin-top: 150px;
  }
}
@media (min-width: 768px) and (max-width: 991px) {
  .box-design-two {
    margin-left: 44px;
    margin-bottom: 50px;
  }
}
@media (min-width: 576px) and (max-width: 768px) {
  .box-design-two {
    margin-left: 44px;
    margin-bottom: 50px;
  }
}
@media (max-width: 576px) {
  .box-design-two {
    width: 270px;
    height: 230px;
    margin-top: 100px;
    margin-left: 10px;
    margin-bottom: 50px;
  }
}
.box-design-two::after {
  content: "";
  position: absolute;
  width: 100%;
  width: 400px;
  height: 380px;
  background-color: var(--primary-yellow);
  bottom: 90px;
  right: 90px;
}
@media (min-width: 1400px) and (max-width: 1599px) {
  .box-design-two::after {
    width: 330px;
  }
}
@media (min-width: 1200px) and (max-width: 1399px) {
  .box-design-two::after {
    width: 360px;
    right: 50px;
    bottom: 50px;
  }
}
@media (min-width: 992px) and (max-width: 1199px) {
  .box-design-two::after {
    width: 350px;
    right: 40px;
    bottom: 40px;
  }
}
@media (min-width: 768px) and (max-width: 991px) {
  .box-design-two::after {
    width: 380px;
    right: 40px;
    bottom: 40px;
  }
}
@media (min-width: 576px) and (max-width: 768px) {
  .box-design-two::after {
    width: 380px;
    right: 40px;
    bottom: 40px;
  }
}
@media (max-width: 576px) {
  .box-design-two::after {
    width: 100%;
    width: 250px;
    height: 260px;
    bottom: 30px;
    right: 30px;
  }
}

.box-design-three {
  width: 100%;
  max-width: 660px;
  height: 460px;
  background-color: var(--primary-blue-light);
  position: absolute;
  top: -250px;
  right: 7%;
  z-index: -1;
}
@media (max-width: 767px) {
  .box-design-three {
    max-width: 300px;
    right: 3%;
  }
}

.box-design-four {
  width: 100%;
  max-width: 660px;
  height: 270px;
  background-color: var(--primary-red-light);
  position: absolute;
  top: 0px;
  left: 7%;
  z-index: -1;
}
@media (max-width: 767px) {
  .box-design-four {
    max-width: 300px;
  }
}

.box-design-five {
  width: 100%;
  max-width: 660px;
  height: 270px;
  background-color: var(--primary-blue-light);
  position: absolute;
  top: 0px;
  left: 7%;
  z-index: -1;
  height: 75%;
}
@media (max-width: 767px) {
  .box-design-five {
    max-width: 300px;
  }
}

.box-design-six {
  width: 100%;
  max-width: 540px;
  height: 370px;
  background-color: var(--primary-yellow-light2);
  position: absolute;
  top: 45px;
  right: 7%;
  z-index: -1;
  height: 75%;
}
@media (max-width: 767px) {
  .box-design-six {
    max-width: 300px;
  }
}

.box-design-seven {
  width: 100%;
  max-width: 960px;
  height: 370px;
  background-color: var(--primary-blue-light);
  position: absolute;
  top: 0px;
  right: 7%;
  z-index: -1;
}
@media (max-width: 767px) {
  .box-design-seven {
    max-width: 300px;
  }
}

.box-design-eight {
  max-width: 90%;
  width: 100%;
  height: 60%;
  background-color: var(--primary-green-light);
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  left: 0%;
  z-index: -1;
}
@media (max-width: 767px) {
  .box-design-eight {
    max-width: 300px;
  }
}

.box-design-nine {
  max-width: 60%;
  width: 100%;
  height: 100%;
  background-color: var(--primary-blue-light);
  position: absolute;
  top: 0;
  bottom: 0;
  right: 0%;
  z-index: -1;
  display: block;
}
@media (max-width: 767px) {
  .box-design-nine {
    max-width: 300px;
  }
}

.box-design-ten {
  width: 100%;
  max-width: 50%;
  height: 270px;
  background-color: var(--primary-yellow-light2);
  position: absolute;
  bottom: 0;
  right: 10%;
  z-index: -1;
  display: block;
}
@media (max-width: 767px) {
  .box-design-ten {
    max-width: 300px;
  }
}

.box-design-11 {
  height: 180px;
  width: 100%;
  max-width: 470px;
  background-color: var(--white);
  border: 1px solid var(--border);
  position: relative;
  z-index: 1;
  margin-left: 70px;
  margin-bottom: 90px;
}
@media (min-width: 992px) and (max-width: 1199px) {
  .box-design-11 {
    margin-left: 0px;
  }
}
@media (min-width: 768px) and (max-width: 991px) {
  .box-design-11 {
    margin-left: 0px;
    margin-top: 150px;
  }
}
@media (min-width: 576px) and (max-width: 768px) {
  .box-design-11 {
    margin-left: 0px;
    margin-top: 150px;
  }
}
@media (max-width: 576px) {
  .box-design-11 {
    margin-left: 0px;
    margin-top: 150px;
  }
}
.box-design-11::before {
  content: "";
  position: absolute;
  left: 90px;
  bottom: 90px;
  width: 100%;
  max-width: 470px;
  height: 370px;
  background-color: var(--primary-green-dark);
  display: block;
}
@media (min-width: 768px) and (max-width: 991px) {
  .box-design-11::before {
    left: 50px;
    bottom: 50px;
    height: 250px;
  }
}
@media (min-width: 576px) and (max-width: 768px) {
  .box-design-11::before {
    left: 50px;
    bottom: 50px;
    height: 250px;
  }
}
@media (max-width: 576px) {
  .box-design-11::before {
    left: 10px;
    bottom: 10px;
    height: 250px;
    max-width: 100%;
  }
}

.box-design-12 {
  width: 100%;
  max-width: 60%;
  height: 380px;
  background-color: var(--primary-yellow-light2);
  position: absolute;
  top: -180px;
  left: 0;
  z-index: -1;
}

.box-design-13 {
  width: 100%;
  max-width: 50%;
  height: 270px;
  background-color: var(--primary-green-dark-light);
  position: absolute;
  top: 0px;
  right: 5%;
  z-index: -1;
}

.box-design-14 {
  position: absolute;
  top: 0;
  right: 5%;
  width: 100%;
  max-width: 370px;
  height: 270px;
  border: 1px solid var(--border);
  background-color: var(--white);
}
@media (min-width: 992px) and (max-width: 1199px) {
  .box-design-14 {
    max-width: 270px;
  }
}
@media (min-width: 768px) and (max-width: 991px) {
  .box-design-14 {
    max-width: 220px;
  }
}
@media (max-width: 767px) {
  .box-design-14 {
    display: none;
    visibility: hidden;
  }
}
.box-design-14::after {
  content: "";
  position: absolute;
  bottom: 90px;
  right: 90px;
  background-color: var(--primary-blue-light);
  height: 270px;
  width: 150%;
}
@media (min-width: 1400px) and (max-width: 1599px) {
  .box-design-14::after {
    width: 130%;
  }
}
@media (min-width: 1200px) and (max-width: 1399px) {
  .box-design-14::after {
    width: 130%;
  }
}
@media (max-width: 1199px) {
  .box-design-14::after {
    width: 100%;
    right: 60px;
  }
}
@media (min-width: 768px) and (max-width: 991px) {
  .box-design-14::after {
    bottom: 60px;
  }
}

.box-design-15 {
  height: 180px;
  width: 100%;
  max-width: 470px;
  background-color: var(--white);
  border: 1px solid var(--border);
  position: relative;
  z-index: 1;
  margin-right: 70px;
  margin-left: auto;
  margin-bottom: 90px;
}
@media (min-width: 992px) and (max-width: 1199px) {
  .box-design-15 {
    margin-left: auto;
    margin-right: 50px;
    max-width: 400px;
  }
}
@media (min-width: 768px) and (max-width: 991px) {
  .box-design-15 {
    margin-left: 0px;
    margin-top: 150px;
  }
}
@media (min-width: 576px) and (max-width: 768px) {
  .box-design-15 {
    margin-left: 0px;
    margin-top: 150px;
  }
}
@media (max-width: 576px) {
  .box-design-15 {
    margin-left: 0px;
    margin-top: 150px;
  }
}
.box-design-15::before {
  content: "";
  position: absolute;
  right: 90px;
  bottom: 90px;
  width: 100%;
  max-width: 470px;
  height: 370px;
  background-color: var(--primary-yellow);
  display: block;
}
@media (min-width: 768px) and (max-width: 991px) {
  .box-design-15::before {
    left: 50px;
    bottom: 50px;
    height: 250px;
  }
}
@media (min-width: 576px) and (max-width: 768px) {
  .box-design-15::before {
    left: 50px;
    bottom: 50px;
    height: 250px;
  }
}
@media (max-width: 576px) {
  .box-design-15::before {
    left: 10px;
    bottom: 30px;
    height: 250px;
    max-width: 100%;
  }
}

.box-design-16 {
  width: 100%;
  max-width: 470px;
  height: 380px;
  border: 1px solid var(--border);
  margin-left: 180px;
  transform: translateY(100px);
}
@media (min-width: 1200px) and (max-width: 1399px) {
  .box-design-16 {
    max-width: 430px;
  }
}
@media (min-width: 992px) and (max-width: 1199px) {
  .box-design-16 {
    max-width: 390px;
  }
}
@media (max-width: 991px) {
  .box-design-16 {
    display: none;
    visibility: hidden;
  }
}

/*=======================================
 13 .Guide-section
=======================================*/
.guide-single {
  border: 1px solid var(--border);
  overflow: hidden;
}
.guide-single img {
  transition: 0.65s ease;
}
.guide-single:hover img {
  transform: scale(1.2);
}

.company-vdo {
  background-size: cover;
  background-repeat: no-repeat;
  background-position: center;
  width: 100%;
  max-width: 100%;
  min-height: 100vh;
  margin-left: auto;
  margin-right: auto;
  position: relative;
  z-index: 1;
  display: flex;
  justify-content: center;
  align-items: center;
  flex-direction: column;
  text-align: center;
}
@media (max-width: 576px) {
  .company-vdo {
    min-height: 450px;
  }
}
@media (min-width: 576px) and (max-width: 768px) {
  .company-vdo {
    height: 450px;
  }
}
@media (min-width: 768px) and (max-width: 991px) {
  .company-vdo {
    height: 480px;
  }
}
@media (min-width: 992px) and (max-width: 1199px) {
  .company-vdo {
    height: 630px;
  }
}
.company-vdo .play-icon {
  width: 65px;
  height: 45px;
  display: flex;
  justify-content: center;
  align-items: center;
}
.company-vdo .play-icon img {
  z-index: 99;
}
.company-vdo h3 {
  color: var(--white);
  font-size: 70px;
  font-weight: 300;
  margin-bottom: 40px;
}
@media (max-width: 576px) {
  .company-vdo h3 {
    font-size: 40px;
  }
}
@media (min-width: 576px) and (max-width: 768px) {
  .company-vdo h3 {
    font-size: 50px;
  }
}
@media (min-width: 768px) and (max-width: 991px) {
  .company-vdo h3 {
    font-size: 55px;
  }
}
@media (min-width: 992px) and (max-width: 1199px) {
  .company-vdo h3 {
    font-size: 60px;
  }
}
@media (min-width: 1200px) and (max-width: 1399px) {
  .company-vdo h3 {
    font-size: 65px;
  }
}

/*================================================
2. Contact Css
=================================================*/
.contact-form .form-inner {
  margin-bottom: 30px;
}
.contact-form .form-inner p {
  margin-bottom: 0px;
  font-size: 10px;
  line-height: 1.3;
}
.contact-form label {
  font-size: 11px;
  color: var(--text-primary);
  font-weight: 700;
  margin-bottom: 5px;
  padding-left: 15px;
}
.contact-form input, .contact-form textarea {
  width: 100%;
  background-color: var(--white);
  outline: none;
  border: 1px solid transparent;
  padding: 12px 15px;
  line-height: 1;
  transition: 0.4s ease;
  font-size: 12px;
}
.contact-form input:focus, .contact-form textarea:focus {
  border: 1px solid var(--primary-green-dark);
}
.contact-form input::-moz-placeholder, .contact-form textarea::-moz-placeholder {
  font-size: 10px;
  color: var(--text-secondary);
  line-height: 1;
}
.contact-form input::placeholder, .contact-form textarea::placeholder {
  font-size: 10px;
  color: var(--text-secondary);
  line-height: 1;
}
.contact-form textarea {
  min-height: 144px;
}
.contact-form .submit-btn {
  font-size: 11px;
  font-weight: 600;
  padding: 8px 15px;
}

.location-card-section {
  margin-top: -100px;
}
@media (max-width: 991px) {
  .location-card-section {
    margin-top: 0px;
  }
}

/*================================================
2. Faq Css
=================================================*/
.faq-area {
  background-color: var(--primary-blue);
  padding: 40px 70px;
  width: 100%;
  max-width: 650px;
  position: relative;
  margin-top: 180px;
}
@media (max-width: 991px) {
  .faq-area {
    max-width: 100%;
  }
}
@media (min-width: 992px) and (max-width: 1199px) {
  .faq-area {
    margin-top: 100px;
  }
}
@media (min-width: 768px) and (max-width: 991px) {
  .faq-area {
    margin-top: 70px;
    padding: 40px;
  }
}
@media (max-width: 767px) {
  .faq-area {
    margin-top: 60px;
    padding: 20px;
  }
}
.faq-area::before {
  content: "";
  position: absolute;
  right: -90px;
  top: -90px;
  display: block;
  width: 100%;
  max-width: 560px;
  background-color: var(--primary-blue-light);
  height: 100%;
}
@media (min-width: 1400px) and (max-width: 1599px) {
  .faq-area::before {
    right: -50px;
  }
}
@media (min-width: 1200px) and (max-width: 1399px) {
  .faq-area::before {
    right: 0px;
  }
}
@media (min-width: 992px) and (max-width: 1199px) {
  .faq-area::before {
    right: 0px;
  }
}
@media (max-width: 991px) {
  .faq-area::before {
    display: none;
    visibility: hidden;
  }
}

.faq-wrap .faq-item {
  border: none;
}
.faq-wrap .faq-item:last-child {
  margin-bottom: 0;
}
.faq-wrap .accordion-button {
  font-weight: 600;
  font-size: 16px;
  border-radius: 0px;
  color: var(--white);
  cursor: pointer;
  transition: 0.4s ease-in-out;
  padding: 8px 0px;
  padding-right: 60px;
  margin-bottom: 0px;
  line-height: 1.4;
  background-color: var(--primary-blue);
}
@media (max-width: 767px) {
  .faq-wrap .accordion-button {
    padding-left: 22px;
    font-size: 14px;
  }
}
@media (min-width: 992px) and (max-width: 1199px) {
  .faq-wrap .accordion-button {
    padding-left: 22px;
  }
}
.faq-wrap .accordion-button:hover {
  padding-left: 22px;
}
@media (max-width: 991px) {
  .faq-wrap .accordion-button:hover {
    padding-left: 22px;
  }
}
.faq-wrap .accordion-button:focus {
  color: var(--primary-blue);
  z-index: unset;
  border-color: unset;
  outline: 0;
}
.faq-wrap .accordion-button::after {
  flex-shrink: 0;
  margin-left: auto;
  background: var(--primary-blue);
  font-family: bootstrap-icons !important;
  position: absolute;
  left: -18px;
  top: 11px;
  content: "\f4fe";
  transition: unset;
  font-size: 20px;
  width: 15px;
  height: 15px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--white);
}
@media (max-width: 991px) {
  .faq-wrap .accordion-button::after {
    left: 0;
    top: 12px;
  }
}
.faq-wrap .accordion-button:not(.collapsed)::after {
  transform: unset;
  font-family: bootstrap-icons !important;
  content: "\f2ea";
  color: var(--white);
  background-image: none;
}
.faq-wrap .accordion-button:not(.collapsed) {
  color: var(--white) !important;
  border-radius: 0px !important;
  box-shadow: none;
}
.faq-wrap .faq-body {
  font-weight: 400;
  font-size: 14px;
  color: var(--white);
  border-top: none;
  padding: 0px 0px 15px 0px;
  line-height: 1.3;
  text-align: left;
}
.faq-wrap .accordion-button:not(.collapsed) {
  border-radius: 5px 5px 0px 0px;
  color: var(--text-primary);
}

/*================================================
2. people-info page
=================================================*/
.experience-block {
  margin-bottom: 20px;
}
.experience-block .experice-text {
  border-top: 1px solid var(--border);
  border-bottom: 1px solid var(--border);
  padding-top: 10px;
  padding-bottom: 15px;
}

.testimonial-block {
  border-bottom: 1px solid var(--border);
  margin-bottom: 20px;
}
.testimonial-block .testi-single {
  margin-bottom: 15px;
}
.testimonial-block .testi-single p {
  font-style: italic;
  margin-bottom: 0;
  font-size: 16px;
}
.testimonial-block .testi-single span {
  display: inline-block;
  font-size: 10px;
  font-weight: 700;
  color: var(--text-primary);
  line-height: 1;
}

.publication-block {
  border-bottom: 1px solid var(--border);
}

.publication-text h6 {
  font-size: 15px;
  font-weight: 700;
  margin-bottom: 0;
}

.people-info-section {
  padding-left: 8%;
}
@media (max-width: 767px) {
  .people-info-section {
    padding-left: 0;
  }
}

.testimonial-card {
  background-color: var(--primary-red);
  padding: 35px 40px;
  width: 100%;
  max-width: 550px;
  margin-left: 120px;
  margin-top: -90px;
}
.testimonial-card p {
  color: var(--white);
  font-style: italic;
  font-size: 16px;
  font-weight: 300;
}

.testimonial-image2 {
  max-width: 320px;
  width: 100%;
  margin-left: auto;
  margin-right: 40px;
}

.tesitmonial-section {
  margin-top: -90px;
  position: relative;
}

.about-people-card {
  border: 1px solid var(--border);
  padding: 40px 100px;
  margin-top: -400px;
  z-index: 2;
  position: relative;
  width: 100%;
  max-width: 630px;
  margin-bottom: 380px;
}
@media (min-width: 1200px) and (max-width: 1399px) {
  .about-people-card {
    padding: 40px 70px;
  }
}
@media (min-width: 992px) and (max-width: 1199px) {
  .about-people-card {
    padding: 40px 50px;
  }
}
@media (max-width: 576px) {
  .about-people-card {
    padding: 30px 15px;
  }
}
.about-people-card .designation h1, .about-people-card .designation h2 {
  font-size: 40px;
  margin-bottom: 0;
  color: var(--text-primary);
  font-weight: 300;
  line-height: 1.1;
}
.about-people-card .designation h2 {
  color: #777;
}
.about-people-card .box {
  height: 190px;
  width: 100%;
  max-width: 380px;
  background-color: var(--primary-green);
  margin-top: 30px;
  margin-bottom: 30px;
  margin-left: -190px;
}
@media (min-width: 768px) and (max-width: 991px) {
  .about-people-card .box {
    margin-left: -160px;
  }
}
@media (max-width: 576px) {
  .about-people-card .box {
    margin-left: -25px;
    max-width: 280px;
  }
}
.about-people-card h6 {
  font-size: 14px;
  font-weight: 700;
  margin-bottom: 0px;
}
.about-people-card ul {
  list-style: none;
  margin: 0;
  padding: 0;
  margin-bottom: 25px;
}
.about-people-card ul li {
  color: var(--text-secondary);
  font-size: 14px;
}
.about-people-card ul li:hover a {
  color: var(--primary-green-dark);
}
.about-people-card ul li a {
  color: inherit;
  text-decoration: none;
  transition: 0.4s;
}
.about-people-card .social-link {
  margin-top: 15px;
}
.about-people-card .social-link:hover i {
  background-color: var(--primary-blue);
  color: var(--white);
}
.about-people-card .social-link i {
  font-size: 18px;
  width: 25px;
  height: 25px;
  line-height: 25px;
  border-radius: 50%;
  color: var(--white);
  background-color: var(--text-primary);
  text-align: center;
  transition: 0.4s;
}
.about-people-card .quote-box {
  background-color: var(--primary-red);
  padding: 20px;
  min-height: 290px;
  width: 100%;
  max-width: 390px;
  position: absolute;
  bottom: -200px;
  right: -40px;
}
@media (max-width: 576px) {
  .about-people-card .quote-box {
    right: -10px;
    bottom: -260px;
  }
}
.about-people-card .quote-box p {
  color: var(--white);
  font-size: 20px;
  font-weight: 300;
  line-height: 1.3;
  margin-bottom: 10px;
}
.about-people-card .quote-box span {
  font-size: 12px;
  font-weight: 700;
  color: var(--white);
}

.experience-box {
  background-color: var(--primary-blue);
  padding: 30px 70px;
  color: var(--white);
  width: 100%;
  max-width: 540px;
  display: block;
  position: relative;
}
@media (max-width: 991px) {
  .experience-box {
    padding: 30px 30px;
    margin-bottom: 50px;
  }
}
.experience-box::after {
  content: "";
  position: absolute;
  display: block;
  right: -90px;
  bottom: -90px;
  height: 180px;
  width: 270px;
  background-color: var(--white);
  border: 1px solid var(--border);
  z-index: -1;
}
@media (max-width: 991px) {
  .experience-box::after {
    display: none;
    visibility: hidden;
  }
}
.experience-box::before {
  content: "";
  position: absolute;
  display: block;
  left: -90px;
  top: -90px;
  height: 85%;
  width: 100%;
  max-width: 540px;
  background-color: var(--primary-blue-light);
  z-index: -1;
}
.experience-box .title {
  font-size: 18px;
  font-weight: 700;
  color: var(--white);
}
.experience-box .subtitle {
  font-size: 15px;
  font-weight: 600;
  color: var(--white);
  margin-bottom: 0px;
  position: relative;
}
.experience-box .subtitle::before {
  content: "\f285";
  font-family: "Bootstrap-icons";
  position: absolute;
  top: 4px;
  left: -20px;
  font-size: 10px;
  color: var(--white);
}
@media (max-width: 991px) {
  .experience-box .subtitle::before {
    left: -15px;
  }
}
.experience-box ul {
  list-style: none;
  margin: 0;
  padding: 0;
  margin-bottom: 20px;
}
.experience-box ul li {
  color: var(--white);
  font-size: 14px;
  font-weight: 300;
}

/*================================================
 Career Section
=================================================*/
.career-card {
  position: relative;
  z-index: 2;
}
.career-card:hover .image-wrap img {
  transform: scale(1.1);
}
.career-card.style-blue .content {
  background-color: var(--primary-blue);
}
.career-card.style-yellow .content {
  background-color: var(--primary-yellow);
}
.career-card.style-green .content {
  background-color: var(--primary-green-dark);
}
.career-card .image-wrap {
  overflow: hidden;
}
.career-card .image-wrap img {
  height: 270px;
  width: 100%;
  -o-object-fit: cover;
     object-fit: cover;
  transition: all 0.4s;
}
.career-card .content {
  padding: 15px;
}
.career-card .content h5 {
  font-size: 16px;
  color: var(--white);
  margin-bottom: 0px;
}
.career-card .content p {
  color: var(--white);
  margin-bottom: 35px;
}

.career-card-two {
  position: relative;
  padding: 20px;
  z-index: 2;
}
@media (max-width: 767px) {
  .career-card-two {
    padding: 15px;
  }
}
.career-card-two.style-blue {
  background-color: var(--primary-blue);
}
.career-card-two.style-yellow {
  background-color: var(--primary-yellow);
}
.career-card-two.style-green {
  background-color: var(--primary-green-dark);
}
.career-card-two .title {
  position: relative;
  display: inline-block;
  padding-bottom: 10px;
  margin-bottom: 10px;
}
.career-card-two .title::after {
  content: "";
  position: absolute;
  bottom: 0;
  left: 0;
  display: block;
  height: 1px;
  width: 100%;
  background-color: var(--white);
}
.career-card-two .title h5 {
  font-size: 16px;
  color: var(--white);
  margin-bottom: 0px;
}
@media (max-width: 767px) {
  .career-card-two .title h5 {
    font-size: 14px;
  }
}
.career-card-two p {
  color: var(--white);
  margin-bottom: 60px;
}
@media (max-width: 767px) {
  .career-card-two p {
    font-size: 13px;
  }
}

.career-card-section {
  position: relative;
}
.career-card-section .red-box {
  position: absolute;
  top: -100px;
  right: 5%;
  background-color: var(--primary-red);
  height: 380px;
  width: 100%;
  max-width: 670px;
  z-index: 1;
}

.career-box-right {
  width: 100%;
  max-width: 570px;
  border: 1px solid var(--border);
  height: 100%;
  max-height: 770px;
  margin-top: -170px;
  position: relative;
}
.career-box-right::before {
  content: "";
  position: absolute;
  bottom: 90px;
  left: 20%;
  width: 100%;
  max-width: 570px;
  background-color: var(--primary-green-dark-light);
  height: 380px;
}

.career-right-image {
  width: 100%;
  max-width: 570px;
  margin-top: -180px;
  margin-left: 90px;
  z-index: 1;
  position: relative;
  margin-bottom: 190px;
}

.career-right-image2 {
  width: 100%;
  max-width: 280px;
}

.career-right-quote {
  background-color: var(--primary-red);
  padding: 20px;
  min-height: 300px;
  width: 100%;
  max-width: 480px;
}
.career-right-quote p {
  color: var(--white);
  font-size: 20px;
  font-weight: 300;
  line-height: 1.3;
  margin-bottom: 10px;
}
.career-right-quote span {
  font-size: 12px;
  font-weight: 700;
  color: var(--white);
}

.career-box-right2 {
  width: 100%;
  max-width: 570px;
  background-color: var(--primary-yellow-light2);
  min-height: 570px;
  display: block;
  margin-top: -100px;
  margin-left: 100px;
}

.career-right-quote2 {
  background-color: var(--primary-red);
  padding: 40px 35px 110px 35px;
}
.career-right-quote2 p {
  font-size: 16px;
  font-style: italic;
  color: var(--white);
}

.skill-card {
  background-color: var(--primary-blue);
  padding: 20px;
  min-height: 320px;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  transition: 0.45s;
}
@media (max-width: 767px) {
  .skill-card {
    padding: 10px;
    min-height: 300px;
  }
}
.skill-card:hover {
  background-color: var(--text-primary);
}
.skill-card:hover .icon {
  transform: rotate(5deg);
}
.skill-card:hover .icon svg {
  fill: var(--white);
}
.skill-card:hover .icon.communicate svg {
  stroke: var(--white);
}
.skill-card .content h5 {
  font-size: 17px;
  font-weight: 600;
  color: var(--white);
}
@media (max-width: 767px) {
  .skill-card .content h5 {
    font-size: 14px;
  }
}
.skill-card .content p {
  color: var(--white);
  margin-bottom: 20px;
  font-size: 15px;
}
@media (max-width: 767px) {
  .skill-card .content p {
    font-size: 12px;
  }
}
.skill-card .icon {
  max-width: 90px;
  margin-left: auto;
  margin-right: 0;
  transition: 0.45s;
  transition-delay: 0.3s;
}
.skill-card .icon.communicate svg {
  fill: none;
  stroke: var(--text-primary);
  max-width: 90px;
}
@media (max-width: 767px) {
  .skill-card .icon.communicate svg {
    max-width: 45px;
  }
}
.skill-card .icon svg {
  fill: var(--text-primary);
  transition: 0.45s;
  max-width: 90px;
}
@media (max-width: 767px) {
  .skill-card .icon svg {
    max-width: 45px;
  }
}

.content-italic p {
  font-style: italic;
}
.content-italic h6 {
  font-style: italic;
}

.testimonial-section {
  padding-left: 90px;
  position: relative;
  padding-top: 180px;
  margin-top: 210px;
}
@media (min-width: 992px) and (max-width: 1199px) {
  .testimonial-section {
    padding-left: 30px;
  }
}
@media (min-width: 768px) and (max-width: 991px) {
  .testimonial-section {
    padding-left: calc((100% - 720px) / 2);
  }
}
@media (min-width: 576px) and (max-width: 768px) {
  .testimonial-section {
    padding-left: calc((100% - 540px) / 2);
  }
}
@media (max-width: 576px) {
  .testimonial-section {
    padding-left: 0;
  }
}
.testimonial-section::before {
  content: "";
  position: absolute;
  right: 15%;
  top: 0;
  width: 100%;
  max-width: 42%;
  height: 71%;
  border: 1px solid var(--border);
  display: block;
  z-index: -2;
}
.testimonial-section .testi-top-image {
  max-width: 460px;
  position: absolute;
  right: 10%;
  top: -275px;
}
@media (min-width: 1400px) and (max-width: 1599px) {
  .testimonial-section .testi-top-image {
    max-width: 380px;
  }
}
@media (min-width: 1200px) and (max-width: 1399px) {
  .testimonial-section .testi-top-image {
    max-width: 290px;
  }
}
@media (min-width: 992px) and (max-width: 1199px) {
  .testimonial-section .testi-top-image {
    max-width: 290px;
    right: 5%;
  }
}
@media (max-width: 991px) {
  .testimonial-section .testi-top-image {
    top: -120px;
  }
}
.testimonial-section .testi-top-image img {
  width: 100%;
  height: 365px;
  -o-object-fit: cover;
     object-fit: cover;
}

.big-card {
  padding: 90px 90px 80px 180px;
  display: flex;
  gap: 50px;
  height: auto;
  width: 100%;
  max-width: 1260px;
  z-index: 2;
  position: relative;
}
@media (min-width: 1200px) and (max-width: 1399px) {
  .big-card {
    padding: 70px 70px 60px 120px;
  }
}
@media (min-width: 992px) and (max-width: 1199px) {
  .big-card {
    padding: 50px 50px 40px 90px;
  }
}
@media (min-width: 768px) and (max-width: 991px) {
  .big-card {
    padding: 40px 40px 30px 40px;
  }
}
@media (max-width: 767px) {
  .big-card {
    padding: 25px 25px;
    gap: 20px;
    flex-wrap: wrap;
  }
}
.big-card.style-blue {
  background-color: var(--primary-blue-light);
}
.big-card.style-blue .body {
  border-top: 1px solid var(--primary-blue);
  border-bottom: 1px solid var(--primary-blue);
}
.big-card.style-yellow {
  background-color: var(--primary-yellow-light);
}
.big-card.style-yellow .body {
  border-top: 1px solid var(--primary-yellow);
  border-bottom: 1px solid var(--primary-yellow);
}
.big-card.style-red {
  background-color: var(--primary-red-light);
}
.big-card.style-red .body {
  border-top: 1px solid var(--primary-red);
  border-bottom: 1px solid var(--primary-red);
}
.big-card .content .title h4 {
  font-size: 24px;
  font-weight: 300;
}
@media (max-width: 767px) {
  .big-card .content .title h4 {
    font-size: 22px;
  }
}
.big-card .content .body {
  padding: 20px 0px;
}
.big-card .content .body p {
  font-size: 17px;
  font-style: italic;
}
@media (max-width: 767px) {
  .big-card .content .body p {
    font-size: 15px;
  }
}
.big-card .author {
  width: 100%;
  max-width: 190px;
  min-width: 120px;
  margin-top: 42px;
}
@media (max-width: 767px) {
  .big-card .author {
    margin-top: 10px;
    max-width: 150px;
  }
}
.big-card .author img {
  margin-bottom: 10px;
}
.big-card .author h6 {
  margin-bottom: 0px;
  font-size: 13px;
  font-weight: 600;
}
.big-card .author span {
  display: block;
  font-size: 12px;
  line-height: 1.3;
}

.skill-section {
  position: relative;
}
.skill-section .skill-box-design {
  position: absolute;
  bottom: 0;
  right: 5%;
  max-width: 460px;
  width: 100%;
  background-color: var(--primary-green-dark-light);
  height: 60%;
  z-index: -1;
}

.choose-us-card {
  background-color: var(--primary-red);
  padding: 140px 70px 90px 70px;
  width: 100%;
  max-width: 640px;
  margin-left: auto;
  margin-right: 0;
  margin-top: -300px;
  position: relative;
  z-index: 2;
}
@media (min-width: 1400px) and (max-width: 1599px) {
  .choose-us-card {
    max-width: 540px;
  }
}
@media (min-width: 1200px) and (max-width: 1399px) {
  .choose-us-card {
    max-width: 500px;
    padding: 120px 50px 70px 50px;
  }
}
@media (min-width: 992px) and (max-width: 1199px) {
  .choose-us-card {
    max-width: 450px;
    padding: 120px 50px 70px 50px;
  }
}
@media (max-width: 991px) {
  .choose-us-card {
    padding: 50px 30px 40px 30px;
    margin-top: -150px;
  }
}
.choose-us-card h2 {
  font-size: 40px;
  font-weight: 700;
  margin-bottom: 0px;
  line-height: 1;
  position: relative;
  color: var(--white);
  padding-bottom: 55px;
  margin-bottom: 30px;
}
@media (max-width: 767px) {
  .choose-us-card h2 {
    font-size: 30px;
  }
}
.choose-us-card h2::after {
  content: "";
  position: absolute;
  left: 0;
  bottom: 0;
  width: 80px;
  background-color: var(--white);
  height: 1px;
}
.choose-us-card ul {
  list-style: none;
  margin: 0;
  padding: 0;
}
.choose-us-card ul li {
  font-size: 35px;
  font-weight: 300;
  color: var(--white);
  line-height: 1.3;
}
.choose-us-card ul li:first-child {
  font-style: italic;
}
@media (max-width: 767px) {
  .choose-us-card ul li {
    font-size: 24px;
  }
}

.choose-card-box {
  width: 100%;
  max-width: 475px;
  margin-left: 0px;
  margin-right: auto;
  margin-top: -180px;
  height: 570px;
  border: 1px solid var(--border);
}
@media (max-width: 991px) {
  .choose-card-box {
    height: 370px;
  }
}

.career-details-section {
  padding-left: 90px;
}
@media (min-width: 992px) and (max-width: 1199px) {
  .career-details-section {
    padding-left: calc((100% - 950px) / 2);
  }
}
@media (min-width: 768px) and (max-width: 991px) {
  .career-details-section {
    padding-left: calc((100% - 720px) / 2);
  }
}
@media (min-width: 576px) and (max-width: 768px) {
  .career-details-section {
    padding-left: calc((100% - 540px) / 2);
  }
}
@media (max-width: 576px) {
  .career-details-section {
    padding-left: 0;
  }
}

.career-left-image {
  max-width: 360px;
  margin-left: auto;
  position: relative;
  margin-top: -90px;
}
.career-left-image::before {
  content: "";
  position: absolute;
  bottom: 90px;
  right: 35%;
  width: 460px;
  height: 100%;
  background-color: var(--primary-red-light);
  z-index: -1;
}
@media (min-width: 1200px) and (max-width: 1399px) {
  .career-left-image::before {
    width: 360px;
  }
}
@media (min-width: 992px) and (max-width: 1199px) {
  .career-left-image::before {
    width: 340px;
  }
}
@media (max-width: 991px) {
  .career-left-image::before {
    width: 100%;
    right: 10%;
  }
}
.career-left-image img {
  max-width: 100%;
}

.eligibility-card {
  background-color: var(--primary-yellow);
  padding: 30px 60px;
  width: 100%;
  max-width: 670px;
  margin-left: auto;
  margin-right: -90px;
  margin-top: -90px;
  position: relative;
}
.eligibility-card.style-green {
  background-color: var(--primary-green-dark);
  margin-left: -90px;
  margin-right: auto;
  margin-top: 90px;
  padding: 30px 50px;
  max-width: 100%;
}
.eligibility-card.style-green::before {
  content: "";
  width: 100%;
  max-width: 770px;
  height: 380px;
  display: block;
  background-color: var(--primary-green-dark-light);
  position: absolute;
  left: -90px;
  bottom: 160px;
  z-index: -1;
}
.eligibility-card.style-blue {
  background-color: var(--primary-blue);
  padding: 30px 60px;
  width: 100%;
  max-width: 670px;
  margin-left: -180px;
  margin-right: auto;
  margin-top: 130px;
  position: relative;
}
.eligibility-card.style-blue::before {
  content: "";
  content: "";
  width: 100%;
  max-width: 670px;
  height: 100%;
  display: block;
  background-color: var(--primary-blue-light);
  position: absolute;
  left: -90px;
  bottom: 160px;
  z-index: -1;
}
@media (max-width: 991px) {
  .eligibility-card.style-blue::before {
    left: -50px;
    bottom: 50px;
  }
}
@media (max-width: 991px) {
  .eligibility-card {
    max-width: 500px;
    margin-top: 0px;
    margin-left: auto;
    margin-right: auto;
    padding: 30px 25px;
  }
}
.eligibility-card::before {
  content: "";
  width: 100%;
  max-width: 670px;
  height: 100%;
  display: block;
  background-color: var(--primary-yellow-light);
  position: absolute;
  left: -90px;
  bottom: 160px;
  z-index: -1;
}
@media (max-width: 991px) {
  .eligibility-card::before {
    left: -50px;
    bottom: 50px;
  }
}
.eligibility-card .title {
  position: relative;
  padding-bottom: 5px;
  display: inline-block;
  margin-bottom: 18px;
}
.eligibility-card .title h6 {
  color: var(--white);
}
.eligibility-card .title::after {
  content: "";
  position: absolute;
  left: 0;
  bottom: 0;
  width: 100%;
  height: 1px;
  background-color: var(--white);
}
.eligibility-card .title h6 {
  font-size: 18px;
  text-transform: uppercase;
}
.eligibility-card p {
  font-size: 15px;
  font-weight: 600;
  color: var(--white);
}
.eligibility-card .date {
  margin-bottom: 20px;
}
.eligibility-card .date p {
  margin-bottom: 0px;
}
.eligibility-card .date span {
  display: inline-block;
  color: var(--white);
  font-size: 15px;
}

.case-author-single {
  position: relative;
  width: 100%;
  max-width: 290px;
  margin-left: auto;
  margin-right: -90px;
  margin-top: 190px;
}
@media (max-width: 991px) {
  .case-author-single {
    margin-right: 0;
    margin-left: auto;
    margin-top: 70px;
    margin-bottom: 20px;
  }
}
.case-author-single .image-wrap {
  margin-bottom: 8px;
}
.case-author-single .content h6 {
  margin-bottom: 0px;
  font-size: 15px;
}
.case-author-single .content span {
  display: block;
  font-size: 15px;
  line-height: 1.3;
}

.business-parnter-single {
  position: relative;
  width: 100%;
  max-width: 380px;
  margin-left: auto;
  margin-right: 0px;
  margin-top: 100px;
}
.business-parnter-single.style-blue::before {
  background-color: var(--primary-blue-light);
}
@media (max-width: 991px) {
  .business-parnter-single {
    margin-left: 0px;
    margin-right: auto;
    margin-bottom: 20px;
  }
}
.business-parnter-single::before {
  content: "";
  position: absolute;
  right: 45%;
  bottom: 155px;
  width: 100%;
  min-width: 580px;
  height: 480px;
  background-color: var(--primary-yellow-light2);
  z-index: -1;
}
@media (min-width: 1400px) and (max-width: 1599px) {
  .business-parnter-single::before {
    min-width: 470px;
  }
}
@media (min-width: 1200px) and (max-width: 1399px) {
  .business-parnter-single::before {
    min-width: 400px;
    right: 30%;
    bottom: 100px;
  }
}
@media (min-width: 992px) and (max-width: 1199px) {
  .business-parnter-single::before {
    min-width: 370px;
    right: 15%;
    bottom: 100px;
  }
}
@media (min-width: 768px) and (max-width: 991px) {
  .business-parnter-single::before {
    min-width: 370px;
    right: 30px;
    bottom: 100px;
    height: 380px;
  }
}
@media (min-width: 576px) and (max-width: 768px) {
  .business-parnter-single::before {
    min-width: 370px;
    right: 30px;
    bottom: 100px;
    height: 380px;
  }
}
@media (max-width: 576px) {
  .business-parnter-single::before {
    min-width: 370px;
    right: 30px;
    bottom: 100px;
    height: 350px;
  }
}
.business-parnter-single .image-wrap {
  margin-bottom: 8px;
}
.business-parnter-single .content h6 {
  margin-bottom: 0px;
  font-size: 15px;
}
.business-parnter-single .content span {
  display: block;
  font-size: 15px;
  line-height: 1.3;
}

.senior-parnter-single h6 {
  font-size: 12px;
  margin-bottom: 0px;
  line-height: 1.3;
  font-weight: 600;
}
.senior-parnter-single span {
  display: block;
  line-height: 1.2;
  font-size: 12px;
}

.apply-area {
  position: relative;
  display: block;
}
.apply-area::before {
  content: "";
  position: absolute;
  left: 40%;
  bottom: 90px;
  background-color: var(--primary-yellow-light);
  width: 100%;
  max-width: 670px;
  height: 280px;
  z-index: -1;
}

.apply-card {
  background-color: var(--primary-blue);
  min-height: 270px;
  width: 100%;
  max-width: 400px;
  padding: 30px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  margin-top: 120px;
}
@media (max-width: 991px) {
  .apply-card {
    margin-top: 50px;
  }
}
.apply-card.style-yellow {
  background-color: var(--primary-yellow);
  margin-top: 90px;
}
.apply-card.style-green {
  background-color: var(--primary-green-dark);
  margin-top: 90px;
}
.apply-card h5 {
  color: var(--white);
  margin-bottom: 0px;
  line-height: 1;
  font-size: 18px;
}
.apply-card .h-line {
  width: 100%;
  display: block;
  max-width: 240px;
  height: 1px;
  background-color: var(--white);
  margin: 30px 0px;
}

.apply-image {
  width: 100%;
  max-width: 380px;
  height: 380px;
  margin-left: auto;
  margin-right: auto;
  position: relative;
  margin-top: 200px;
}
@media (max-width: 991px) {
  .apply-image {
    margin-bottom: 90px;
  }
}
.apply-image::before {
  content: "";
  position: absolute;
  width: 100%;
  max-width: 380px;
  height: 280px;
  border: 1px solid var(--border);
  left: -90px;
  top: -180px;
  z-index: -1;
}
@media (min-width: 768px) and (max-width: 991px) {
  .apply-image::before {
    left: -160px;
    top: -90px;
  }
}
@media (max-width: 767px) {
  .apply-image::before {
    left: -70px;
    top: -90px;
    height: 180px;
  }
}
.apply-image::after {
  content: "";
  position: absolute;
  width: 100%;
  min-width: 680px;
  height: 280px;
  background-color: var(--primary-green-dark-light);
  right: -90px;
  bottom: -180px;
  z-index: -1;
}
@media (max-width: 991px) {
  .apply-image::after {
    bottom: -90px;
    height: 180px;
    max-width: 100%;
    right: -10px;
  }
}
.apply-image img {
  -o-object-fit: cover;
     object-fit: cover;
}

.trainee-placement-image {
  position: relative;
  margin-top: 90px;
}
.trainee-placement-image::before {
  content: "";
  position: absolute;
  width: 100%;
  height: 280px;
  border: 1px solid var(--border);
  left: -90px;
  bottom: -180px;
  z-index: -1;
}

.facility-image {
  position: relative;
  width: 100%;
  height: 390px;
  border: 1px solid var(--border);
}
@media (max-width: 991px) {
  .facility-image {
    height: 300px;
    width: 90%;
    margin-left: 30px;
    margin-bottom: 40px;
    margin-top: 40px;
  }
}
.facility-image img {
  position: absolute;
  right: 90px;
  bottom: 90px;
  z-index: 1;
  width: 570px;
  height: 470px;
  -o-object-fit: cover;
     object-fit: cover;
}
@media (min-width: 992px) and (max-width: 1199px) {
  .facility-image img {
    right: 35px;
  }
}
@media (max-width: 991px) {
  .facility-image img {
    height: 300px;
    bottom: 30px;
    right: 30px;
  }
}

.attorny-left-image {
  position: relative;
  margin-top: 190px;
}
@media (max-width: 991px) {
  .attorny-left-image {
    margin-bottom: 40px;
    margin-left: 30px;
    margin-top: 60px;
  }
}
.attorny-left-image::before {
  content: "";
  position: absolute;
  background-color: var(--primary-yellow-light2);
  width: 100%;
  height: 380px;
  left: -90px;
  top: -190px;
  z-index: -1;
}
@media (min-width: 992px) and (max-width: 1199px) {
  .attorny-left-image::before {
    left: -50px;
  }
}
@media (max-width: 991px) {
  .attorny-left-image::before {
    left: -30px;
    top: -60px;
  }
}
.attorny-left-image img {
  z-index: 1;
  height: 390px;
  -o-object-fit: cover;
     object-fit: cover;
  width: 100%;
}

.offer-card {
  background-color: var(--primary-green-dark);
  padding: 50px;
  width: 100%;
  min-width: 670px;
  margin-left: -212px;
  margin-top: 270px;
  z-index: 3;
  position: relative;
}
@media (min-width: 1400px) and (max-width: 1599px) {
  .offer-card {
    min-width: 600px;
    margin-left: -162px;
  }
}
@media (max-width: 1399px) {
  .offer-card {
    min-width: 100%;
    margin-left: 0;
  }
}
@media (min-width: 1200px) and (max-width: 1399px) {
  .offer-card {
    padding: 35px;
  }
}
@media (min-width: 992px) and (max-width: 1199px) {
  .offer-card {
    padding: 35px;
  }
}
@media (max-width: 991px) {
  .offer-card {
    padding: 25px;
    margin-bottom: 40px;
    margin-top: 120px;
  }
}
.offer-card .title h4 {
  font-size: 20px;
  color: var(--white);
  text-transform: uppercase;
}
.offer-card .list-block {
  margin-bottom: 25px;
}
.offer-card .list-block:last-child {
  margin-bottom: 0px;
}
.offer-card .list-block h6 {
  color: var(--white);
  font-size: 16px;
  font-weight: 600;
  margin-bottom: 0px;
}
@media (max-width: 767px) {
  .offer-card .list-block h6 {
    font-size: 15px;
  }
}
.offer-card .list-block ul {
  list-style: none;
  padding: 0;
  margin: 0;
}
.offer-card .list-block ul li {
  font-size: 15px;
  color: var(--white);
  margin-bottom: 5px;
}
@media (max-width: 767px) {
  .offer-card .list-block ul li {
    font-size: 14px;
  }
}

.recruitment-image {
  position: relative;
  margin-bottom: 180px;
}
@media (max-width: 991px) {
  .recruitment-image {
    margin-top: 40px;
    margin-bottom: 0px;
  }
}
.recruitment-image::after {
  content: "";
  position: absolute;
  bottom: -180px;
  right: 90px;
  min-width: 650px;
  width: 100%;
  height: 400px;
  background-color: var(--primary-green-dark-light);
  z-index: -1;
}
@media (min-width: 1400px) and (max-width: 1599px) {
  .recruitment-image::after {
    min-width: 580px;
  }
}
@media (min-width: 1200px) and (max-width: 1399px) {
  .recruitment-image::after {
    min-width: 450px;
  }
}
@media (min-width: 992px) and (max-width: 1199px) {
  .recruitment-image::after {
    min-width: 100%;
  }
}
@media (max-width: 991px) {
  .recruitment-image::after {
    min-width: 100%;
    right: 50px;
    height: 300px;
    bottom: -50px;
  }
}

.tip-left-box {
  width: 100%;
  max-width: 570px;
  height: 470px;
  border: 1px solid var(--border);
  margin-top: -90px;
  z-index: 2;
  position: relative;
}

.tips-section {
  margin-top: -90px;
  padding-left: 3%;
}
@media (min-width: 768px) and (max-width: 991px) {
  .tips-section {
    margin-top: 0px;
    padding-left: 0px;
    padding-top: 70px;
    padding-bottom: 70px;
  }
}
@media (max-width: 767px) {
  .tips-section {
    margin-top: 0px;
    padding-left: 0px;
    padding-top: 60px;
    padding-bottom: 60px;
  }
}

/*================================================
 About-section 
=================================================*/
.about-section .quote-box {
  background-color: var(--primary-red);
  padding: 35px 45px;
  min-height: 380px;
  width: 100%;
  max-width: 450px;
  margin-left: auto;
  margin-right: auto;
  position: relative;
}
@media (max-width: 991px) {
  .about-section .quote-box {
    margin-top: 30px;
    padding: 25px;
    min-height: 280px;
  }
}
.about-section .quote-box::before {
  content: "";
  position: absolute;
  left: 50%;
  transform: translateX(-50%);
  height: 380px;
  width: 130%;
  background-color: var(--primary-red-light);
  top: -190px;
  z-index: 1;
}
@media (max-width: 991px) {
  .about-section .quote-box::before {
    display: none;
    visibility: hidden;
  }
}
.about-section .quote-box p {
  color: var(--white);
  font-size: 20px;
  font-weight: 300;
  line-height: 1.3;
  margin-bottom: 10px;
}
@media (max-width: 767px) {
  .about-section .quote-box p {
    font-size: 17px;
  }
}
.about-section .quote-box span {
  font-size: 12px;
  font-weight: 700;
  color: var(--white);
}
.about-section .author-image {
  max-width: 380px;
  margin-top: 190px;
  position: relative;
}
@media (max-width: 576px) {
  .about-section .author-image {
    max-width: 280px;
    margin-top: 100px;
  }
}
.about-section .author-image::before {
  content: "";
  position: absolute;
  border: 1px solid var(--border);
  width: 100%;
  max-width: 380px;
  height: 380px;
  z-index: -1;
  left: 82%;
  bottom: 75%;
}
@media (min-width: 1200px) and (max-width: 1399px) {
  .about-section .author-image::before {
    left: 70%;
  }
}
@media (min-width: 992px) and (max-width: 1199px) {
  .about-section .author-image::before {
    left: 45%;
  }
}
@media (min-width: 768px) and (max-width: 991px) {
  .about-section .author-image::before {
    left: 45%;
  }
}
@media (max-width: 767px) {
  .about-section .author-image::before {
    left: 5%;
    max-width: 280px;
  }
}
.about-section .author-image img {
  width: 100%;
  -o-object-fit: cover;
     object-fit: cover;
  margin-left: 90px;
}
@media (max-width: 991px) {
  .about-section .author-image img {
    margin-left: 0px;
  }
}

/*================================================
Counter-section
=================================================*/
.counter-section {
  background-color: var(--primary-green-dark-light);
}

.counter-single {
  text-align: center;
}
.counter-single.style-green h3 {
  color: var(--primary-green-dark);
}
.counter-single.style-yellow h3 {
  color: var(--primary-yellow);
  position: relative;
}
.counter-single.style-yellow h3::after {
  content: "\f4d1";
  font-family: "Bootstrap-icons";
  position: absolute;
  right: -25px;
  top: 32px;
  font-size: 32px;
  color: var(--primary-yellow);
}
.counter-single.style-red h3 {
  color: var(--primary-red);
}
.counter-single.style-blue h3 {
  color: var(--primary-blue);
  position: relative;
}
.counter-single.style-blue h3::after {
  content: "\f64d";
  font-family: "Bootstrap-icons";
  position: absolute;
  right: -30px;
  top: 50%;
  transform: translateY(-50%);
  font-size: 32px;
  color: var(--primary-blue);
}
.counter-single h3 {
  font-size: 130px;
  font-weight: 300;
  line-height: 1;
  letter-spacing: -8px;
  margin-bottom: 15px;
}
@media (min-width: 992px) and (max-width: 1199px) {
  .counter-single h3 {
    font-size: 100px;
    letter-spacing: -5px;
  }
}
@media (min-width: 768px) and (max-width: 991px) {
  .counter-single h3 {
    letter-spacing: -3px;
    font-size: 80px;
  }
}
@media (max-width: 767px) {
  .counter-single h3 {
    font-size: 50px;
    letter-spacing: 0px;
  }
}
.counter-single p {
  margin-bottom: 0px;
  font-size: 15px;
  line-height: 1.4;
}

.sponsor-wrapper {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 15px;
  flex-wrap: nowrap;
}
.sponsor-wrapper a {
  max-width: auto;
}
@media (max-width: 576px) {
  .sponsor-wrapper a {
    max-width: 80px;
  }
}
@media (max-width: 576px) {
  .sponsor-wrapper {
    flex-wrap: wrap;
  }
}

/*================================================
 Culture-section 
=================================================*/
.culture-section {
  position: relative;
}
.culture-section .bottom-box {
  position: absolute;
  width: 100%;
  max-width: 380px;
  height: 300px;
  background-color: var(--primary-red-light);
  right: 15%;
  bottom: 0px;
}
@media (max-width: 991px) {
  .culture-section .bottom-box {
    display: none;
    visibility: hidden;
  }
}
.culture-section .quote-box {
  background-color: var(--primary-green-dark-light);
  padding: 35px 45px;
  min-height: 380px;
  width: 100%;
  max-width: 450px;
  margin-left: auto;
  margin-right: auto;
  position: relative;
}
@media (max-width: 991px) {
  .culture-section .quote-box {
    margin-top: 30px;
    padding: 25px;
    min-height: 290px;
  }
}
.culture-section .quote-box p {
  color: var(--text-primary);
  font-size: 20px;
  font-weight: 300;
  line-height: 1.3;
  margin-bottom: 10px;
}
@media (max-width: 767px) {
  .culture-section .quote-box p {
    font-size: 17px;
  }
}
.culture-section .quote-box span {
  font-size: 12px;
  font-weight: 700;
  color: var(--text-primary);
}
.culture-section .value-card {
  max-width: 350px;
  margin-top: 90px;
  position: relative;
  background-color: var(--primary-red);
  padding: 45px;
  min-height: 350px;
}
@media (max-width: 991px) {
  .culture-section .value-card {
    margin-bottom: 30px;
    padding: 25px;
    min-height: 250px;
  }
}
.culture-section .value-card h6 {
  color: var(--white);
  font-weight: 600;
  margin-bottom: 0px;
  font-size: 19px;
}
@media (max-width: 767px) {
  .culture-section .value-card h6 {
    font-size: 17px;
  }
}
.culture-section .value-card ul {
  list-style: none;
  margin: 0;
  padding: 0;
}
.culture-section .value-card ul li {
  color: var(--white);
  font-size: 19px;
  line-height: 1.2;
}
@media (max-width: 767px) {
  .culture-section .value-card ul li {
    font-size: 17px;
  }
}
.culture-section .value-card::before {
  content: "";
  position: absolute;
  border: 1px solid var(--border);
  width: 100%;
  max-width: 380px;
  height: 380px;
  z-index: -1;
  left: 90%;
  bottom: 75%;
}
@media (min-width: 1200px) and (max-width: 1399px) {
  .culture-section .value-card::before {
    left: 65%;
  }
}
@media (min-width: 992px) and (max-width: 1199px) {
  .culture-section .value-card::before {
    left: 60%;
  }
}
@media (min-width: 768px) and (max-width: 991px) {
  .culture-section .value-card::before {
    left: 49%;
  }
}
@media (max-width: 767px) {
  .culture-section .value-card::before {
    left: 5%;
    max-width: 280px;
  }
}
.culture-section .value-card img {
  width: 100%;
  -o-object-fit: cover;
     object-fit: cover;
  margin-left: 90px;
}

.about-news-box {
  position: absolute;
  width: 30%;
  right: 30%;
  height: 300px;
  border: 1px solid var(--border);
  top: -90px;
}
@media (min-width: 992px) and (max-width: 1199px) {
  .about-news-box {
    right: 20%;
  }
}
@media (max-width: 991px) {
  .about-news-box {
    right: 10%;
    width: 35%;
  }
}

.trending-right-box {
  position: absolute;
  top: 0px;
  right: 5%;
  width: 100%;
  max-width: 40%;
  height: 380px;
  background-color: var(--primary-red-light);
}

.un-logo-wrap {
  margin-top: 90px;
  display: grid;
  grid-template-columns: repeat(9, 1fr);
  grid-gap: 15px;
  position: relative;
  padding-bottom: 180px;
}
.un-logo-wrap::after {
  content: "";
  position: absolute;
  right: -90px;
  top: -90px;
  background-color: var(--primary-red-light);
  width: 80%;
  height: 485px;
}
@media (min-width: 992px) and (max-width: 1199px) {
  .un-logo-wrap::after {
    right: -30px;
  }
}
@media (max-width: 991px) {
  .un-logo-wrap::after {
    right: 0px;
  }
}
@media (min-width: 768px) and (max-width: 991px) {
  .un-logo-wrap {
    grid-template-columns: repeat(6, 1fr);
    grid-gap: 10px;
    margin-top: 70px;
  }
}
@media (max-width: 767px) {
  .un-logo-wrap {
    grid-gap: 5px;
    grid-template-columns: repeat(6, 1fr);
    margin-top: 60px;
  }
}
.un-logo-wrap .logo-wrap {
  max-width: 130px;
}

.business-card-section {
  position: relative;
}
.business-card-section .business-card-box {
  position: absolute;
  width: 20%;
  height: 210px;
  border: 1px solid var(--border);
  bottom: 150px;
  left: 12%;
}
@media (min-width: 768px) and (max-width: 991px) {
  .business-card-section .business-card-box {
    width: 10%;
    left: 2%;
  }
}
@media (max-width: 767px) {
  .business-card-section .business-card-box {
    display: none;
    visibility: hidden;
    left: 4%;
  }
}
.business-card-section::before {
  content: "";
  position: absolute;
  top: 40%;
  left: calc((100% - 1040px) / 2);
  transform: translateY(-50%);
  width: 28.8%;
  height: 55%;
  background-color: var(--primary-blue-light);
  z-index: -1;
}
@media (min-width: 1200px) and (max-width: 1399px) {
  .business-card-section::before {
    left: calc((100% - 1020px) / 2);
  }
}
@media (min-width: 992px) and (max-width: 1199px) {
  .business-card-section::before {
    left: calc((100% - 950px) / 2);
  }
}
@media (min-width: 768px) and (max-width: 991px) {
  .business-card-section::before {
    left: 0;
  }
}
@media (min-width: 576px) and (max-width: 768px) {
  .business-card-section::before {
    left: 0;
  }
}
@media (max-width: 576px) {
  .business-card-section::before {
    content: none;
  }
}

.address-section {
  background-color: var(--primary-yellow-light2);
}

.address-single h6 {
  font-size: 14px;
  margin-bottom: 0px;
}
.address-single .details {
  min-height: 350px;
  margin-bottom: 25px;
}
@media (max-width: 991px) {
  .address-single .details {
    min-height: auto;
  }
}
.address-single .details p {
  font-size: 14px;
}

.address-list {
  list-style: none;
  padding: 0;
  margin: 0;
  position: relative;
  padding-top: 15px;
  margin-bottom: 30px;
}
.address-list::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  max-width: 90px;
  height: 1px;
  background-color: var(--text-primary);
}
.address-list li {
  font-size: 14px;
  font-weight: 600;
  line-height: 1.3;
}
.address-list li:first-child {
  margin-bottom: 15px;
}
.address-list li:last-child {
  margin-bottom: 0px;
}
.address-list li a {
  color: inherit;
}

.key-contact-list {
  list-style: none;
  padding: 0;
  margin: 0;
  position: relative;
  padding-top: 15px;
}
.key-contact-list::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  max-width: 90px;
  height: 1px;
  background-color: var(--text-primary);
}
.key-contact-list li {
  font-size: 14px;
  font-weight: 600;
  line-height: 1.3;
  margin-bottom: 15px;
}
.key-contact-list li:last-child {
  margin-bottom: 0px;
}
.key-contact-list li a {
  color: inherit;
}
.key-contact-list li span {
  display: block;
  font-size: 14px;
  font-weight: 300;
  line-height: 1;
}

.openday-section {
  padding-left: 8%;
  padding-right: 8%;
}
.openday-section .box-right {
  width: 100%;
  max-width: 550px;
  height: 320px;
  border: 1px solid var(--border);
  margin-top: -90px;
}
.openday-section .openday-apply {
  margin-left: -90px;
  margin-top: -90px;
}
.openday-section .attendents {
  max-width: 280px;
  width: 100%;
  margin-right: auto;
  text-align: right;
}
.openday-section .attendents h2 {
  font-size: 180px;
  margin-bottom: 0px;
  font-weight: 300;
  color: var(--primary-green-dark);
  letter-spacing: -8px;
  position: relative;
  display: inline-block;
  line-height: 1;
}
.openday-section .attendents h2::after {
  content: "\f4d1";
  font-family: "Bootstrap-icons";
  position: absolute;
  right: -35px;
  top: 55px;
  font-size: 45px;
  color: vvar(--primary-green-dark);
}
.openday-section .attendents span {
  display: block;
  width: 100%;
  max-width: 170px;
  margin-left: auto;
  font-size: 12px;
  font-weight: 700;
  text-align: left;
}/*# sourceMappingURL=style.css.map */