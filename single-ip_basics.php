<?php

// Exit if accessed directly.
defined('ABSPATH') || exit;

get_header();

$pll = pll_current_language();
$post_name = get_post(get_the_ID())->post_name;
if (have_posts()) :
    while (have_posts()) : the_post();

        if (get_the_post_thumbnail_url()) {
?><div class="d-flex container-fluid banner-section" style="background:url(<?php echo get_the_post_thumbnail_url(); ?>)  center / cover no-repeat;">
                <div class="container-one position-relative">

                </div>
            </div>
            <div class="banner-single-post-title">
                <div class="container-one">
                    <h1 class="display-44"><?php the_title(); ?></h1>
                    <?php
                       
                        if ($pll == 'es') {
                        ?>
                            <div class="breadcrumps-wrap">
                                <span typeof="v:Breadcrumb"><a rel="v:url" property="v:title" hreflang="<?php echo esc_attr($pll); ?>" title="Servicios" href="/es/experiencia/#services">Servicios</a></span> -
                                <?php if($post_name == 'trade-marks') : ?>
                                <span typeof="v:Breadcrumb"><a rel="v:url" property="v:title" hreflang="<?php echo esc_attr($pll); ?>" title="Marcas" href="/es/service/marcas">Marcas</a></span> -
                                <?php elseif( $post_name == 'disenos' ) : ?>
                                <span typeof="v:Breadcrumb"><a rel="v:url" property="v:title" hreflang="<?php echo esc_attr($pll); ?>" title="Desenos" href="/es/service/disenos-industriales">Desenos</a></span> -
                                <?php elseif( $post_name == 'copyright' ) : ?>
                                <!-- <span typeof="v:Breadcrumb"><a rel="v:url" property="v:title" title="Copyright" href="/es/ip_basics/copyright/">Copyright</a></span> - -->
                                <?php elseif( $post_name == 'patents') : ?>
                                <span typeof="v:Breadcrumb"><a rel="v:url" property="v:title" hreflang="<?php echo esc_attr($pll); ?>" title="Patentes" href="/es/service/patentes/">Patentes</a></span> -
                                <?php else : ?>
                                <span typeof="v:Breadcrumb"><a rel="v:url" property="v:title" hreflang="<?php echo esc_attr($pll); ?>" class="dynamic-breadcrumb" title="Marcas" data-lang="es" href="/es/service/marcas">Marcas</a></span> -
                                <?php endif ?>
                                <span typeof="v:Breadcrumb"><a rel="v:url" property="v:title" hreflang="<?php echo esc_attr($pll); ?>" title="Aspectos básicos de PI" href="/es/aspectos-basicos-de-pi">Aspectos básicos de PI</a></span> -
                                <span typeof="v:Breadcrumb"><span property="v:title"><?php the_title(); ?></span></span>
                            </div>
                        <?php
                        } else {
                           
                        ?>
                            <div class="breadcrumps-wrap">
                                <span typeof="v:Breadcrumb"><a rel="v:url" property="v:title" hreflang="<?php echo esc_attr($pll); ?>" title="Services" href="/expertise/#services">Services</a></span> -
                                <?php if($post_name == 'trade-marks') : ?>
                                <span typeof="v:Breadcrumb"><a rel="v:url" property="v:title" hreflang="<?php echo esc_attr($pll); ?>" title="Trade marks" href="/service/trade-marks">Trade marks</a></span> -
                                <?php elseif( $post_name == 'designs' ) : ?>
                                <span typeof="v:Breadcrumb"><a rel="v:url" property="v:title" hreflang="<?php echo esc_attr($pll); ?>" title="Designs" href="/service/designs">Designs</a></span> -
                                <?php elseif( $post_name == 'patents') : ?>
                                <span typeof="v:Breadcrumb"><a rel="v:url" property="v:title" hreflang="<?php echo esc_attr($pll); ?>" title="Patents" href="/service/patents/">Patents</a></span> -
                                <?php else : ?>
                                <!-- <span typeof="v:Breadcrumb"><a rel="v:url" property="v:title" class="dynamic-breadcrumb" title="Trade marks" href="/trade-marks">Trade marks</a></span> - -->
                                <?php endif ?>
                                <span typeof="v:Breadcrumb"><a rel="v:url" property="v:title" hreflang="<?php echo esc_attr($pll); ?>" title="IP Basics" href="/ip-basics">IP basics</a></span> -
                                <span typeof="v:Breadcrumb"><span property="v:title"><?php the_title(); ?></span></span>
                            </div>
                        <?php
                        }
                        ?>
                    <!-- <div class="banner-single-post-meta"> -->
                    <!-- <span class="post-date title-tag"><strong><?php echo __('IP basics', 'picostrap5-child-base'); ?></strong> </span> -->
                    <!-- <span class="post-date"><?php the_date('d F Y'); ?> </span> -->
                    <!-- </div> -->
                    <?php
                    if ($people = get_field('people')) {
                        $person = $people[0];

                        $reverse_person_photo_places = get_field('reverse_person_photo_places', $person->ID);
                        $image_url = get_field('person_photo_1', $person->ID) ? get_field('person_photo_1', $person->ID)['sizes']['medium_large'] : get_stylesheet_directory_uri() . '/assets/images/team/team.jpg';
                        if ($reverse_person_photo_places) {
                            $image_url = get_field('person_photo_2', $person->ID) ? get_field('person_photo_2', $person->ID)['sizes']['medium_large'] : get_stylesheet_directory_uri() . '/assets/images/team/team.jpg';
                        }

                    ?>
                        <div class="banner-auther">
                            <img src="<?php echo $image_url; ?>" alt="<?php echo get_the_title($person); ?>">
                        </div>
                    <?php
                    }
                    ?>
                </div>

            </div>
        <?php } else {
        ?><div class="d-flex container-fluid banner-section">
                <div class="container-one position-relative">
                    <div class="banner-single-post-title">
                        <h1 class="display-44"><?php the_title(); ?></h1>

                        <?php
                        if ($pll == 'es') {
                        ?>
                            <div class="breadcrumps-wrap">
                                <span typeof="v:Breadcrumb"><a rel="v:url" property="v:title" hreflang="<?php echo esc_attr($pll); ?>" title="Servicios" href="/es/expertise/#services">Servicios</a></span> -
                                <?php if($post_name == 'trade-marks') : ?>
                                <span typeof="v:Breadcrumb"><a rel="v:url" property="v:title" hreflang="<?php echo esc_attr($pll); ?>" title="Marcas" href="/es/service/marcas">Marcas</a></span> -
                                <?php elseif( $post_name == 'disenos' ) : ?>
                                <span typeof="v:Breadcrumb"><a rel="v:url" property="v:title" hreflang="<?php echo esc_attr($pll); ?>" title="Marcas" href="/es/service/disenos-industriales">Desenos</a></span> -
                                <?php elseif( $post_name == 'copyright' ) : ?>
                                <span typeof="v:Breadcrumb"><a rel="v:url" property="v:title" hreflang="<?php echo esc_attr($pll); ?>" title="Copyright">Copyright</a></span> -
                                <?php elseif( $post_name == 'patents') : ?>
                                <span typeof="v:Breadcrumb"><a rel="v:url" property="v:title" hreflang="<?php echo esc_attr($pll); ?>" title="Marcas" href="/es/service/patentes/">Patentes</a></span> -
                                <?php else : ?>
                                <span typeof="v:Breadcrumb"><a rel="v:url" property="v:title" hreflang="<?php echo esc_attr($pll); ?>" class="dynamic-breadcrumb" title="Marcas" data-lang="es" href="/es/trade-marks">Marcas</a></span> -
                                <?php endif ?>
                                <span typeof="v:Breadcrumb"><a rel="v:url" property="v:title" hreflang="<?php echo esc_attr($pll); ?>" title="Aspectos básicos de PI" href="/es/ip-basics">Aspectos básicos de PI</a></span> -
                                <span typeof="v:Breadcrumb"><span property="v:title"><?php the_title(); ?></span></span>
                            </div>
                        <?php
                        } else {
                        ?>
                            <div class="breadcrumps-wrap">
                                <span typeof="v:Breadcrumb"><a rel="v:url" property="v:title" hreflang="<?php echo esc_attr($pll); ?>" title="Services" href="/expertise/#services">Services</a></span> -
                                <?php if($post_name == 'trade-marks') : ?>
                                <span typeof="v:Breadcrumb"><a rel="v:url" property="v:title" hreflang="<?php echo esc_attr($pll); ?>" title="Trade marks" href="/service/trade-marks">Trade marks</a></span> -
                                <?php elseif( $post_name == 'designs' ) : ?>
                                <span typeof="v:Breadcrumb"><a rel="v:url" property="v:title" hreflang="<?php echo esc_attr($pll); ?>" title="Designs" href="/service/designs">Designs</a></span> -
                                <?php else : ?>
                                <span typeof="v:Breadcrumb"><a rel="v:url" property="v:title" hreflang="<?php echo esc_attr($pll); ?>" class="dynamic-breadcrumb" title="Marcas" href="/es/trade-marks">Marcas</a></span> -
                                <?php endif ?>
                                <span typeof="v:Breadcrumb"><a rel="v:url" property="v:title" hreflang="<?php echo esc_attr($pll); ?>" title="IP Basics" href="/ip-basics">IP basics</a></span> -
                                <span typeof="v:Breadcrumb"><span property="v:title"><?php the_title(); ?></span></span>
                            </div>
                        <?php
                        }
                        ?>


                        <div class="banner-single-post-meta">
                            <span class="post-date title-tag"><strong><?php echo __('IP basics', 'picostrap5-child-base'); ?></strong> </span>
                            <span class="post-date"><?php the_date('d F Y'); ?> </span>
                        </div>
                        <?php
                        if ($people = get_field('people')) {
                            $person = $people[0];

                            $reverse_person_photo_places = get_field('reverse_person_photo_places', $person->ID);
                            $image_url = get_field('person_photo_1', $person->ID) ? get_field('person_photo_1', $person->ID)['sizes']['medium_large'] : get_stylesheet_directory_uri() . '/assets/images/team/team.jpg';
                            if ($reverse_person_photo_places) {
                                $image_url = get_field('person_photo_2', $person->ID) ? get_field('person_photo_2', $person->ID)['sizes']['medium_large'] : get_stylesheet_directory_uri() . '/assets/images/team/team.jpg';
                            }

                        ?>
                            <div class="banner-auther">
                                <img src="<?php echo $image_url; ?>" alt="<?php echo get_the_title($person); ?>">
                            </div>
                        <?php
                        }
                        ?>
                    </div>
                </div>
            </div>
        <?php } ?>

        <!-- <div class="container p-5 bg-light" style="margin-top:-100px"> -->
        <div class="container-one content-wrapper">
            <?php
            if ($people = get_field('people')) {
                $person = $people[0];

                $roles = get_the_terms($person->ID, 'people_role');
                $roles = join(', ', wp_list_pluck($roles, 'name'));

            ?>
                <div class="row">
                    <div class="col-sm-12">
                        <div class="author-name-section">
                            <p><?php echo __('Author:', 'picostrap5-child-base'); ?></p>
                            <h6><a hreflang="<?php echo esc_attr($pll); ?>" href="<?php echo get_the_permalink($person); ?>"><?php echo get_the_title($person); ?></a></h6>
                            <span><?php echo $roles; ?> </span>
                            <span class="border-bottom-el"></span>
                        </div>
                    </div>
                </div>
            <?php
            }
            ?>

            <?php
            //CATS
            if (!get_theme_mod("singlepost_disable_entry_cats") &&  has_category()) {
            ?>
                <div class="row text-center">
                    <div class="col-md-12">
                        <div class="entry-categories">
                            <span class="screen-reader-text"><?php _e('Categories', 'picostrap5-child-base'); ?></span>
                            <div class="entry-categories-inner">
                                <?php the_category(' '); ?>
                            </div><!-- .entry-categories-inner -->
                        </div><!-- .entry-categories -->
                    </div><!-- /col -->
                </div>
            <?php
            }

            ?>



            <?php if (!get_theme_mod("singlepost_disable_date") or !get_theme_mod("singlepost_disable_author")) : ?>
                <div class="post-meta" id="single-post-meta">
                    <p class="lead text-secondary">

                        <?php if (!get_theme_mod("singlepost_disable_date")) : ?>
                            <!-- <span class="post-date"><?php the_date(); ?> </span> -->
                        <?php endif; ?>

                        <?php if (!get_theme_mod("singlepost_disable_author")) : ?>
                            <!-- <span class="text-secondary post-author"> <?php _e('by', 'picostrap5-child-base') ?> <?php the_author(); ?></span> -->
                        <?php endif; ?>
                    </p>
                </div>
            <?php endif; ?>


            <div class="row">
                <!-- <div class="col-md-8 offset-md-2"> -->
                <div class="col-md-12">
                    <?php

                    the_content();


                    if (get_theme_mod("enable_sharing_buttons")) picostrap_the_sharing_buttons();

                    edit_post_link(__('Edit this post', 'picostrap5-child-base'), '<p class="text-end">', '</p>');

                    // If comments are open or we have at least one comment, load up the comment template.
                    if (!get_theme_mod("singlepost_disable_comments")) if (comments_open() || get_comments_number()) {
                        comments_template();
                    }

                    ?>

                </div><!-- /col -->
            </div>

            <div class="row">
                <div class="col-sm-12">
                    <div class="experience-box <?php echo get_field('sector_sub_sector') ? '' : 'd-none'; ?>">
                        <h5 class="title"><?php echo __('Sector Experience', 'picostrap5-child-base'); ?> </h5>
                        <?php
                        // echo get_field('sector_sub_sector');

                        $sectors = get_field('sector_sub_sector');
                        $_sectors = $sectors = wp_list_pluck($sectors, 'ID');

                        $arg = array(
                            'post_type' => 'sector',
                            'posts_per_page' => '-1',
                            'post_status' => 'publish',
                            'post_parent' => '0',
                            'post__in' => $sectors,
                        );
                        $s = get_posts($arg);
                        if (empty($s)) {
                            unset($arg['post_parent']);
                            $s = get_posts($arg);
                        }

                        if ($s) {
                            foreach ($s as $sector) {
                        ?>
                                <h6 class="subtitle"><?php echo $sector->post_title; ?></h6>
                                <?php
                                $arg = array(
                                    'post_type' => 'sector',
                                    'posts_per_page' => '-1',
                                    'post_status' => 'publish',
                                    'post_parent' => $sector->ID,
                                    'post__in' => $_sectors,
                                );
                                $sec = get_posts($arg);
                                if ($sec) {
                                    echo '<ul >';
                                    foreach ($sec as $_sector) {
                                ?>
                                        <li><?php echo $_sector->post_title; ?></li>
                            <?php
                                    }
                                    echo '</ul>';
                                }
                            }
                        } else {
                            ?>
                            <h6 class="subtitle"><?php echo __('No sectors listed.', 'picostrap5-child-base'); ?></h6>
                        <?php
                        }
                        ?>
                    </div>
                </div>

                <div class="row">
                    <?php
                    /*if(get_field('people')){
                    ?>
                        <div class="col-sm-12 mt-5">
                            <h3 class="pt-3 mb-50 bt-black border-top border-dark"><?php echo __('People', 'picostrap5-child-base'); ?></h3>
                        </div>
                        <div class="row">
                            <div class="col-md-8 offset-md-2">
                                <div class="row">
                                    <?php 
                                        if(get_field('people')){
                                            global $post;
                                            foreach (get_field('people') as $post) {
                                                setup_postdata($post);
                                                ?>
                                                    <div class="col-sm-3">
                                                        <div class="team-item style-two style-two">
                                                            <div class="team-image">
                                                                <a href="<?php echo get_the_permalink(); ?>">
                                                                    <img src="<?php echo get_field('person_photo_1') ? get_field('person_photo_1')['sizes']['medium_large'] : get_stylesheet_directory_uri() . '/assets/images/team/team.jpg'; ?>" alt="<?php echo get_the_title(); ?>">
                                                                </a>
                                                            </div>
                                                            <div class="team-content">
                                                                <h6><a href="<?php echo get_the_permalink(); ?>"><?php echo get_the_title(); ?></a></h6>
                                                                <span><?php echo $roles; ?> </span>
                                                                <span><?php echo $locations; ?></span>
                                                            </div>
                                                        </div>
                                                    </div>
                                                <?php 
                                            }
                                            wp_reset_postdata();
                                        }
                                    ?>
                                </div>
                            </div>
                        </div>
                    <?php 
                }*/
                    ?>
                </div>
            </div>
            <script>
                let get_service_name = localStorage.getItem('service-name');
                let get_service_slug = localStorage.getItem('service-slug');
                let lang = jQuery('.dynamic-breadcrumb').attr('data-lang');
                if( get_service_name ) {
                	if( lang == 'es' ) {
                		// for patents
                		if( get_service_name == 'Patents' ) {
                			get_service_name = 'Patentes';
                		}
                		// for trade-marks
                		if( get_service_name == 'Trade marks' ) {
                			get_service_name = 'Marcas comerciales';
                		}
                		// for design
                		if( get_service_name == 'Designs' ) {
                			get_service_name = 'Diseños industriales';
                		}
                		// global plant
                		if( get_service_name == 'Global plant breeders’ rights and national listings' ) {
                			get_service_name = 'Derechos de obtención vegetal y registros de variedades';
                		}
                	}   
                	console.log('service-name', get_service_name);
                    jQuery('.dynamic-breadcrumb').text(get_service_name);
                    jQuery('.dynamic-breadcrumb').attr('title', get_service_name);
                }
                if( get_service_slug ) {
                    if( lang == 'es' ) {                        
                        // for patents 
                        if( get_service_slug == '/service/patents/' ){
                        	get_service_slug = '/service/patentes/';
                        } 
                        
                        // for trade-marks 
                        if( get_service_slug == '/service/trade-marks/' ){
                        	get_service_slug = '/service/marcas/';
                        } 
                        
                        // for design
                        if( get_service_slug == '/service/designs/' ){
                        	get_service_slug = '/service/disenos-industriales/';
                        } 
                        
                        // for global plant
                        if( get_service_slug == '/service/global-plant-breeders-rights-and-national-listings/' ){
                        	get_service_slug = '/service/derechos-de-obtencion-vegetal-y-registros-de-variedades/';
                        } 
                        
                        const es_link = '/' + 'es' + get_service_slug;
                        jQuery('.dynamic-breadcrumb').attr('href', es_link);
                    }else{

                        jQuery('.dynamic-breadcrumb').attr('href', get_service_slug);
                    }
                }
            </script>
    <?php
    endwhile;
else :
    _e('Sorry, no posts matched your criteria.', 'picostrap5-child-base');
endif;
    ?>




    <?php get_footer();
