<?php 
/*
    Template Name: Patent Essentials Page
*/

// Exit if accessed directly.
defined( 'ABSPATH' ) || exit;

get_header();
$pll = pll_current_language();

?>

<h1 style="opacity:0; display: none;">hidden</h1>
<div class="content-section pt-240">
        <div class="container-one">
        <div class="row">
            <div class="col-lg-6">
                <div class="section-title-one style-blue title-pb-1500">
                    <h2 ><?php echo get_field('page_title'); ?></h2>
                </div>
                
            </div>
        </div>
    </div>
</div>
<div class="career-details-section pt-1000 pr-container" id="next-section">
    <div class="container-fluid">
        <div class="row g-4">
            <div class="col-lg-7">
                <div class="career-left-image mt-100" >
                    <img src="<?php echo get_field('image')['url'];?>" alt="image">
                </div>
                <div class="choose-card-box"></div>
            </div>
            <div class="col-lg-5 ps-lg-5">
                <div class="content animated-section" >
                    <?php echo get_field('description'); ?>
                </div>
                <!-- <div class="blue-box">
                </div> -->
            </div>
        </div>
    </div>
</div>

<!-- ========== toolkit section start ============= -->

<?php 
    $args = array(
        'post_type' => 'patent_essential',
        'posts_per_page' => '-1',
        'order' => 'DESC',
        'orderby' => 'date',
    );

    $q = new WP_Query($args);

    if($q->have_posts()){
        ?>
            <div class="event-section position-relative pb-100 pt-50">
                <div class="box-design-ten"></div>
                <div class="container-one">
                    <div class="section-title-one style-yellow">
                        <h2 ><?php echo get_field('list_title') ? get_field('list_title') : get_field('page_title'); ?></h2>
                    </div>
                    <div class="row justify-content-lg-start justify-content-center g-sm-4 g-2">
                        <?php 
                            $i = 0;
                            while($q->have_posts()){
                                $q->the_post();
                                ?>
                                    <div class="col-lg-3 col-md-6 col-6" >
                                        <?php //echo get_the_ID();?>
                                        <?php 
                                            $urlpd = '';
                                            if($pll == 'es'){
                                                $urlpd = get_field('pdf')['url'];
                                            }else{
                                                $urlpd = get_the_permalink();
                                            }
                                        ?>
                                        <a hreflang="<?php echo esc_attr($pll); ?>" href="<?php echo get_the_permalink(); ?>">
                                        <div class="sector-item-two style-yellow">
                                            <h6 class="title"><?php echo get_the_title(); ?></h6>
                                            <!-- <p><?php // echo get_the_excerpt(); ?></p> -->
                                            <!-- <span class="date">28 MARCH 2023</span> -->
                                            <div class="arrow-btn">
                                                <img src="<?php echo get_stylesheet_directory_uri(); ?>/assets/images/icons/arrow-white.svg" alt="arrow">
                                            </div>
                                        </div>
                                        </a>
                                    </div>
                                <?php
                                $i++;
                                if($i == 3){
                                    $i = 0;

                                    ?>
                                        <div class="col-lg-3 col-md-6 col-6" >
                                        </div>
                                    <?php 
                                } 
                            }
                        ?>
                    </div>
                </div>
            </div>
        <?php 
    }
?>


<!-- ========== toolkit section start ============= -->



<?php get_footer();