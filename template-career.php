<?php
/*
    Template Name: Career Page
*/

// Exit if accessed directly.
defined('ABSPATH') || exit;
$pll = pll_current_language();
get_header();

?>
<h1 style="opacity:0; display: none;">hidden</h1>
<div class="overflow-hidden position-relative">
    <!-- ========== banner-section start============= -->
    <!-- <div class="banner-section d-flex flex-column align-items-staer justify-content-center"
        style="background-image:linear-gradient(to bottom, rgba(255,255,255,0.05) 0%, rgba(0,0,0,0.15) 50%), radial-gradient(at top center, rgba(255,255,255,0.20) 50%, rgba(0,0,0,0.40) 190%), url('<?php echo get_the_post_thumbnail_url(get_the_ID(), 'full'); ?>')"> -->
    <!-- style="background-image:linear-gradient(to bottom, rgba(255,255,255,0.05) 0%, rgba(0,0,0,0.15) 100%), radial-gradient(at top center, rgba(255,255,255,0.40) 0%, rgba(0,0,0,0.40) 120%), url('<?php echo get_the_post_thumbnail_url(get_the_ID(), 'full'); ?>')"> -->
    <!-- <div class="container text-center">
            <div class="banner-content">
                <h1 ><?php echo get_field('banner_title') ? get_field('banner_title') : get_the_title(); ?></h1>
            </div>
        </div>
    </div> -->
    <div class="banner-video-section overflow-hidden">
        <div class="container-fluid px-0">
            <div class="company-vdo position-relative" style="background-image: url('<?php echo get_the_post_thumbnail_url(get_the_ID(), 'full'); ?>');">
                <div class="video-banner-bg">
                    <?php
                    /*if($banner_video_url = get_field('banner_video_url')){
                            // check if youtube
                            ?>
                                <div data-vbg-autoplay="false" data-vbg-muted="false" data-vbg-play-button="true" data-vbg="<?php echo $banner_video_url; ?>" data-vbg-poster="<?php echo get_the_post_thumbnail_url(get_the_ID(), 'full'); ?>"></div>
                            <?php 
                        }*/
                    ?>
                </div>
                <div class="inner-overlay"></div>
                <!-- style="background-image:linear-gradient(to bottom, rgba(255,255,255,0.05) 0%, rgba(0,0,0,0.15) 50%), radial-gradient(at top center, rgba(255,255,255,0.20) 50%, rgba(0,0,0,0.40) 190%),  url('<?php echo get_the_post_thumbnail_url(get_the_ID(), 'full'); ?>');"> -->
                <!-- style="background-image:linear-gradient(to bottom, rgba(255,255,255,0.05) 0%, rgba(0,0,0,0.15) 100%), radial-gradient(at top center, rgba(255,255,255,0.40) 0%, rgba(0,0,0,0.40) 120%),  url('<?php echo get_the_post_thumbnail_url(get_the_ID(), 'full'); ?>');"> -->
                <h3><?php echo get_field('banner_title') ? get_field('banner_title') : get_the_title(); ?></h3>
                <?php
                if ($banner_video_url = get_field('banner_video_url')) {
                ?>
                    <div class="video-popup-button">
                        <!-- <a href="https://www.youtube.com/watch?v=u31qwQUeGuM" class="video-popup play-icon"><img -->
                        <!-- <a href="https://vimeo.com/user48769004/review/837702986/c569277040" class="video-popup play-icon"><img -->
                        <!-- <a href="<?php echo $banner_video_url; ?>" class="video-popupp play-icon"><img -->
                        <!-- src="<?php echo get_stylesheet_directory_uri(); ?>/assets/images/icons/play-icon.svg" alt="image"></a> -->
                        <a hreflang="<?php echo esc_attr($pll); ?>" href="<?php echo $banner_video_url; ?>" class="video-popup play-icon"><img src="<?php echo get_stylesheet_directory_uri(); ?>/assets/images/icons/play-icon.svg" alt="image"></a>
                    </div>
                <?php
                }
                ?>
            </div>
        </div>
    </div>
    <!-- ========== banner-section end============= -->
    <!-- ==========career-info section start ============= -->
    <?php
    $careers_list = get_field('careers_list');
    if (!empty($careers_list)) :
    ?>
        <div class="career-card-section pt-100">
            <div class="red-box">
            </div>
            <div class="container-one position-relative z-index-11">
                <div class="row">
                    <div class="col-lg-4">
                        <div class="section-title-one style-red">
                            <h2><?php echo __('Careers', 'picostrap5-child-base'); ?></h2>
                        </div>
                        <div><?php the_content(); ?></div>
                    </div>
                    <div class="col-lg-8">
                        <div class="row g-3 gy-3">
                            <?php foreach ($careers_list as $value) : ?>
                                <div class="col-lg-4 col-12">
                                    <div class="career-card style-blue">
                                        <div class="image-wrap">
                                            <img src="<?php echo $value['image']; ?>" alt="image">
                                        </div>
                                        <div style="background: <?php echo $value['box_bg_color']; ?>" class="content">
                                            <h5><?php echo $value['title']; ?></h5>
                                            <p><?php echo $value['content']; ?></p>
                                            <?php if (isset($value['link']) && isset($value['link']['url'])) : ?>
                                                <a hreflang="<?php echo esc_attr($pll); ?>" href="<?php echo $value['link']['url']; ?>" class="eg-btn btn--primary-black style-two btn--lg2"><?php echo $value['link']['title']; ?> <i class="bi bi-arrow-right"></i></a>
                                            <?php endif; ?>
                                        </div>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    <?php endif; ?>
    <?php
    $first_section = get_field('first_section');
    $second_section = get_field('second_section');
    $third_section = get_field('third_section');
    $four_section = get_field('four_section');
    ?>
    <div class="career-info-section pl-container pt-100">
        <div class="container-fluid">
            <div class="row">
                <div class="col-lg-6">
                    <div class="mb-50">
                        <div class="section-title-one style-blue">
                            <h2><?php echo $first_section['title']; ?></h2>
                        </div>
                        <div>

                            <?php echo $first_section['content']; ?>
                        </div>
                    </div>
                    <div class="mb-50">
                        <div class="section-title-one style-blue">
                            <h2><?php echo $second_section['title']; ?></h2>
                        </div>
                        <div>

                            <?php echo $second_section['content']; ?>
                        </div>
                    </div>
                    <div class="mb-50">
                        <div class="section-title-one style-green">
                            <h2><?php echo $third_section['title']; ?></h2>
                        </div>
                        <div>

                            <?php echo $third_section['content']; ?>
                        </div>
                    </div>
                    <div class="mb-50">
                        <div class="section-title-one style-red">
                            <h2><?php echo $four_section['title']; ?></h2>
                        </div>
                        <div>

                            <?php echo $four_section['content']; ?>
                        </div>
                    </div>
                </div>
                <div class="col-lg-6">
                    <div class="career-box-right"></div>
                    <div class="career-right-image">
                        <img src="<?php echo get_field('image_one'); ?>" alt="image">
                    </div>
                    <div class="career-right-image2">
                        <img src="<?php echo get_field('image_two'); ?>" alt="image">
                    </div>
                    <div class="career-box-right2">
                    </div>
                    <div class="career-right-quote d-none">
                        <p><?php // echo get_field('note'); 
                            ?></p>
                    </div>
                    <div class="recent-highlight-quotes">
                        <div class="box-with-border-inner"></div>
                        <div class="box-inner-content">
                            <?php echo get_field('quotes'); ?>
                            <?php
                            $quote_person_image = get_field('quote_person_image');
                            $person_image = get_stylesheet_directory_uri() . '/assets/images/team/team.jpg';
                            if ($quote_person_image) {
                                $roles = get_the_terms($quote_person_image->ID, 'people_role');
                                $roles = join(', ', wp_list_pluck($roles, 'name'));

                                $person_image = get_field('person_photo_1', $quote_person_image->ID) ? get_field('person_photo_1', $quote_person_image->ID)['sizes']['medium_large'] : get_stylesheet_directory_uri() . '/assets/images/team/team.jpg';
                            ?>
                                <div class="team-content quote-inner">
                                    <h6><a hreflang="<?php echo esc_attr($pll); ?>" href="<?php echo get_the_permalink($quote_person_image); ?>"><?php echo get_the_title($quote_person_image); ?></a></h6>
                                    <span><?php echo strtoupper($roles); ?></span>
                                </div>
                            <?php
                            }
                            ?>
                        </div>
                        <div class="box-inner-image">
                            <img src="<?php echo $person_image; ?>" alt="image">
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>


    <!-- ========== career-info section start ============= -->

    <!-- ========== search section start ============= -->

    <div class="search-section style-blue mt-100 d-none">
        <div class="container">
            <div class="search-block">
                <button type="search"><i class="bi bi-search"></i></button>
                <input type="text" placeholder="<?php echo __('CURRENT OPPORTUNITIES', 'picostrap5-child-base'); ?>">
            </div>
        </div>
    </div>

    <!-- ========== search section start ============= -->
</div>

<?php get_footer();
