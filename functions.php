<?php
/*
        _               _                  _____        _     _ _     _   _   _                         
       (_)             | |                | ____|      | |   (_) |   | | | | | |                        
  _ __  _  ___ ___  ___| |_ _ __ __ _ _ __| |__     ___| |__  _| | __| | | |_| |__   ___ _ __ ___   ___ 
 | '_ \| |/ __/ _ \/ __| __| '__/ _` | '_ \___ \   / __| '_ \| | |/ _` | | __| '_ \ / _ \ '_ ` _ \ / _ \
 | |_) | | (_| (_) \__ \ |_| | | (_| | |_) |__) | | (__| | | | | | (_| | | |_| | | |  __/ | | | | |  __/
 | .__/|_|\___\___/|___/\__|_|  \__,_| .__/____/   \___|_| |_|_|_|\__,_|  \__|_| |_|\___|_| |_| |_|\___|
 | |                                 | |                                                                
 |_|                                 |_|                                                                

                                                       
*************************************** WELCOME TO PICOSTRAP ***************************************

********************* THE BEST WAY TO EXPERIENCE SASS, BOOTSTRAP AND WORDPRESS *********************

    PLEASE WATCH THE VIDEOS FOR BEST RESULTS:
    https://www.youtube.com/playlist?list=PLtyHhWhkgYU8i11wu-5KJDBfA9C-D4Bfl

*/

if ((int)$_GET['post'] != 0 && $_GET['action'] == 'edit') $_GET['lang'] = 'all';

function disable_large_image_scaling($threshold, $imagesize, $file, $attachment_id)
{
  // Set a high threshold value to disable large image scaling
  return PHP_INT_MAX;
}
add_filter('big_image_size_threshold', 'disable_large_image_scaling', 10, 4);


// DE-ENQUEUE PARENT THEME BOOTSTRAP JS BUNDLE
add_action('wp_print_scripts', function () {
  wp_dequeue_script('bootstrap5');
}, 100);


function get_main_search_insights_data_ajax_handler()
{
  $author_id  = !empty($_POST['name']) ? sanitize_text_field($_POST['name']) : '';
  $ins_type   = !empty($_POST['insight_typee']) ? sanitize_text_field($_POST['insight_typee']) : '';
  $service    = !empty($_POST['service']) ? sanitize_text_field($_POST['service']) : '';
  $sector     = !empty($_POST['sector']) ? sanitize_text_field($_POST['sector']) : '';

  $args = [
    'posts_per_page' => -1,
    'post_status'    => 'publish',
  ];

  // Handle custom value for "Firm news"
  if ($ins_type === 'news-post') {
    $args['post_type'] = ['news'];
  } else {
    $args['post_type'] = ['bulletin', 'upc_news'];
  }

  $tax_query = ['relation' => 'AND'];
  $meta_query = ['relation' => 'AND'];

  // Only include taxonomy query if it's not the "Firm news" special case
  if (!empty($ins_type) && $ins_type !== 'news-post') {
    $tax_query[] = [
      'taxonomy' => 'insight_type',
      'field'    => 'term_id',
      'terms'    => $ins_type,
    ];
  }

  if (!empty($service)) {
    $tax_query[] = [
      'taxonomy' => 'service',
      'field'    => 'term_id',
      'terms'    => $service,
    ];
  }

  if (!empty($sector)) {
    $tax_query[] = [
      'taxonomy' => 'sectors',
      'field'    => 'term_id',
      'terms'    => $sector,
    ];
  }

  if (!empty($author_id)) {
    $meta_query[] = [
      'key'     => 'people',
      'value'   => '"' . intval($author_id) . '"',
      'compare' => 'LIKE',
    ];
  }

  if (count($tax_query) > 1 || ($ins_type !== 'news-post' && count($tax_query) > 0)) {
    $args['tax_query'] = $tax_query;
  }

  if (count($meta_query) > 1) {
    $args['meta_query'] = $meta_query;
  }

  $query = new WP_Query($args);

  $results = [
    'bulletins' => [],
  ];

  if ($query->have_posts()) {
    while ($query->have_posts()) {
      $query->the_post();
         $post_type = get_post_type($b);
        $post_type_obj = get_post_type_object($post_type);
        $singular_name = $post_type_obj ? $post_type_obj->labels->singular_name : '';

        if ($post_type === 'bulletin') {
            $singular_name = 'IP updates';
        }
      $item = [
        'id'        => get_the_ID(),
        'title'     => get_the_title(),
        'excerpt'   => wp_trim_words(get_the_excerpt(), 15, '...'),
        'permalink' => get_permalink(),
        'post_type' => $singular_name,
        'thumbnail' => has_post_thumbnail() ? get_the_post_thumbnail_url(get_the_ID(), 'medium') : home_url('/wp-content/uploads/2023/06/iStock-97970805-scaled.jpg'),
      ];
      $results['bulletins'][] = $item;
    }
    wp_reset_postdata();
  }

  wp_send_json_success(['results' => $results]);
}
add_action('wp_ajax_get_main_search_insights_data', 'get_main_search_insights_data_ajax_handler');
add_action('wp_ajax_nopriv_get_main_search_insights_data', 'get_main_search_insights_data_ajax_handler');

// add_action('wp_ajax_get_insights_author_list', 'get_insights_people_list');
// add_action('wp_ajax_nopriv_get_insights_author_list', 'get_insights_people_list');

// function get_insights_people_list() {
//     global $wpdb;

//     $search = isset($_POST['search']) ? sanitize_text_field($_POST['search']) : '';
//     if (empty($search)) {
//         wp_send_json_error(['message' => 'No search term provided.']);
//         return;
//     }

//     $used_people = $wpdb->get_col(
//         "SELECT DISTINCT meta_value FROM $wpdb->postmeta WHERE meta_key = 'people' AND meta_value != ''"
//     );

//     $result = [];
//     foreach ($used_people as $serialized) {
//         $unserialized = @unserialize($serialized);
//         if ($unserialized !== false) {
//             $result = array_merge($result, $unserialized);
//         }
//     }
//     $result = array_unique($result);

//     if (!empty($result)) {
//         $args = [
//             'post_type'      => 'people',
//             'post__in'       => $result,
//             'posts_per_page' => -1,
//             's'              => $search,
//         ];
//         add_filter('posts_search', 'author_search_by_title_only', 10, 2);
//         $people = get_posts($args);
//         remove_filter('posts_search', 'author_search_by_title_only');
//     } else {
//         $people = [];
//     }



//     if (!empty($people)) {
//         $output = '<ul>';
//         foreach ($people as $person) {
//             $output .= '<li data-id="' . esc_attr($person->ID) . '">' . esc_html($person->post_title) . '</li>';
//         }
//         $output .= '</ul>';
//         wp_send_json_success(['html' => $output]);
//     } else {
//         wp_send_json_success(['html' => '<span>No matching people found.</span>']);
//     }
// }


function get_insights_people_list()
{
  global $wpdb;

  $used_people = $wpdb->get_col(
    "SELECT DISTINCT meta_value FROM $wpdb->postmeta WHERE meta_key = 'people' AND meta_value != ''"
  );

  $result = [];
  foreach ($used_people as $serialized) {
    $unserialized = @unserialize($serialized);
    if ($unserialized !== false) {
      $result = array_merge($result, $unserialized);
    }
  }
  $result = array_unique($result);

  if (!empty($result)) {
    $args = [
      'post_type'      => 'people',
      'post__in'       => $result,
      'posts_per_page' => -1,
      'orderby'        => 'title',
      'order'          => 'ASC', // 'DESC' for reverse order
    ];
    $people = get_posts($args);
  } else {
    $people = [];
  }

  return $people;
}

/**
 * Filters the search query to only search within post titles.
 */
function author_search_by_title_only($search, $query)
{
  global $wpdb;

  if (!is_admin() && $query->is_search() && !empty($query->query['s'])) {
    $search = $wpdb->prepare(" AND {$wpdb->posts}.post_title LIKE %s ", '%' . $wpdb->esc_like($query->query['s']) . '%');
  }

  return $search;
}


add_action('wp_footer', function () { ?>
  <script type="text/javascript">
    jQuery(document).on('click', '.search-section .search-block input', function() {
      window.open('<?php echo get_permalink(10520); ?>', '_blank');
    });
  </script>

  <script>
    // Get a reference to the iframe element
    var iframe = document.getElementById("thisIframe");

    iframe.onload = function() {
      iframe.style.height = iframe.contentWindow.document.body.scrollHeight + 'px';
    }
    window.addEventListener('resize', function(event) {
      var iframe = document.getElementById("thisIframe");
      iframe.style.height = iframe.contentWindow.document.body.scrollHeight + 'px';
    }, true);
  </script>
  <?php
});

// ENQUEUE THE BOOTSTRAP JS BUNDLE FROM THE CHILD THEME DIRECTORY
add_action('wp_enqueue_scripts', function () {
  //enqueue js in footer, async
  wp_enqueue_style('all', get_stylesheet_directory_uri() . '/assets/css/all.css', array(), 1.0, 'all');
  wp_enqueue_style('bootstrap', get_stylesheet_directory_uri() . '/assets/css/bootstrap.min.css', array(), 1.0, 'all');
  wp_enqueue_style('boxicons', get_stylesheet_directory_uri() . '/assets/css/boxicons.min.css', array(), 1.0, 'all');
  wp_enqueue_style('bootstrap-icons', get_stylesheet_directory_uri() . '/assets/css/bootstrap-icons.css', array(), 1.0, 'all');
  wp_enqueue_style('nice-select', get_stylesheet_directory_uri() . '/assets/css/nice-select.css', array(), 1.0, 'all');

  wp_enqueue_style('jquery-ui', get_stylesheet_directory_uri() . '/assets/css/jquery-ui.css', array(), 1.0, 'all');
  wp_enqueue_style('magnific-popup', get_stylesheet_directory_uri() . '/assets/css/magnific-popup.css', array(), 1.0, 'all');
  wp_enqueue_style('swiper', get_stylesheet_directory_uri() . '/assets/css/swiper-bundle.css', array(), 1.0, 'all');

  wp_enqueue_style('aos-style', 'https://unpkg.com/aos@next/dist/aos.css', array(), 1.0, 'all');

  wp_enqueue_style('main-style', get_stylesheet_directory_uri() . '/assets/css/main/style.css', array(), time(), 'all');
  wp_enqueue_style('main-responsive-desktop', get_stylesheet_directory_uri() . '/assets/css/main/responsive-desktop.css', array(), time(), 'all');
  wp_enqueue_style('main-responsive-imac', get_stylesheet_directory_uri() . '/assets/css/main/responsive-imac.css', array(), time(), 'all');
  wp_enqueue_style('main-responsive-ipad', get_stylesheet_directory_uri() . '/assets/css/main/responsive-ipad.css', array(), time(), 'all');
  wp_enqueue_style('main-responsive-mobile', get_stylesheet_directory_uri() . '/assets/css/main/responsive-mobile.css', array(), time(), 'all');


  wp_enqueue_script('boult-jquery', get_stylesheet_directory_uri() . '/assets/js/jquery-3.6.0.min.js', array(), 1.0, true);
  wp_enqueue_script('boult-bootstrap.bundle', get_stylesheet_directory_uri() . '/assets/js/bootstrap.bundle.min.js', array('jquery'), 1.0, true);
  wp_enqueue_script('boult-nice-select', get_stylesheet_directory_uri() . '/assets/js/jquery.nice-select.js', array('jquery'), 1.0, true);
  wp_enqueue_script('boult-wiper-bundle', get_stylesheet_directory_uri() . '/assets/js/swiper-bundle.min.js', array('jquery'), 1.0, true);

  wp_enqueue_script('aos-script', 'https://unpkg.com/aos@next/dist/aos.js', array('jquery'), 1.0, true);
  wp_enqueue_script('is-in-viewport', get_stylesheet_directory_uri() . '/assets/js/viewport.jquery.js', array('jquery'), 1.0, true);

  wp_enqueue_script('boult-magnific', get_stylesheet_directory_uri() . '/assets/js/jquery.magnific-popup.min.js', array('jquery'), 1.0, true);
  wp_enqueue_script('boult-video-bg', get_stylesheet_directory_uri() . '/assets/js/jquery.youtube-background.js', array('jquery'), 1.0, true);
  wp_enqueue_script('boult-main', get_stylesheet_directory_uri() . '/assets/js/main.js', array('jquery'), time(), true);
}, 101);

// ENQUEUE YOUR CUSTOM JS FILES, IF NEEDED 
add_action('wp_enqueue_scripts', function () {

  //UNCOMMENT next row to include the js/custom.js file globally
  //wp_enqueue_script('custom', get_stylesheet_directory_uri() . '/js/custom.js', array(/* 'jquery' */), null, true); 

  //UNCOMMENT next 3 rows to load the js file only on one page
  //if (is_page('mypageslug')) {
  //    wp_enqueue_script('custom', get_stylesheet_directory_uri() . '/js/custom.js', array(/* 'jquery' */), null, true); 
  // }  

});

// OPTIONAL: ADD MORE NAV MENUS
//register_nav_menus( array( 'third' => __( 'Third Menu', 'picostrap5-child-base' ), 'fourth' => __( 'Fourth Menu', 'picostrap5-child-base' ), 'fifth' => __( 'Fifth Menu', 'picostrap5-child-base' ), ) );
// THEN USE SHORTCODE:  [lc_nav_menu theme_location="third" container_class="" container_id="" menu_class="navbar-nav"]


add_action('admin_head', function () {
  //echo '<style>#wp-admin-bar-languages{display:none!important;}</style>';
});


function config_add_nav_menus()
{
  register_nav_menus(array(
    'chinese_main_menu' => 'Chinese Main Menu',
    'japanese_main_menu' => 'Japanese Main Menu',
    'korean_main_menu' => 'Korean Main Menu',
    'german_main_menu' => 'German Main Menu',
  ));
}
add_action('init', 'config_add_nav_menus');

add_action('init', function () {



  global $wpdb;

  if ($_GET['reset_patner'] == 'yes') {
    exit;

    $sql = "SELECT * FROM nw1_posts WHERE post_type = 'sector' AND  ID IN (SELECT post_id FROM nw1_postmeta_copy);";
    $results = $wpdb->get_results($sql);

    foreach ($results as $result) {


      $sql = "SELECT * FROM nw1_term_taxonomy WHERE taxonomy = 'post_translations' AND description LIKE '%:" . $result->ID . ";%' LIMIT 1";
      $results3 = $wpdb->get_results($sql);

      //$permalink = get_permalink($result->ID);

      if ($results3) {

        $description = unserialize($results3[0]->description);


        $sql = "SELECT * FROM nw1_postmeta_copy WHERE post_id = '{$description['en']}' LIMIT 1;";

        $results2 = $wpdb->get_results($sql);

        if ($results2) {

          $decx = array_search($result->ID, $description);

          $undata = unserialize($results2[0]->meta_value);

          $this_lan = [];
          foreach ($undata as $undat) {

            $sql = "SELECT * FROM nw1_term_taxonomy WHERE taxonomy = 'post_translations' AND description LIKE '%:" . $undat . ";%' LIMIT 1";
            $results4 = $wpdb->get_results($sql);

            $descriptiont = unserialize($results4[0]->description);

            $this_lan[] = $descriptiont[$decx];
          }



          echo '<p>' . $result->ID . '---';
          print_r($this_lan);
          echo '</p>';


          update_post_meta($result->ID, 'partners', $this_lan);
        }
      }
    }
    exit;
  }


  if ($_GET['auto_srcipt'] == 'yes') {

    exit;


    $autos = get_posts(array('post_type' => 'people', 'posts_per_page' => -1, 'post_status' => 'publish'));
    foreach ($autos as $auto) {
      $qualifications = get_post_meta($auto->ID, 'qualifications', true);
      $qualifications = str_replace('Registered Patent Attorney', 'Chartered Patent Attorney', $qualifications);
      $qualifications = str_replace('Registered Trade Mark Attorney', 'Chartered Trade Mark Attorney', $qualifications);
      update_post_meta($auto->ID, 'qualifications', $qualifications);
    }


    exit;
  }
}, 0);


add_action('wp_footer', function () {
  global $post;

  $german_pages_footer = get_field('the_pages_footer', 'options');
  $gepages = explode(',', $german_pages_footer);

  if (in_array($post->ID, $gepages)) { ?>
    <script type="text/javascript">
      jQuery(function($) {
        jQuery('li.nav-item-8322>a').html('Team');
        //jQuery('li.nav-item-8323>a').html('EPG');
        //jQuery('li.nav-item-9091>a, li.nav-item-9019>a').html('Karriere');
        jQuery('li.nav-item-8324>a').html('Über uns');
      });
    </script>
  <?php
  }
}, 999999999);

// CHECK PARENT THEME VERSION as Bootstrap 5.2 requires an updated SCSSphp, so picostrap5 v2 is required
add_action('admin_notices', function () {
  if ((pico_get_parent_theme_version()) >= 2) return;
  $class = 'notice notice-error';
  $message = __('This Child Theme requires at least Picostrap Version 2 for the SCSS compiler to work properly. Please update the parent theme.', 'picostrap5-child-base');
  printf('<div class="%1$s"><h1>%2$s</h1></div>', esc_attr($class), esc_html($message));
});

// FOR SECURITY: DISABLE APPLICATION PASSWORDS. Remove if needed (unlikely!)
add_filter('wp_is_application_passwords_available', '__return_false');

// ADD YOUR CUSTOM PHP CODE DOWN BELOW /////////////////////////
class bootstrap_5_wp_nav_menu_walker_extended extends Walker_Nav_menu
{
  private $current_item;
  private $dropdown_menu_alignment_values = [
    'dropdown-menu-start',
    'dropdown-menu-end',
    'dropdown-menu-sm-start',
    'dropdown-menu-sm-end',
    'dropdown-menu-md-start',
    'dropdown-menu-md-end',
    'dropdown-menu-lg-start',
    'dropdown-menu-lg-end',
    'dropdown-menu-xl-start',
    'dropdown-menu-xl-end',
    'dropdown-menu-xxl-start',
    'dropdown-menu-xxl-end'
  ];

  function start_lvl(&$output, $depth = 0, $args = null)
  {
    $dropdown_menu_class[] = '';
    foreach ($this->current_item->classes as $class) {
      if (in_array($class, $this->dropdown_menu_alignment_values)) {
        $dropdown_menu_class[] = $class;
      }
    }
    $indent = str_repeat("\t", $depth);
    $submenu = ($depth > 0) ? ' sub-menu' : '';
    // $output .= "\n$indent<ul class=\"dropdown-menu$submenu sub-menu" . esc_attr(implode(" ",$dropdown_menu_class)) . " depth_$depth\">\n";
    $output .= "\n$indent<ul class=\"sub-menu$submenu " . esc_attr(implode(" ", $dropdown_menu_class)) . " depth_$depth\">\n";
  }

  function start_el(&$output, $item, $depth = 0, $args = null, $id = 0)
  {
    $this->current_item = $item;

    $indent = ($depth) ? str_repeat("\t", $depth) : '';

    $li_attributes = '';
    $class_names = $value = '';

    $classes = empty($item->classes) ? array() : (array) $item->classes;

    $classes[] = ($args->walker->has_children) ? 'dropdown' : '';
    $classes[] = 'nav-item';
    $classes[] = 'nav-item-' . $item->ID;
    if ($depth && $args->walker->has_children) {
      //$classes[] = 'dropdown-menu dropdown-menu-end'; // standard
      $classes[] = 'dropend dropdown-menu-end'; //  patch #1
    }

    $class_names =  join(' ', apply_filters('nav_menu_css_class', array_filter($classes), $item, $args));
    $class_names = ' class="' . esc_attr($class_names) . '"';

    $id = apply_filters('nav_menu_item_id', 'menu-item-' . $item->ID, $item, $args);
    $id = strlen($id) ? ' id="' . esc_attr($id) . '"' : '';

    $output .= $indent . '<li ' . $id . $value . $class_names . $li_attributes . '>';

    $attributes = !empty($item->attr_title) ? ' title="' . esc_attr($item->attr_title) . '"' : '';
    $attributes .= !empty($item->target) ? ' target="' . esc_attr($item->target) . '"' : '';
    $attributes .= !empty($item->xfn) ? ' rel="' . esc_attr($item->xfn) . '"' : '';
    $attributes .= !empty($item->url) ? ' href="' . esc_attr($item->url) . '"' : '';

    $active_class = ($item->current || $item->current_item_ancestor || in_array("current_page_parent", $item->classes, true) || in_array("current-post-ancestor", $item->classes, true)) ? 'active' : '';
    // $nav_link_class = ( $depth > 0 ) ? 'dropdown-item ' : 'nav-link '; //patch #2 is the row below
    $nav_link_class = ($depth > 0) ? '  ' : 'nav-link '; //patch #2 is the row below
    //$attributes.=( $args->walker->has_children ) ? ' class="'. $nav_link_class . $active_class . ' dropdown-toggle" data-bs-toggle="dropdown" aria-haspopup="true" aria-expanded="false"' : ' class="'. $nav_link_class . $active_class . '"';
    // $attributes .= ( $args->walker->has_children ) ? ' class="'. $nav_link_class . $active_class . ' dropdown-toggle" data-bs-toggle="dropdown" data-bs-auto-close="outside" aria-haspopup="true" aria-expanded="false"' : ' class="'. $nav_link_class . $active_class . '"';
    $attributes .= ($args->walker->has_children) ? ' class="' . $nav_link_class . $active_class . ' " ' : ' class="' . $nav_link_class . $active_class . '"';

    $item_output = $args->before;
    $item_output .= '<a' . $attributes . '>';
    $item_output .= $args->link_before . apply_filters('the_title', $item->title, $item->ID) . $args->link_after;
    $item_output .= '</a>';
    $item_output .= $args->after;

    $output .= apply_filters('walker_nav_menu_start_el', $item_output, $item, $depth, $args);
  }
}

function picostrap_all_excerpts_get_more_link($post_excerpt)
{
  /*if ( ! is_admin() OR ( isset($_POST['action']) && $_POST['action'] == 'lc_process_dynamic_templating_shortcode') ) {
    $post_excerpt = $post_excerpt . '...<p class="text-end"><a class="btn btn-outline-secondary picostrap-read-more-link mt-3" href="' . esc_url( get_permalink( get_the_ID() ) ) . '">' . __(
      'Read More...',
      'picostrap5-child-base'
    ) . '</a></p>';
  }*/
  return $post_excerpt;
}

if (function_exists('acf_add_options_page')) {

  acf_add_options_page(array(
    'page_title'    => 'Theme General Settings',
    'menu_title'    => 'Theme Settings',
    'menu_slug'     => 'theme-general-settings',
    'capability'    => 'edit_posts',
    'redirect'      => false
  ));

  // acf_add_options_sub_page(array(
  //     'page_title'    => 'Theme Header Settings',
  //     'menu_title'    => 'Header',
  //     'parent_slug'   => 'theme-general-settings',
  // ));

  acf_add_options_sub_page(array(
    'page_title'    => 'Theme Footer Settings',
    'menu_title'    => 'Footer',
    'parent_slug'   => 'theme-general-settings',
  ));

  acf_add_options_sub_page(array(
    'page_title'    => 'Image Placeholders',
    'menu_title'    => 'Image Placeholders',
    'parent_slug'   => 'theme-general-settings',
  ));

  acf_add_options_sub_page(array(
    'page_title'    => 'Search Keywords',
    'menu_title'    => 'Search Keywords',
    'parent_slug'   => 'theme-general-settings',
  ));
}

add_filter('acf/load_value/name=email', 'config_boult_acf_load_value', 10, 3);
function config_boult_acf_load_value($value, $post_id, $field)
{
  $post = get_post($post_id);
  if ($post->post_type != 'people') {
    return $value;
  }

  if (is_string($value)) {
    $value = strtolower($value);
  }
  return $value;
}

// image placegolders
add_filter('has_post_thumbnail', function ($has_thumbnail, $post, $thumbnail_id) {
  if (is_int($post)) {
    $post = get_post($post);
  }
  if ('news' == $post->post_type) {
    return true;
  }
  if ('bulletin' == $post->post_type) {
    return true;
  }
  if ('upc_news' == $post->post_type) {
    return true;
  }

  return $has_thumbnail;
}, 100, 3);

add_filter('post_thumbnail_id', function ($thumbnail_id, $post) {
  if ('news' == $post->post_type && !$thumbnail_id) {
    $news_gallery = get_field('news_gallery', 'option');
    $image = array_rand($news_gallery, 1);
    return $news_gallery[$image]['ID'];
  }
  if ('bulletin' == $post->post_type && !$thumbnail_id) {
    $bulletin_gallery = get_field('bulletin_gallery', 'option');
    $image = array_rand($bulletin_gallery, 1);
    return $bulletin_gallery[$image]['ID'];
  }
  if ('upc_news' == $post->post_type && !$thumbnail_id) {
    $upc_news_gallery = get_field('upc_news_gallery', 'option');
    $image = array_rand($upc_news_gallery, 1);
    return $upc_news_gallery[$image]['ID'];
  }

  return $thumbnail_id;
}, 10, 2);


add_action('wp_ajax_get_main_search_people_data', 'get_main_search_people_data_ajax_handler'); // wp_ajax_{action}
add_action('wp_ajax_nopriv_get_main_search_people_data', 'get_main_search_people_data_ajax_handler'); // wp_ajax_nopriv_{action}
function get_main_search_people_data_ajax_handler()
{
  if (!function_exists('config_wpdocs_search_by_title_only')) {
    function config_wpdocs_search_by_title_only($search, $wp_query)
    {
      global $wpdb;

      if (empty($search)) {
        return $search; // skip processing - no search term in query
      }

      $q = $wp_query->query_vars;
      $n = ! empty($q['exact']) ? '' : '%';
      $search = '';
      $searchand = '';

      foreach ((array) $q['search_terms'] as $term) {
        $term = esc_sql($wpdb->esc_like($term));
        $search .= "{$searchand}($wpdb->posts.post_title LIKE '{$n}{$term}{$n}')";
        $searchand = ' AND ';
      }

      if (! empty($search)) {
        $search = " AND ({$search}) ";
        if (! is_user_logged_in())
          $search .= " AND ($wpdb->posts.post_password = '') ";
      }

      return $search;
    }
  }
  add_filter('posts_search', 'config_wpdocs_search_by_title_only', 500, 2);

  if (!function_exists('config_boult_posts_groupby')) {
    function config_boult_posts_groupby($groupby, $wp_query)
    {
      global $wpdb;

      $q = $wp_query->query_vars;
      if (isset($q['orderby']) && $q['orderby'] == 'meta_value') {
        $groupby = "{$wpdb->posts}.ID, {$wpdb->postmeta}.meta_value";
      }

      return $groupby;
    }
  }
  // add_filter( 'posts_groupby', 'config_boult_posts_groupby', 500, 2 );


  $r = array(
    'status' => 'ok',
    'message' => '',
    'html' => '',
    'data' => [],
    'roles' => [],
  );



  $post_ids = get_posts($args);


  $available_roles = array();

  $name = trim($_POST['name']);
  $role = $_POST['role'];
  $location = $_POST['location'];
  $sector = $_POST['sector'];



  $args = array(
    'post_type' => 'people',
    'posts_per_page' => '-1',
    'post_status' => 'publish',
    'orderby' => 'title',
    'orderby' => 'meta_value',
    'order' => 'ASC',
    'meta_key' => 'last_name',
  );


  // if($sector) $sectorp = get_post((int)$sector);






  if ($name != '') {
    $args['s'] = $name;
  }

  $q1_ids = array();
  $q2_ids = array();
  // if(isset($args['s'])){
  //   $q1 = new WP_Query($args);
  //   $q1_ids = wp_list_pluck($q1->posts, 'ID');
  //   unset($args['s']);
  // }

  $role_tax = false;
  if (!empty($role)) {



    $role_tax = array(
      'taxonomy' => 'people_role',
      'taxonomy' => 'group_listing',
      'field'    => 'term_id',
      'terms'    => [$role],
      'operator' => 'IN',
    );
  }
  if ($role_tax) {
    $args['tax_query'][] = $role_tax;
  }

  $location_tax = false;
  if (!empty($location)) {


    $location_tax = array(
      'taxonomy' => 'location',
      'field'    => 'term_id',
      'terms'    => [$location],
      'operator' => 'IN',
    );
  }
  if ($location_tax) {
    $args['tax_query'][] = $location_tax;
  }


  $trade_args = array(
    'post_type'      => 'people', // Replace with your custom post type
    'posts_per_page' => -1,              // Retrieve all posts
    'fields'         => 'ids',           // Retrieve only post IDs
    'meta_query'     => array(
      array(
        'key'     => 'sectors',
        'value'   => '"25"',
        'compare' => 'LIKE',
      ),
    ),
  );

  $people_ids_for_trade = get_posts($trade_args);
  // print_r( $people_ids_for_trade );
  // die();

  $sector_meta = false;
  /*if(!empty($sector)){

    // if($sectorp->post_title != 'Trade marks'){
    
    if(!in_array($sector, array(pll_get_post(25)))){
      if(in_array($location, [pll_get_term(261)]) || in_array($location, [pll_get_term(257)])){
        // do nothing
      }
      else{
        $sector_meta = array(
          'relation' => 'AND',
          array(
          'key'     => 'sectors',
          'value'   => '"'.$sector.'"',
          'compare' => 'LIKE',
          ),
          array(
          'key'     => 'sectors',
          // 'value'   => '"25"',
          'value'   => '"'. pll_get_post(25) .'";',
          'compare' => 'NOT LIKE',
        )
        );
      }*/

  // taxonomy query for location
  /*$sector_tax = false;
      if($location == ''){
        $sector_tax = array(
          'taxonomy' => 'location',
          'field'    => 'term_id',
          'terms'    => [pll_get_term(261), pll_get_term(257)],
          'operator' => 'NOT IN',
        );
      }

      if($sector_tax){
        $args['tax_query'][] = $sector_tax;
      }*/

  /*}else{
      if(in_array($location, [pll_get_term(261)]) || in_array($location, [pll_get_term(257)])){
        // do nothing
      }
      else{
        $sector_meta = array(
          'key'     => 'sectors',
          'value'   => '"'.$sector.'"',
          'compare' => 'LIKE',
        );
      }
    }*/
  // }

  if (!empty($sector)) {
    $sector_meta = array(
      'key'     => 'sectors',
      'value'   => '"' . $sector . '"',
      'compare' => 'LIKE',
    );
  }
  if ($sector_meta) {
    $args['meta_query'][] = $sector_meta;
  }

  // if(isset($args['tax_query']) || isset($args['meta_query'])){
  //   $q2 = new WP_Query($args);
  //   $q2_ids = wp_list_pluck($q2->posts, 'ID');
  //   unset($args['tax_query']);
  //   unset($args['meta_query']);
  // }




  if (!empty($q1_ids) || !empty($q2_ids)) {
    $all_ids = array_merge($q1_ids, $q2_ids);
    $all_ids = array_unique($all_ids);

    $args['post__in'] = $all_ids;

    // echo '<pre>';
    // print_r($args);
    // echo '</pre>';
    $q = new WP_Query($args);
  } elseif ($name != '') {
    $args['s'] = $name;

    // echo '<pre>';
    // print_r($args);
    // echo '</pre>';
    $q = new WP_Query($args);
  } else {
    // echo '<pre>';
    // print_r($args);
    // echo '</pre>';
    $q = new WP_Query($args);
  }

  // $q = get_posts($args);
  // ob_start();
  if ($q->have_posts()) {
    while ($q->have_posts()) {
      // if($q){
      // foreach($q as $item){
      $q->the_post();
      // echo get_the_title();
      $item = get_post(get_the_ID());
      $item_id = $item->ID;


      $group_listing = get_the_terms($item_id, 'group_listing');
      $available_roles = array_merge($available_roles, wp_list_pluck($group_listing, 'term_id'));

      $_group_listing = get_the_terms($item_id, 'group_listing');
      $_locations = get_the_terms($item_id, 'location');
      $locations = join(', ', wp_list_pluck($_locations, 'name'));

      $get_locations_array = get_only_term_id($_locations);
      $get_roles_array = get_only_term_id($_group_listing);
      if (!empty($role) || !empty($sector) || !empty($location)) {
        if (!in_array($role, $get_roles_array) && !in_array($location, $get_locations_array) && ($sector != 25 && count($post_ids) > 0 && in_array(get_the_ID(), $people_ids_for_trade))) {
          continue;
        }
      }

      $_location_ids = get_the_terms($item_id, 'location');
      $location_ids = wp_list_pluck($_locations, 'term_id');

      $sectors = get_field('sectors');
      $sectors = wp_list_pluck($sectors, 'ID');

      /*if(!empty($location) || !empty($sector) || !empty($role) || !empty($name)){
          // if(empty($location) || (in_array($location, [pll_get_term(261)])) || in_array($location, [pll_get_term(257)])){
          if((in_array($location, [pll_get_term(261)])) || in_array($location, [pll_get_term(257)])){
            // if(in_array($sector, array(pll_get_post(25))) && (!in_array(pll_get_term(261), $location_ids) || !in_array(pll_get_term(257), $location_ids))){
            //   continue;
            // }
          }
          else{
            if(!in_array($sector, array(pll_get_post(25))) && in_array(pll_get_post(25), $sectors)){
              continue;
            }
          }
        }*/

      $_roles = get_the_terms($item_id, 'people_role');
      // $available_roles = array_merge($available_roles, wp_list_pluck($_roles, 'term_id'));
      $roles = join(', ', wp_list_pluck($_roles, 'name'));



      /* ?>
            <div class="col" >
                <div class="team-item style-two style-two">
                    <div class="team-image">
                        <a href="<?php echo get_the_permalink(); ?>">
                            <img src="<?php echo get_field('person_photo_1') ? get_field('person_photo_1')['sizes']['medium_large'] : get_stylesheet_directory_uri() . '/assets/images/team/team.jpg'; ?>" alt="<?php echo get_the_title(); ?>">
                        </a>
                    </div>
                    <div class="team-content">
                        <h6><a href="<?php echo get_the_permalink(); ?>"><?php echo get_the_title(); ?></a></h6>
                        <span><?php echo $roles; ?> </span>
                        <span><?php echo $locations; ?></span>
                    </div>
                </div>
            </div>
        <?php */

      $reverse_person_photo_places = get_field('reverse_person_photo_places', $item_id);
      $image_url = get_field('person_photo_2', $item_id) ? get_field('person_photo_2', $item_id)['sizes']['medium_large'] : get_stylesheet_directory_uri() . '/assets/images/team/team.jpg';
      if ($reverse_person_photo_places) {
        $image_url = get_field('person_photo_1', $item_id) ? get_field('person_photo_1', $item_id)['sizes']['medium_large'] : get_stylesheet_directory_uri() . '/assets/images/team/team.jpg';
      }

      $r['data'][] = [
        'permalink' => get_the_permalink($item),
        'image' => $image_url,
        'title' => get_the_title($item),
        'roles' => $roles,
        'roles_data' => $group_listing, // $_roles,
        'locations' => $locations,
        'locations_data' => $_locations,
      ];
    }
  }

  $terms = get_terms(array(
    'taxonomy'   => 'people_role',
    'taxonomy'   => 'group_listing',
    'hide_empty' => true,
    'orderby' => 'name',
    'orderby' => 'count',
    'orderby' => 'meta_value',
    'meta_key' => 'order',
    'order' => 'DESC',
    'order' => 'ASC',
    'number' => 0,
    'fields' => 'all',
    'include' => join(',', $available_roles),
  ));

  if (!empty($terms)) {
    $r['roles'] = $terms;
  }


  // $r['html'] = ob_get_clean();

  $json = json_encode($r);
  echo $json;
  die();
}



add_filter('the_title', function ($post_title, $post_id) {
  if (get_post_type($post_id) != 'people') {
    return $post_title;
  }

  $name_title = get_field('name_title', $post_id);
  $name_title = trim($name_title);
  if (!$name_title) {
    return $post_title;
  }

  // check if any prefix already available
  if (strpos($post_title, $name_title) === 0) {
    return $post_title;
  }

  return $name_title . ' ' . $post_title;
}, 10, 2);

add_action('wp_footer', function () {
  ?>
  <script type="text/javascript">
    jQuery(function($) {
      var main_search_ajax = null;
      var ajax_loader_html = '<div class="main-search-ajax-loader"><i class="fa fa-spinner fa-spin"></i></div>';

      $('#main_search').keypress(function(e) {
        /*if (e.shiftKey || e.ctrlKey || e.altKey) {
          e.preventDefault();
        } else {
          var key = e.keyCode;
          if (!((key == 8) || (key == 32) || (key == 46) || (key >= 35 && key <= 40) || (key >= 65 && key <= 90))) {
            e.preventDefault();
          }
        }*/
        if (e.keyCode == 13) {
          e.preventDefault();
        }
      });

      var typingTimer; //timer identifier
      var doneTypingInterval = 1000; //time in ms, 5 seconds for example
      var $input = $('#main_search');

      //on keyup, start the countdown
      $input.on('keyup', function(event) {
        event.preventDefault();
        clearTimeout(typingTimer);
        typingTimer = setTimeout(doneTyping, doneTypingInterval);
      });

      //on keydown, clear the countdown 
      $input.on('keydown', function() {
        clearTimeout(typingTimer);
      });

      //user is "finished typing," do something
      function doneTyping() {
        //do something
        //$(document).on('keyup', '#main_search', function(event){
        //event.preventDefault();
        let keyword = $('#main_search').val();
        var data = {
          'action': 'get_main_search_data',
          'keyword': keyword,
          'type': $(document).find('.search-option-filters input[name="type"]:checked').val(),
        };
        main_search_ajax = $.ajax({ // you can also use $. post here
          beforeSend: function() {
            if (main_search_ajax != null) {
              main_search_ajax.abort();
              main_search_ajax = null;
            }

            $('#filtered-data').empty().html(ajax_loader_html);
          },
          url: '<?php echo admin_url("admin-ajax.php"); ?>', // AJAX handler
          data: data,
          type: 'POST',
          success: function(data) {
            if (keyword) {
              $('#filtered-data').empty().html(data);
            } else {
              $('#filtered-data').empty();
            }
          }
        });
        //});
      }

      $(document).on('change', '.search-option-filters input[name="type"]', function(e) {
        e.preventDefault();

        $(document).find('#main_search').trigger('keyup');
      });

      // team search start
      // ======================
      $(document).on('keyup', '#home-page-banner-search-form [name="keyword"]', function(e) {
        $(this).closest('form').trigger('submit');
      });
      $(document).on('submit', '#home-page-banner-search-form', function(e) {
        e.preventDefault();

        var this_form = $(this);
        var keyword = this_form.find('[name="keyword"]').val();

        var data = {
          'action': 'get_main_search_data',
          'keyword': keyword,
          'type': 'all',
          'action_from': 'inpage',
        };
        main_search_ajax = $.ajax({ // you can also use $. post here
          beforeSend: function() {
            if (main_search_ajax != null) {
              main_search_ajax.abort();
              main_search_ajax = null;
            }

            $('#home-page-banner-search-list').empty().html(ajax_loader_html);
          },
          url: '<?php echo admin_url("admin-ajax.php"); ?>', // AJAX handler
          data: data,
          type: 'POST',
          success: function(data) {
            data = '<div class="text-end"><a href="javascript:;" class="close-home-search">&times;</a></div>' + data;

            if (keyword) {
              $('#home-page-banner-search-list').empty().html(data);
            } else {
              $('#home-page-banner-search-list').empty();
            }

          }
        });
      });
      $(document).on('click', '.close-home-search', function(e) {
        $('#home-page-banner-search-list').empty();
        $('#home-page-banner-search-form [name="keyword"]').val('');
      });
      // team search end
      // ======================

      // for people list sorting
      var sortList = function(ul) {
        // var ul = document.getElementById(ul);

        Array.from(ul.getElementsByTagName("LI"))
          .sort((a, b) => a.textContent.localeCompare(b.textContent))
          .forEach(li => ul.appendChild(li));
      };

      $(document).find('.single-people .experience-box > ul').each(function() {
        var this_ul = $(this);

        sortList(this_ul[0]);
      });
    });
  </script>
<?php
}, 9999999999999999999);


function get_user_location($ip)
{
  // Use a free IP Geolocation API (e.g., ip-api.com)
  $location_data = @file_get_contents("http://ip-api.com/json/$ip");
  if ($location_data) {
    $location_data = json_decode($location_data, true);
    if ($location_data['status'] === 'success') {
      return array(
        'country'  => $location_data['country'] ?? '',
        'region'   => $location_data['regionName'] ?? '',
        'city'     => $location_data['city'] ?? '',
        'latitude' => $location_data['lat'] ?? '',
        'longitude' => $location_data['lon'] ?? '',
        'timezone' => $location_data['timezone'] ?? '',
      );
    }
  }
  return null;
}

add_action('wp_ajax_get_main_search_data', 'get_main_search_data_ajax_handler'); // wp_ajax_{action}
add_action('wp_ajax_nopriv_get_main_search_data', 'get_main_search_data_ajax_handler'); // wp_ajax_nopriv_{action}
function get_main_search_data_ajax_handler()
{
  global $wpdb;

  $keyword = isset($_POST['keyword']) ? sanitize_text_field($_POST['keyword']) : '';
  $type = isset($_POST['type']) ? sanitize_text_field($_POST['type']) : '';
  $action_from = isset($_POST['action_from']) ? sanitize_text_field($_POST['action_from']) : 'popup';
  if (empty($keyword)) {
    return;
  }
  // Normalize specific keywords
  if (strtolower($keyword) == 'ai') $keyword = 'artificial';
  if (strtolower($keyword) == 'registered upc representative') $keyword = 'UPC';

  // Search keyword mapping
  $sk = array();
  $search_keywords = get_field('search_keywords', 'option');
  if (!empty($search_keywords)) {
    foreach ($search_keywords as $key => $value) {
      if (!empty(trim($value['search_term'])) && !empty(trim($value['search_for_terms']))) {
        $sk[strtolower(trim($value['search_term']))] = str_replace(array(' ', ','), array('+', '|'), strtolower(trim($value['search_for_terms'])));
      }
    }
  }

  if (!empty($sk) && isset($sk[strtolower(trim($keyword))])) {
    $keyword = $sk[strtolower(trim($keyword))];
  }

  $no_results = true;

  // Custom search function
  if (!function_exists('_config_wpdocs_search_by_title_only')) {
    function _config_wpdocs_search_by_title_only($search, $wp_query)
    {
      global $wpdb;

      if (empty($search)) {
        return $search; // Skip processing - no search term in query
      }

      $q = $wp_query->query_vars;
      $n = !empty($q['exact']) ? '' : '%';
      $search = '';
      $searchand = '';

      $q['search_terms'] = implode(' ', (array)$q['search_terms']);
      foreach ((array) $q['search_terms'] as $term) {
        $term = esc_sql($wpdb->esc_like($term));
        $search .= "{$searchand}($wpdb->posts.post_title REGEXP '{$term}')";
        $searchand = ' AND ';
      }

      if (!empty($search)) {
        $search = " AND ({$search}) ";
        if (!is_user_logged_in())
          $search .= " AND ($wpdb->posts.post_password = '') ";
      }
      return $search;
    }
  }
  add_filter('posts_search', '_config_wpdocs_search_by_title_only', 500, 2);

  // Fetch results based on post type
  // ... (existing code for fetching people, bulletins, news, sectors, services, and pages)

  // Check if the keyword already exists in search_logs
  // Check if the search keyword already exists in the logs
  $existing_log = get_posts(array(
    'post_type'  => 'search_logs',
    'meta_query' => array(
      array(
        'key'   => 'search_keyword',
        'value' => $keyword,
        'compare' => '='
      )
    ),
    'posts_per_page' => 1,
  ));
  // Get the user's IP address
  $user_ip = $_SERVER['REMOTE_ADDR'] ?? 'unknown';

  // Fetch location details
  $location = get_user_location($user_ip);

  // Prepare the location string
  $location_string = $location
    ? "{$location['city']}, {$location['region']}, {$location['country']}"
    : 'Location unavailable';

  $new_log_entry = array(
    'keyword' => $keyword,
    'type' => $type,
    'action_from' => $action_from,
    'timestamp' => current_time('mysql'),
    'ip_address' => $user_ip,
    'location' => $location_string,
  );

  if ($existing_log) {
    // If the search term exists, update the count
    $post_id = $existing_log[0]->ID;

    $current_content = json_decode($existing_log[0]->post_content, true);
    // Ensure $current_content is a valid array
    if (!is_array($current_content)) {
      $current_content = [];
    }
    // Add new log entry and concatenate the data
    $current_content[] = $new_log_entry;

    // Update post content and meta
    wp_update_post(array(
      'ID'           => $post_id,
      'post_content' => json_encode($current_content),
    ));

    $current_count = get_post_meta($post_id, 'hits', true);
    $new_count = $current_count ? $current_count + 1 : 1;

    // Update the search count
    update_post_meta($post_id, 'hits', $new_count);
  } else {
    // If the search term doesn't exist, create a new post
    $search_log_data = array(
      'post_title'   => $keyword,
      'post_content' => json_encode($new_log_entry),
      'post_status'  => 'publish',
      'post_type'    => 'search_logs'
    );

    // Insert the post into 'search_logs'
    $post_id = wp_insert_post($search_log_data);

    // Add search count meta
    add_post_meta($post_id, 'hits', 1);
    // Add search keyword meta
    add_post_meta($post_id, 'search_keyword', $keyword);
  }

  // Query search logs to count the total number of searches for all keywords
  $logs = get_posts(array(
    'post_type' => 'search_logs',
    'posts_per_page' => -1  // Retrieve all search logs
  ));

  if ($logs) {
    $total_search_count = 0;
    foreach ($logs as $log) {
      $count = get_post_meta($log->ID, 'hits', true);
      $total_search_count += $count ? $count : 0;  // Sum the search counts for all keywords
    }
    echo 'Total searches across all terms: ' . $total_search_count . ' times.';
  } else {
    echo 'No logs found for any search term.';
  }



  // people to exclude
  $args = array(
    'post_type' => 'people',
    'posts_per_page' => '-1',
    'meta_query' => array(
      array(
        'key' => 'exclude_from_main_search',
        'value' => '1',
        'compare' => '=',
      ),
    )
  );

  $my_query = new WP_Query($args);
  $exclude_people_ids = wp_list_pluck($my_query->posts, 'ID');

  $people_args = array(
    'post_type'         => 'people',
    'posts_per_page'    => -1,
    'post_status'       => 'publish',
    'meta_key'          => 'last_name',
    'orderby' => 'meta_value',
    'order'   => 'ASC',
    // 'lang'              => '',

    // 'post__not_in' => $exclude_people_ids,
  );
  if ($keyword) {

    $people_args['s'] = $keyword;


    if (strtolower($keyword) == 'upc') {
      $px1 = get_post_meta(pll_get_post(660), 'the_team_select_partners', true);
      $px2 = get_post_meta(pll_get_post(660), 'the_team_senior_associates', true);
      $px3 = get_post_meta(pll_get_post(660), 'the_team_attorneys', true);

      $people_args = array(
        'post_type'         => 'people',
        'posts_per_page'    => -1,
        'post_status'       => 'publish',
        'meta_key'          => 'last_name',
        'orderby' => 'meta_value',
        'order'   => 'ASC',
        'post__in' => array_merge($px1, $px2, $px3),
      );
    }
  }



  $get_peoples = array();
  if ($type == 'people' || $type == 'all') {
    // $get_peoples = get_posts($people_args);
    $get_peoples = new WP_Query($people_args);
    $get_peoples = $get_peoples->posts;
  }

  if ($get_peoples) {
    $no_results = false;
  }

  $bulletin_args = array(
    'post_type'         => 'bulletin',
    'posts_per_page'    => -1,
    'post_status'       => 'publish',
  );
  if ($keyword) {
    $bulletin_args['s'] = $keyword;
  }

  $get_bulletins = array();
  if ($type == 'articles' || $type == 'all') {
    // $get_bulletins = get_posts($bulletin_args);
    $get_bulletins = new WP_Query($bulletin_args);
    $get_bulletins = $get_bulletins->posts;
  }

  if ($get_bulletins) {
    $no_results = false;
  }


  $news_args = array(
    'post_type'         => array('news', 'upc_news'),
    'posts_per_page'    => -1,
    'post_status'       => 'publish',
    'orderby' => 'title',
    'order'   => 'ASC',
  );
  if ($keyword) {
    $news_args['s'] = $keyword;
  }

  $get_news = array();
  if ($type == 'articles' || $type == 'all') {
    global $wpdb;
    $sector_news = new WP_Query(array(
      'post_type'         => 'sector',
      'posts_per_page'    => -1,
      'post_status'       => 'publish',
      's'                 => $keyword,
      'orderby' => 'title',
      'order'   => 'ASC',
    ));


    $bulletin_ids = [];
    if ($sector_news) {
      foreach ($sector_news->posts as $sector_new) {
        $sector_ids = $sector_new->ID;
        // $sqll = "SELECT * FROM {$wpdb->prefix}postmeta WHERE meta_key = 'sector_sub_sector' AND meta_value LIKE '%\"{$sector_new->ID}\"%';";
        // $sector_ids = $wpdb->get_results($sqll);
        foreach ($sector_ids as $sector_id) $bulletin_ids[] = $sector_id->post_id;
      }
    }


    if ($bulletin_ids) {
      //print_r($bulletin_ids);
      $news_args['s'] = '';
      $news_args['post__in'] = array_unique($bulletin_ids);
    }

    // $get_news = get_posts($news_args);
    $get_news = new WP_Query($news_args);
    $get_news = $get_news->posts;
  }

  if ($get_news) {
    $no_results = false;
  }


  $sector_args = array(
    'post_type'         => 'sector',
    'posts_per_page'    => -1,
    'post_status'       => 'publish',
    // 'lang'              => '',
    'orderby' => 'title',
    'order'   => 'ASC',
  );
  if ($keyword) {
    $sector_args['s'] = $keyword;
  }

  $get_sectors = array();
  if ($type == 'expertise' || $type == 'all') {
    // $get_sectors = get_posts($sector_args);
    $get_sectors = new WP_Query($sector_args);
    $get_sectors = $get_sectors->posts;
  }
  if ($get_sectors) {
    $no_results = false;
  }

  $parent_sectors = array();
  if ($type == 'people') {
    $args = array(
      'post_type'         => 'sector',
      'posts_per_page'    => -1,
      'post_status'       => 'publish',
      // 'lang'              => '',
      'post_parent' => 0,
      'orderby' => 'title',
      'order'   => 'ASC',
    );
    if ($keyword) {
      $args['s'] = $keyword;
    }

    $sectors = new WP_Query($args);
    $sectors = $sectors->posts;

    $parent_sectors = wp_list_pluck($sectors, 'ID');
  }

  $black_list_html = '';
  $sector_people_html = '';
  if (($get_sectors && !$get_peoples) || $parent_sectors) {
    if (!$parent_sectors) {
      $hello_ids = wp_list_pluck($get_sectors, 'ID');
      $hello_post_parent = wp_list_pluck($get_sectors, 'post_parent');
      $hello = array_combine($hello_ids, $hello_post_parent);
      foreach ($hello as $k => $v) {
        if (!$v) {
          $parent_sectors[] = $k;
        }
      }
    }

    if (strpos('medtech', strtolower($keyword)) !== false) {
      $parent_sectors[] = pll_get_post(9);
    }




    if ($parent_sectors) {
      $no_results = false;
      // run query for people
      /*$people_args = array(
            'post_type'         => 'people',
            'posts_per_page'    => -1,
            'post_status'       => 'publish',
            // 'lang'              => '',
        );
        
        $meta_q = array();
        foreach($parent_sectors as $sector){
          $meta_q[] = array(
            'key'     => 'sectors',
            'value'   => sprintf('"%s"', $sector),
            'compare' => 'LIKE',
          );
        }
        if($meta_q && count($meta_q) > 1){
          $meta_q['relation'] = 'OR';
        }
        $people_args['meta_query'][] = $meta_q;

        $get_peoples = array();
        $get_peoples = new WP_Query($people_args);
        $get_peoples = $get_peoples->posts;*/

      $non_black_list_people = array();


      $sector_parentsx = [];


      $sector_people_html .= '
          <div class="col-md-4 people-list">
                <h5 class="text-white">' . __('People', 'picostrap5-child-base') . '</h5>
                <ul class="search-result-list" style="list-style:none;padding:0; max-height: 250px; overflow: auto;">
        ';



      foreach ($get_sectors as $sc) {
        if ($sc->post_parent == 0) $sector_parentsx[$sc->ID] = strtolower(strip_tags(str_replace('&nbsp;', '', get_the_title($sc))));
      }





      foreach ($parent_sectors as $sector) {

        $args = array(
          'post_type'         => 'people',
          'posts_per_page'    => -1,
          'post_status'       => 'publish',
          // 'lang'              => '',
          'meta_key'          => 'last_name',
          'orderby' => 'meta_value',
          'order'   => 'ASC',
          'meta_query' => array(
            array(
              'key'     => 'sectors',
              'value'   => sprintf('"%s"', $sector),
              'compare' => 'LIKE',
            ),
          ),
          'tax_query' => array(
            'relation' => 'OR',
            array(
              'taxonomy' => 'group_listing',
              'field' => 'slug',
              'terms' => 'partners',
            ),
            array(
              'taxonomy' => 'people_role',
              'field' => 'name',
              'terms' => $keyword,
              'operator' => 'LIKE',
            )
          )
        );

        if (get_the_title($sector) != 'Trade marks' || count($parent_sectors) != 1)
          $args['post__not_in'] = $exclude_people_ids;

        $people = new WP_Query($args);
        $people = $people->posts;

        if (count($people) > 0) {

          foreach ($people as $p) {
            $non_black_list_people[] = $p->ID;
            $sector_people_html .= '<li><a href="' . get_the_permalink($p) . '">' . get_the_title($p) . '</a></li>';
          }
        }

        //if(get_the_title($sector) == 'Artificial intelligence and machine learning') 
        break;
      }
      $sector_people_html .= '</ul></div>';
    }
  }

  $keyword_in = strtolower($keyword);


  $get_services = array();
  $services_in = array('patent', 'patents', 'trade mark',  'trade marks', 'design', 'designs', 'global plant breeder', 'global plant breeders');
  if (in_array($keyword_in, $services_in)) {
    $no_results = false;

    $args = array(
      'post_type'         => 'service',
      'posts_per_page'    => -1,
      'post_status'       => 'publish',
      // 'lang'              => '',
      'orderby' => 'title',
      'order'   => 'ASC',
    );
    if ($keyword) {
      $args['s'] = $keyword;
    }

    $get_services = new WP_Query($args);
    $get_services = $get_services->posts;
  }


  $get_pages = array();
  $pages_in = array(
    'upc' => pll_get_post(660),
    'london' => pll_get_post(1121),
    'cambridge' => pll_get_post(1121),
    'reading' => pll_get_post(1121),
    'germany' => pll_get_post(1121),
    'frankfurt' => pll_get_post(1121),
    'munich' => pll_get_post(1121),
    'spain' => pll_get_post(1121),
    'madrid' => pll_get_post(1121),
    'jobs' => pll_get_post(632),
    'roles' => pll_get_post(632),
    'working' => pll_get_post(632),
    'client log' => pll_get_post(632),
    'equality and diversity' => array(pll_get_post(1570), pll_get_post(8503), pll_get_post(1230), pll_get_post(1575), pll_get_post(1580)),
    'diversity' => array(pll_get_post(1570), pll_get_post(8503), pll_get_post(1230), pll_get_post(1575), pll_get_post(1580)),
    'inclusion' => array(pll_get_post(1570), pll_get_post(8503), pll_get_post(1230), pll_get_post(1575), pll_get_post(1580)),
    'values' => array(pll_get_post(1570), pll_get_post(8503), pll_get_post(1230), pll_get_post(1575), pll_get_post(1580)),
    'culture' => array(pll_get_post(1570), pll_get_post(8503), pll_get_post(1230), pll_get_post(1575), pll_get_post(1580)),
    'responsible business' => array(pll_get_post(1570), pll_get_post(8503), pll_get_post(1230), pll_get_post(1575), pll_get_post(1580)),
    'wellbeing' => array(pll_get_post(1570), pll_get_post(8503), pll_get_post(1230), pll_get_post(1575), pll_get_post(1580)),
    'well being' => array(pll_get_post(1570), pll_get_post(8503), pll_get_post(1230), pll_get_post(1575), pll_get_post(1580)),
    'edi' => array(pll_get_post(1570), pll_get_post(8503), pll_get_post(1230), pll_get_post(1575), pll_get_post(1580)),
    'equality' => array(pll_get_post(1570), pll_get_post(8503), pll_get_post(1230), pll_get_post(1575), pll_get_post(1580)),
    'charity' => array(pll_get_post(1570), pll_get_post(8503), pll_get_post(1230), pll_get_post(1575), pll_get_post(1580)),
    'patent attorney' => pll_get_post(416),
    'trade mark attorney' => pll_get_post(416),
    'design attorney' => pll_get_post(416),
    'trainee' => pll_get_post(639),
    'vacation scheme' => pll_get_post(639),
    'open day' => pll_get_post(639),
    'news' => pll_get_post(639),
  );
  if ((int)$pages_in[$keyword_in] != 0) {
    $no_results = false;
    $args = array(
      'post_type'         => 'page',
      'posts_per_page'    => -1,
      'post_status'       => 'publish',
      'post__in'          => (array)$pages_in[$keyword_in],
      'orderby' => 'title',
      'order'   => 'ASC',
    );

    $get_pages = new WP_Query($args);
    $get_pages = $get_pages->posts;
  }


  $go_flag = false;

  $sql = "SELECT * FROM {$wpdb->prefix}search_keywords WHERE search_term LIKE '{$keyword_in}' AND search_post_id != '0' ORDER BY search_post_type ASC";
  $search_keywords = $wpdb->get_results($sql);
  $search_pts = [];
  foreach ($search_keywords as $search_keyword) {
    $pll_id = pll_get_post($search_keyword->search_post_id);
    if (!in_array($pll_id, (array)$search_pts[$search_keyword->search_post_type])) $search_pts[$search_keyword->search_post_type][] = $pll_id;
  }
  foreach ($search_pts as $key => $search_pt) {
    $no_results = false;
    $args = array(
      'post_type'         => $key,
      'posts_per_page'    => -1,
      'post_status'       => 'publish',
      'post__in'          => (array)$search_pt,
      'orderby' => 'title',
      'order'   => 'ASC',
    );

    $get_pt = new WP_Query($args);

    if ($get_pt) $go_flag = true;

    ${'get_' . $key . 's'} = array_merge((array)${'get_' . $key . 's'}, (array)$get_pt->posts);
  }

  $args = array(
    'post_type'         => 'people',
    'lang'           =>  pll_current_language('slug'),
    'posts_per_page'    => -1,
    'post_status'       => 'publish',
    'meta_key'          => 'last_name',
    'orderby' => 'meta_value',
    'order'   => 'ASC',
    'tax_query' => array(
      array(
        'taxonomy' => 'people_role',
        'field' => 'name',
        'terms' => $keyword,
        //'operator' => 'LIKE',
      )
    )
  );
  $get_ppl = new WP_Query($args);
  if ($get_ppl)
    $get_peoples = array_merge($get_peoples, (array)$get_ppl->posts);

  // echo "<pre>";
  // print_r( $get_sectors );

?>

  <div class="row">
    <?php echo $sector_people_html; ?>
    <?php if (count($get_peoples) > 0) : ?>
      <div class="col-md-4 people-list">
        <h5 class="text-white"><?php echo __('People', 'picostrap5-child-base'); ?></h5>
        <ul class="search-result-list" style='list-style:none;padding:0; max-height: 250px; overflow: auto;'>
          <?php foreach ($get_peoples as $people) : ?>
            <li><a href="<?php echo get_the_permalink($people); ?>"><?php echo get_the_title($people); ?></a></li>
          <?php endforeach ?>
        </ul>
      </div>
    <?php endif ?>

    <?php if (count($get_services) > 0) : ?>
      <div class="col-md-4 people-list">
        <h5 class="text-white"><?php echo __('Services', 'picostrap5-child-base'); ?></h5>
        <ul class="search-result-list" style='list-style:none;padding:0; max-height: 250px; overflow: auto;'>
          <?php foreach ($get_services as $service) : ?>
            <li><a href="<?php echo get_the_permalink($service); ?>"><?php echo get_the_title($service); ?></a></li>
          <?php endforeach ?>
        </ul>
      </div>
    <?php endif ?>

    <?php if (count($get_pages) > 0) : ?>
      <div class="col-md-4 people-list">
        <h5 class="text-white"><?php echo __('Pages', 'picostrap5-child-base'); ?></h5>
        <ul class="search-result-list" style='list-style:none;padding:0; max-height: 250px; overflow: auto;'>
          <?php foreach ($get_pages as $page) : ?>
            <li><a href="<?php echo get_the_permalink($page); ?>"><?php echo get_the_title($page); ?></a></li>
          <?php endforeach ?>
        </ul>
      </div>
    <?php endif ?>

    <?php if (count($get_sectors) > 0) : ?>
      <div class="col-md-4 people-list">
        <h5 class="text-white" data-sector="<?php echo get_the_title($parent_sectors[0]) ?>"><?php echo __('Sectors', 'picostrap5-child-base'); ?></h5>
        <ul class="search-result-list" style='list-style:none;padding:0; max-height: 250px; overflow: auto;'>
          <?php


          $done_id = [];
          foreach ($get_sectors as $key => $sector) :

            if (!in_array($sector->ID, $done_id)) {

              $done_id[] = $sector->ID;


              // if((strstr(strtolower(get_the_title($get_sectors[0])), 'artificial') && $sector->ID != pll_get_post(526))) continue;


              $sector_title = strip_tags(str_replace('&nbsp;', '', get_the_title($sector)));

              // if(!in_array((string)strtolower($sector_title), (array)$sector_parentsx) || $go_flag || $sector_parentsx[$sector->ID] != '')

              //if($sector_title != ''){
              if (true) { ?>
                <li class="<?php echo $sector->post_parent != 0 ? 'sub-sector' : '' ?>"><a href="<?php echo get_the_permalink($sector); ?>" data-id="<?php echo $sector->ID ?>"><?php echo $sector_title; ?></a></li>
          <?php }
            }
          endforeach ?>
        </ul>
      </div>
    <?php endif ?>
    <?php /*if( count( $get_bulletins ) > 0 ) : ?>
              <div class="col-md-4 people-list">
                <h5 class="text-white"><?php echo __('Bulletins', 'picostrap5-child-base'); ?></h5>
                  <ul class="search-result-list" style='list-style:none;padding:0; max-height: 250px; overflow: auto;'>
                    <?php foreach( $get_bulletins as $bulletin ) : ?>
                        <li><a href="<?php echo get_the_permalink($bulletin); ?>"><?php echo get_the_title($bulletin); ?></a></li>
                    <?php endforeach ?>
                  </ul>
              </div>
            <?php endif */ ?>
    <?php if ($action_from != 'inpage' && (count($get_news) > 0 || count($get_bulletins) > 0)) : ?>
      <div class="col-md-4 people-list">
        <h5 class="text-white"><?php echo __('Insights', 'picostrap5-child-base'); ?></h5>
        <ul class="search-result-list" style='list-style:none;padding:0; max-height: 250px; overflow: auto;'>
          <?php if (count($get_news) > 0) : ?>
            <?php foreach ($get_news as $news) : ?>
              <li><a href="<?php echo get_the_permalink($news); ?>"><?php echo get_the_title($news); ?></a></li>
            <?php endforeach;
            wp_reset_postdata(); ?>
          <?php endif ?>
          <?php if (count($get_bulletins) > 0) : ?>
            <?php foreach ($get_bulletins as $bulletin) : ?>
              <li><a href="<?php echo get_the_permalink($bulletin); ?>"><?php echo get_the_title($bulletin); ?></a></li>
            <?php endforeach;
            wp_reset_postdata(); ?>
          <?php endif ?>
        </ul>
      </div>
    <?php endif ?>
    <?php // echo $black_list_html; 
    ?>
    <?php
    if ($no_results) {
    ?>
      <div class="col-md-12">
        <div class="no-search-esult">
          <?php echo __('Not found', 'picostrap5-child-base'); ?>
        </div>
      </div>
    <?php
    }
    ?>
  </div>
  <style type="text/css">
    .mobile-search .people-list ul li a {
      color: #fff;
      text-decoration: none;
      font-size: 14px;
    }

    #filtered-data {
      /*            max-height: 55vh;*/
      /*            overflow-y: auto;*/
      /*            overflow-x: hidden;*/
    }
  </style>
  <?php
  die();
}

add_action('wp_ajax_search_page_filter_search_action', 'search_page_filter_search_action_ajax_handler'); // wp_ajax_{action}
add_action('wp_ajax_nopriv_search_page_filter_search_action', 'search_page_filter_search_action_ajax_handler'); // wp_ajax_nopriv_{action}
function search_page_filter_search_action_ajax_handler()
{

  $keyword = isset($_POST['keyword']) ? sanitize_text_field($_POST['keyword']) : '';
  $type = isset($_POST['type']) ? sanitize_text_field($_POST['type']) : '';

  $sk = array();
  $search_keywords = get_field('search_keywords', 'option');
  if (!empty($search_keywords)) {
    foreach ($search_keywords as $key => $value) {
      if (!empty(trim($value['search_term'])) && !empty(trim($value['search_for_terms']))) {
        $sk[strtolower(trim($value['search_term']))] = str_replace(array(' ', ','), array('+', '|'), strtolower(trim($value['search_for_terms'])));
      }
    }
  }

  if (!empty($sk) && isset($sk[strtolower(trim($keyword))])) {
    $keyword = $sk[strtolower(trim($keyword))];
  }

  $no_results = true;


  if (!function_exists('_config_wpdocs_search_by_title_only_search')) {
    function _config_wpdocs_search_by_title_only_search($search, $wp_query)
    {
      global $wpdb;

      if (empty($search)) {
        return $search; // skip processing - no search term in query
      }

      $q = $wp_query->query_vars;
      $n = ! empty($q['exact']) ? '' : '%';
      $search = '';
      $searchand = '';

      $q['search_terms'] = implode(' ', (array)$q['search_terms']);
      foreach ((array) $q['search_terms'] as $term) {
        $term = esc_sql($wpdb->esc_like($term));
        // $search .= "{$searchand}($wpdb->posts.post_title LIKE '{$n}{$term}{$n}')";
        $search .= "{$searchand}($wpdb->posts.post_title REGEXP '{$term}')";
        $searchand = ' AND ';
      }

      if (! empty($search)) {
        $search = " AND ({$search}) ";
        if (! is_user_logged_in())
          $search .= " AND ($wpdb->posts.post_password = '') ";
      }
      // echo $search;
      return $search;
    }
  }
  add_filter('posts_search', '_config_wpdocs_search_by_title_only_search', 500, 2);

  // people to exclude
  $args = array(
    'post_type' => 'people',
    'posts_per_page' => '-1',
    'meta_query' => array(
      array(
        'key' => 'exclude_from_main_search',
        'value' => '1',
        'compare' => '=',
      ),
    )
  );

  $my_query = new WP_Query($args);
  $exclude_people_ids = wp_list_pluck($my_query->posts, 'ID');

  $people_args = array(
    'post_type'         => 'people',
    'posts_per_page'    => -1,
    'post_status'       => 'publish',
    // 'lang'              => '',
    'orderby' => 'title',
    'order'   => 'ASC',

    // 'post__not_in' => $exclude_people_ids,
  );
  if ($keyword) {
    $people_args['s'] = $keyword;
  }

  $get_peoples = array();
  if ($type == 'people' || $type == 'all') {
    // $get_peoples = get_posts($people_args);
    $get_peoples = new WP_Query($people_args);
    $get_peoples = $get_peoples->posts;
  }

  if ($get_peoples) {
    $no_results = false;
  }

  $bulletin_args = array(
    'post_type'         => 'bulletin',
    'posts_per_page'    => -1,
    'post_status'       => 'publish',
    'orderby' => 'title',
    'order'   => 'ASC',
  );
  if ($keyword) {
    $bulletin_args['s'] = $keyword;
  }

  $get_bulletins = array();
  if ($type == 'articles' || $type == 'all') {
    // $get_bulletins = get_posts($bulletin_args);
    $get_bulletins = new WP_Query($bulletin_args);
    $get_bulletins = $get_bulletins->posts;
  }

  if ($get_bulletins) {
    $no_results = false;
  }


  $news_args = array(
    'post_type'         => array('news', 'upc_news'),
    'posts_per_page'    => -1,
    'post_status'       => 'publish',
    'orderby' => 'title',
    'order'   => 'ASC',
  );
  if ($keyword) {
    $news_args['s'] = $keyword;
  }

  $get_news = array();
  if ($type == 'articles' || $type == 'all') {
    // $get_news = get_posts($news_args);
    $get_news = new WP_Query($news_args);
    $get_news = $get_news->posts;
  }

  if ($get_news) {
    $no_results = false;
  }


  $sector_args = array(
    'post_type'         => 'sector',
    'posts_per_page'    => -1,
    'post_status'       => 'publish',
    // 'lang'              => '',
    'orderby' => 'title',
    'order'   => 'ASC',
  );
  if ($keyword) {
    $sector_args['s'] = $keyword;
  }

  $get_sectors = array();
  if ($type == 'expertise' || $type == 'all') {
    // $get_sectors = get_posts($sector_args);
    $get_sectors = new WP_Query($sector_args);
    $get_sectors = $get_sectors->posts;
  }
  if ($get_sectors) {
    $no_results = false;
  }

  $parent_sectors = array();
  if ($type == 'people') {
    $args = array(
      'post_type'         => 'sector',
      'posts_per_page'    => -1,
      'post_status'       => 'publish',
      // 'lang'              => '',
      'post_parent' => 0,
      'orderby' => 'title',
      'order'   => 'ASC',
    );
    if ($keyword) {
      $args['s'] = $keyword;
    }

    $sectors = new WP_Query($args);
    $sectors = $sectors->posts;

    $parent_sectors = wp_list_pluck($sectors, 'ID');
  }

  $sector_people_html = '';
  $black_list_html = '';
  if (($get_sectors && !$get_peoples) || $parent_sectors) {
    if (!$parent_sectors) {
      $hello_ids = wp_list_pluck($get_sectors, 'ID');
      $hello_post_parent = wp_list_pluck($get_sectors, 'post_parent');
      $hello = array_combine($hello_ids, $hello_post_parent);
      foreach ($hello as $k => $v) {
        if (!$v) {
          $parent_sectors[] = $k;
        }
      }
    }

    if (strpos('medtech', strtolower($keyword)) !== false) {
      $parent_sectors[] = pll_get_post(9);
    }

    if ($parent_sectors) {
      $no_results = false;
      // run query for people
      /*$people_args = array(
            'post_type'         => 'people',
            'posts_per_page'    => -1,
            'post_status'       => 'publish',
            // 'lang'              => '',
        );
        
        $meta_q = array();
        foreach($parent_sectors as $sector){
          $meta_q[] = array(
            'key'     => 'sectors',
            'value'   => sprintf('"%s"', $sector),
            'compare' => 'LIKE',
          );
        }
        if($meta_q && count($meta_q) > 1){
          $meta_q['relation'] = 'OR';
        }
        $people_args['meta_query'][] = $meta_q;

        $get_peoples = array();
        $get_peoples = new WP_Query($people_args);
        $get_peoples = $get_peoples->posts;*/

      $non_black_list_people = array();

      $sector_people_html .= '
          <div class="section-title-two">
                <h4>' . __('People', 'picostrap5-child-base') . '</h4>
            </div>
        ';
      foreach ($parent_sectors as $sector) {



        $args = array(
          'post_type'         => 'people',
          'posts_per_page'    => -1,
          'post_status'       => 'publish',
          // 'lang'              => '',
          'orderby' => 'title',
          'order'   => 'ASC',
          'post__not_in' => $exclude_people_ids,
          'meta_query' => array(
            array(
              'key'     => 'sectors',
              'value'   => sprintf('"%s"', $sector),
              'compare' => 'LIKE',
            ),
          ),
          'tax_query' => array(
            array(
              'taxonomy' => 'group_listing',
              'field' => 'slug',
              'terms' => 'partners',
            )
          )
        );

        $people = new WP_Query($args);
        $people = $people->posts;
        if (count($people) > 0) {
          // $sector_people_html .= '<li><strong>'. __('People in') . ' ' . get_the_title($sector) .'</strong></li>';
          /*
            $sector_people_html .= '
              <div class="section-title-two">
                  <h4><small>'. __('People in') . ' ' . get_the_title($sector) .'</small></h4>
              </div>';
            */

          $sector_people_html .= '<div class="row row-cols-xl-4 row-cols-lg-4 row-cols-md-3 row-cols-sm-2 row-cols-2 g-4 mb-20">
            ';

          foreach ($people as $p) {
            $non_black_list_people[] = $p->ID;
            $sector_people_html .= '';

            ob_start();
            $pid = $p->ID;
            $roles = get_the_terms($pid, 'people_role');
            $roles = join(', ', wp_list_pluck($roles, 'name'));

            $locations = get_the_terms($pid, 'location');
            $locations = join(', ', wp_list_pluck($locations, 'name'));

            $reverse_person_photo_places = get_field('reverse_person_photo_places', $pid);
            $image_url = get_field('person_photo_2', $pid) ? get_field('person_photo_2', $pid)['sizes']['medium_large'] : get_stylesheet_directory_uri() . '/assets/images/team/team.jpg';
            if ($reverse_person_photo_places) {
              $image_url = get_field('person_photo_1', $pid) ? get_field('person_photo_1', $pid)['sizes']['medium_large'] : get_stylesheet_directory_uri() . '/assets/images/team/team.jpg';
            }
  ?>
            <div class="col">
              <div class="team-item style-two style-two">
                <div class="team-image">
                  <a href="<?php echo get_the_permalink($p); ?>">
                    <img class="<?php echo strpos($image_url, 'team/team.jpg') === false ? '' : 'no-person-image'; ?>" src="<?php echo $image_url; ?>" alt="<?php echo get_the_title(); ?>">
                    <?php
                    if (strpos($image_url, 'team/team.jpg') === false) {
                    ?>
                    <?php
                    } else {
                      $classes = array('bg-primary-red', 'bg-primary-green', 'bg-primary-yellow', 'bg-primary-blue');
                      shuffle($classes);
                      $c = array_rand($classes, 1);
                    ?>
                      <div class="no-person-image-box <?php echo $classes[$c]; ?>"></div>
                    <?php
                    }
                    ?>
                  </a>
                </div>
                <div class="team-content">
                  <h6><a href="<?php echo get_the_permalink(); ?>"><?php echo get_the_title(); ?></a></h6>
                  <span><?php echo $roles; ?> </span>
                  <span><?php echo $locations; ?></span>
                </div>
              </div>
            </div>
  <?php
            $sector_people_html .= ob_get_clean();
          }
          $sector_people_html .= '</div>';
        }
      }
      $sector_people_html .= '</div>';

      /*$args = array(
            'post_type'         => 'people',
            'posts_per_page'    => -1,
            'post_status'       => 'publish',
            // 'lang'              => '',
            'post__not_in' => $non_black_list_people,          
        );

        $people = new WP_Query($args);
        $people = $people->posts;

        $black_list_html .= '
          <div class="col-md-4 people-list">
                <h5 class="text-white">'. __('People Black List', 'picostrap5-child-base') .'</h5>
                <ul class="search-result-list" style="list-style:none;padding:0; max-height: 250px; overflow: auto;">
        ';

        foreach($people as $p){
          $black_list_html .= '<li><a href="'. get_the_permalink($p) .'">'. get_the_title($p) .'</a></li>';
        }

        $black_list_html .= '</ul></div>';*/
    }
  }


  ?>

  <div class="row">
    <?php echo $sector_people_html; ?>
    <?php if (count($get_peoples) > 0) : ?>

      <div class="section-title-two">
        <h4><?php echo __('People', 'picostrap5-child-base'); ?></h4>
      </div>
      <div class="row row-cols-xl-6 row-cols-lg-5 row-cols-md-4 row-cols-sm-2 row-cols-2 g-4 mb-20">
        <?php foreach ($get_peoples as $people) : ?>
          <?php
          $pid = $people->ID;
          $roles = get_the_terms($pid, 'people_role');
          $roles = join(', ', wp_list_pluck($roles, 'name'));

          $locations = get_the_terms($pid, 'location');
          $locations = join(', ', wp_list_pluck($locations, 'name'));

          $reverse_person_photo_places = get_field('reverse_person_photo_places', $pid);
          $image_url = get_field('person_photo_2', $pid) ? get_field('person_photo_2', $pid)['sizes']['medium_large'] : get_stylesheet_directory_uri() . '/assets/images/team/team.jpg';
          if ($reverse_person_photo_places) {
            $image_url = get_field('person_photo_1', $pid) ? get_field('person_photo_1', $pid)['sizes']['medium_large'] : get_stylesheet_directory_uri() . '/assets/images/team/team.jpg';
          }
          ?>
          <div class="col">
            <div class="team-item style-two style-two">
              <div class="team-image">
                <a href="<?php echo get_the_permalink($people); ?>">
                  <img class="<?php echo strpos($image_url, 'team/team.jpg') === false ? '' : 'no-person-image'; ?>" src="<?php echo $image_url; ?>" alt="<?php echo get_the_title(); ?>">
                  <?php
                  if (strpos($image_url, 'team/team.jpg') === false) {
                  ?>
                  <?php
                  } else {
                    $classes = array('bg-primary-red', 'bg-primary-green', 'bg-primary-yellow', 'bg-primary-blue');
                    shuffle($classes);
                    $c = array_rand($classes, 1);
                  ?>
                    <div class="no-person-image-box <?php echo $classes[$c]; ?>"></div>
                  <?php
                  }
                  ?>
                </a>
              </div>
              <div class="team-content">
                <h6><a href="<?php echo get_the_permalink(); ?>"><?php echo get_the_title(); ?></a></h6>
                <span><?php echo $roles; ?> </span>
                <span><?php echo $locations; ?></span>
              </div>
            </div>
          </div>
        <?php endforeach ?>
      </div>
    <?php endif ?>
    <?php if (count($get_sectors) > 0) : ?>
      <div class="section-title-one style-green">
        <h2><?php echo __('Sectors', 'picostrap5-child-base'); ?></h2>
      </div>
      <div class="row justify-content-lg-start justify-content-center g-4 mb-50">
        <?php foreach ($get_sectors as $sector) : ?>
          <div class="col-lg-3 col-md-6 col-sm-10">
            <a href="<?php echo get_the_permalink($sector); ?>" id="<?php echo get_post_field('post_name', $sector->ID); ?>">
              <div class="sector-item">
                <h6 class="title"><?php echo get_the_title($sector); ?></h6>
                <p><?php echo get_the_excerpt($sector); ?></p>
                <div class="arrow-btn">
                  <img src="<?php echo get_stylesheet_directory_uri(); ?>/assets/images/icons/arrow-white.svg" alt="">
                </div>
              </div>
            </a>
          </div>
        <?php endforeach ?>
      </div>
    <?php endif ?>
    <?php if (count($get_bulletins) > 0) : ?>
      <div class="section-title-one style-blue">
        <h2><?php echo __('Bulletins', 'picostrap5-child-base'); ?></h2>
      </div>
      <div class="row justify-content-center mb-40 g-lg-4 g-3">
        <?php foreach ($get_bulletins as $bulletin) : ?>
          <div class="col-lg-3 col-md-6 col-6">
            <div class="insight-item style-blue">
              <div class="image">
                <img src="<?php echo has_post_thumbnail($bulletin) ? get_the_post_thumbnail_url($bulletin, 'full') : home_url('/wp-content/uploads/2023/06/iStock-97970805-scaled.jpg'); ?>" alt="image">
              </div>
              <div class="content">
                <h6><a href="<?php echo get_permalink($bulletin); ?>"><?php echo $bulletin->post_title; ?></a></h6>
                <p><?php echo get_the_excerpt($bulletin); ?></p>
                <a href="<?php echo get_permalink($bulletin); ?>"><?php echo __('MORE', 'picostrap5-child-base'); ?></a>
              </div>
            </div>
          </div>
        <?php endforeach ?>
      </div>
    <?php endif ?>
    <?php if (count($get_news) > 0) : ?>
      <div class="section-title-one style-red">
        <h2><?php echo __('News', 'picostrap5-child-base'); ?> </h2>
      </div>
      <div class="row justify-content-center mb-40 g-lg-4 g-3">
        <?php foreach ($get_news as $news) : ?>
          <div class="col-lg-3 col-md-6 col-6">
            <div class="insight-item">
              <div class="image">
                <!-- <img src="<?php echo get_stylesheet_directory_uri(); ?>/assets/images/insights/insight-1.jpg" alt="image"> -->
                <img src="<?php echo has_post_thumbnail($news) ? get_the_post_thumbnail_url($news, 'full') : home_url('/wp-content/uploads/2023/06/iStock-97970805-scaled.jpg'); ?>" alt="image">
              </div>
              <div class="content">
                <h6><a href="<?php echo get_the_permalink($news) ?>"><?php echo get_the_title($news); ?></a></h6>
                <p><?php echo get_the_excerpt($news); ?></p>
                <a href="<?php echo get_the_permalink($news) ?>"><?php echo __('MORE', 'picostrap5-child-base'); ?></a>
              </div>
            </div>
          </div>
        <?php endforeach ?>
      </div>
    <?php endif ?>
    <?php // echo $black_list_html; 
    ?>
    <?php
    if ($no_results) {
    ?>
      <div class="col-md-12">
        <div class="no-search-esult">
          <?php echo __('Not found', 'picostrap5-child-base'); ?>
        </div>
      </div>
    <?php
    }
    ?>
  </div>
  <style type="text/css">
    .mobile-search .people-list ul li a {
      color: #fff;
      text-decoration: none;
      font-size: 14px;
    }

    #filtered-data {
      /*            max-height: 55vh;*/
      /*            overflow-y: auto;*/
      /*            overflow-x: hidden;*/
    }
  </style>
<?php
  die();
}

function str_lreplace($search, $replace, $subject)
{
  $pos = strrpos($subject, $search);

  if ($pos !== false) {
    $subject = substr_replace($subject, $replace, $pos, strlen($search));
  }

  return $subject;
}

add_filter('wp_nav_menu', function ($nav_menu, $args) {
  $lang_html = '';
  ob_start();
?>
  <li class="menu-item menu-item-type-post_type menu-item-object-page nav-item language-list-inner-menu">
    <ul class="language-list">
      <?php
      // add_filter( 'acf/validate_post_id', 'skip_acf_polylang_options', 10, 2 );
      // remove_filter( 'acf/validate_post_id', 'skip_acf_polylang_options' );
      $japanese_homepage_link = get_field('japanese_homepage_link', 'option');
      $korean_homepage_link = get_field('korean_homepage_link', 'option');

      $japanese_homepage_link = get_post('1255');
      $korean_homepage_link = get_post('1259');

      global $wp_query;


      $pll_the_languages = pll_the_languages(array('raw' => 1));
      if ($pll_the_languages) {
        foreach ($pll_the_languages as $l) {
          $url = $l['url'];
          $slug = $l['slug'];


          if ($wp_query->post->ID == $japanese_homepage_link->ID || $wp_query->post->ID == $korean_homepage_link->ID) {
            if (strtolower($slug) == 'en') {
              $url = home_url('/');
            } else if (strtolower($slug) == 'es') {
              $url = home_url('/es/');
            } else if (strtolower($slug) == 'de') {
              $url = home_url('/ger/');
            }
          } else if ($l['current_lang']) {
            continue;
          }

      ?>
          <li>
            <a href="<?php echo $url; ?>"><?php echo $slug; ?></a>
          </li>
      <?php
        }
      }
      ?>
      <li>
        <a href="#">CN</a>
      </li>
      <?php
      // if(is_home() || is_front_page() || $wp_query->post->ID == $japanese_homepage_link->ID || $wp_query->post->ID == $korean_homepage_link->ID){
      if (true) {
        if ($japanese_homepage_link && $wp_query->post->ID != $japanese_homepage_link->ID) {
      ?>
          <li>
            <a href="<?php echo get_permalink($japanese_homepage_link); ?>">JP</a>
          </li>
        <?php
        }
        if ($korean_homepage_link && $wp_query->post->ID != $korean_homepage_link->ID) {
        ?>
          <li>
            <a href="<?php echo get_permalink($korean_homepage_link); ?>">KR</a>
          </li>
      <?php
        }
      }
      ?>
    </ul>
    <div class="header-icons d-lg-flex justify-content-end">
      <ul>
        <li>
          <a href="<?php echo get_permalink(get_field('locations_page_link', 'option')); ?>">
            <i class="bi bi-geo-alt"></i>
          </a>
        </li>
        <li><i class="bi bi-search search-btn"></i></li>
      </ul>
    </div>
  </li>
<?php
  $lang_html .= ob_get_clean();

  // $new_nav = str_replace('</ul>', $lang_html . '</ul>', $nav_menu);
  $new_nav = str_lreplace('</ul>', $lang_html . '</ul>', $nav_menu);

  return $new_nav;
}, 999, 2);


// add_action('init', 'config_custom_rewrite_rule', 10, 0);
function config_custom_rewrite_rule()
{
  global $wp_rewrite;
  // echo '<pre>';
  // print_r($wp_rewrite);
  // echo '</pre>';
  // add_rewrite_rule('^nutrition/([^/]*)/([^/]*)/?','index.php?page_id=12&food=$matches[1]&variety=$matches[2]','top');
}

/*add_filter( 'wp_nav_menu_objects', function($sorted_menu_items, $args){
  echo '<pre>';
  print_r($sorted_menu_items);
  echo '</pre>';

  return $sorted_menu_items;
}, 10, 2 );*/


add_action('template_redirect', 'config_boult_redirect_templates');
function config_boult_redirect_templates()
{

  if (is_singular('sector')) {
    global $post;

    if ($post->post_parent) {
      wp_redirect(get_the_permalink($post->post_parent));
      die;
    }
  }
}

function modify_project_post_type_args($args, $post_type)
{
  if ($post_type === 'people') {
    if (pll_current_language() == 'es') {
      $args['rewrite']['slug'] = 'equipo';
    } else {
      $args['rewrite']['slug'] = 'people';
    }
    flush_rewrite_rules(false);
  }
  return $args;
}
add_filter('register_post_type_args', 'modify_project_post_type_args', 10, 2);



add_action('admin_menu', 'boult_register_my_custom_submenu_page', 999999);

function boult_register_my_custom_submenu_page()
{
  add_submenu_page(
    'theme-general-settings',
    'Map Keyword',
    'Map Keyword',
    'manage_options',
    'map-keyword-page',
    'boult_my_custom_submenu_page_callback'
  );
}

function boult_my_custom_submenu_page_callback()
{
  echo '<div class="wrap"><div id="icon-tools" class="icon32"></div>';
  global $wpdb;

  if ($_POST['deleteQ']) {
    $sql = "DELETE FROM {$wpdb->prefix}search_keywords WHERE id = '" . (int)$_POST['deleteQ'] . "' LIMIT 1;";
    $wpdb->query($sql);
  }
  if ($_POST['mapK']) {
    $sql = "INSERT INTO {$wpdb->prefix}search_keywords 
            (search_term,search_post_type,search_post_id,search_post_url) VALUE 
            ('" . $_POST['mapK']['search_term'] . "','" . $_POST['mapK']['search_post_type'] . "','" . $_POST['mapK']['search_post_id'] . "','" . $_POST['mapK']['search_post_url'] . "');";
    if ($_POST['editD']) {
      $sql = "UPDATE {$wpdb->prefix}search_keywords 
            SET search_term = '" . $_POST['mapK']['search_term'] . "',
            search_post_type = '" . $_POST['mapK']['search_post_type'] . "',
            search_post_id = '" . $_POST['mapK']['search_post_id'] . "',
            search_post_url = '" . $_POST['mapK']['search_post_url'] . "' WHERE id = '" . (int)$_POST['editD'] . "' LIMIT 1;";
    }

    if ($sql)
      $wpdb->query($sql);
  }


  $sql = "SELECT * FROM {$wpdb->prefix}search_keywords";
  $postx = $wpdb->get_results($sql);

?>
  <link rel="stylesheet" type="text/css" href="//ajax.aspnetcdn.com/ajax/jquery.dataTables/1.9.4/css/jquery.dataTables.css" />

  <table id="table_id" style="display: none;">
    <thead>
      <tr>
        <th>ID</th>
        <th>Keyword term</th>
        <th>Post TYPE</th>
        <th>Post ID</th>
        <th>Post URL</th>
        <th>Action</th>
      </tr>
    </thead>
    <tbody>
      <?php foreach ($postx as $post) { ?>
        <tr>
          <td><?php echo $post->id ?></td>
          <td><?php echo $post->search_term ?></td>
          <td><?php echo $post->search_post_type ?></td>
          <td><?php echo $post->search_post_id ?></td>
          <td><?php echo $post->search_post_url ?></td>
          <td><a href="/wp-admin/admin.php?page=map-keyword-page&editQ=<?php echo $post->id ?>">Edit<a> | <a href="/wp-admin/admin.php?page=map-keyword-page&deleteQ=<?php echo $post->id ?>" onclick="return confirm('Are you sure you want to delete this item?');">Delete<a></td>
        </tr>
      <?php } ?>
    </tbody>
  </table>
  <?php

  $sql = "SELECT * FROM {$wpdb->prefix}search_keywords WHERE id=" . $_GET['editQ'] . " LIMIT 1";
  $postz = $wpdb->get_results($sql);

  ?>
  <div style="margin-top: 40px;">
    <form action="/wp-admin/admin.php?page=map-keyword-page" method="POST" style="border:1px solid #ccc;max-width: 310px;padding: 20px 40px;">
      <table>
        <tr>
          <td><label>Keyword</label></td>
          <td><input type="text" name="mapK[search_term]" value="<?php echo $postz[0]->search_term ?>" required></td>
        </tr>

        <tr>
          <td><label>Post TYPE</label></td>
          <td><select name="mapK[search_post_type]">
              <?php foreach (get_post_types('', 'names') as $post_type) {
                $sel = '';
                if ($post_type ==  $postz[0]->search_post_type) $sel = 'selected'; ?>
                <option value="<?php echo $post_type ?>" <?php echo $sel ?>><?php echo $post_type ?></option>
              <?php } ?>
            </select></td>
        </tr>

        <tr>
          <td><label>Post ID</label></td>
          <td><input type="number" name="mapK[search_post_id]" value="<?php echo $postz[0]->search_post_id ?>" required></td>
        </tr>

        <tr>
          <td><label>Post URL</label></td>
          <td><input type="text" name="mapK[search_post_url]" value="<?php echo $postz[0]->search_post_url ?>" required></td>
        </tr>
      </table>
      <p class="submit"><input type="submit" name="submit" id="submit" class="button button-primary" value="Save"></p>
      <?php if ((int)$_GET['editQ']) { ?>
        <input type="hidden" name="editD" value="<?php echo (int)$_GET['editQ'] ?>">
      <?php } ?>
    </form>
  </div>
  <script type="text/javascript" src="//code.jquery.com/jquery-3.7.0.js"></script>
  <script type="text/javascript" src="//cdn.datatables.net/1.13.6/js/jquery.dataTables.min.js"></script>
  <script type="text/javascript" src="//cdn.datatables.net/buttons/2.4.2/js/dataTables.buttons.min.js"></script>
  <script type="text/javascript" src="//cdnjs.cloudflare.com/ajax/libs/pdfmake/0.1.53/pdfmake.min.js"></script>
  <script type="text/javascript" src="//cdnjs.cloudflare.com/ajax/libs/pdfmake/0.1.53/vfs_fonts.js"></script>
  <script type="text/javascript" src="//cdn.datatables.net/buttons/2.4.2/js/buttons.html5.min.js"></script>
  <script type="text/javascript" src="//cdn.datatables.net/buttons/2.4.2/js/buttons.print.min.js"></script>
  <script>
    jQuery(document).ready(function() {
      jQuery("#table_id").show();
      jQuery(document).ready(function() {
        jQuery('#table_id').DataTable({
          dom: 'Bfrtip',
          buttons: [
            'copy', 'csv', 'excel', 'pdf', 'print'
          ]
        });
      });
    });
  </script>
<?php
  echo '</div>';
}

add_action('init', function () {


  if ($_GET['add-keywords'] == 'yes') {
    exit;
    global $wpdb;



    $row = 1;
    if (($handle = fopen(STYLESHEETPATH . "/search-keywords/qoutes-es.csv", "r")) !== FALSE) {
      while (($data = fgetcsv($handle, 1000, ",")) !== FALSE) {

        if ($row > 1 && $data[0] != '') {
          $sql = "SELECT * FROM nw1_posts WHERE post_title LIKE '$data[0]'AND post_type = 'people' AND post_status = 'publish' LIMIT 1";
          $peoples = $wpdb->get_results($sql);

          $data_put = explode('"', $data[1]);

          if ($peoples && $data[1] != '') {
            $sql = "SELECT * FROM nw1_term_taxonomy WHERE taxonomy = 'post_translations' AND description LIKE '%:" . $peoples[0]->ID . ";%' ORDER by term_taxonomy_id DESC LIMIT 1";
            $terms = $wpdb->get_results($sql);
            if ($terms) {
              $description = unserialize($terms[0]->description);

              if ((int)$description['es'] && count($data_put) > 1) {
                //update_post_meta((int)$description['es'], '_qualifications', 'field_6426424f35ca0');
                echo '<h3>' . $description['es'] . '</h3>"' . $data_put[1] . '"<br><br><br><br><br>';

                update_post_meta((int)$description['es'], 'partner_quote', '"' . $data_put[1] . '"');
              }
            }
          }
        }

        $row++;
      }
      fclose($handle);
    }



    exit;
  }
}, 999999999999);


add_filter('wp_nav_menu_args', 'config_wp_nav_menu_args', 9999999999);
function config_wp_nav_menu_args($args)
{
  $page_ids = array(8263, 8266, 9538, 8201, 8121, 8203, 9319, 8105);

  if (pll_current_language() != 'de') {
    return $args;
  }

  global $post;
  $page_id = $post->ID;
  if (!in_array($page_id, $page_ids)) {
    return $args;
  }

  if (isset($args['theme_location']) && $args['theme_location'] == 'primary') {
    unset($args['theme_location']);
    $args['menu'] = '2420';

    return $args;
  }
  return $args;
}




return;

function search_array($array, $search_string)
{
  foreach ($array as $key => $value) {
    if (is_array($value)) {
      $result = search_array($value, $search_string);
      if ($result !== false) {
        return $result;
      }
    } else {
      if (stripos($value, $search_string) !== false) {
        return $key;
      }
    }
  }
  return false;
}

add_action('wp_head', function () {
?>
  <style type="text/css">
    .mobile-search {
      background: rgba(0, 0, 0, 0.85);
      box-shadow: 0px 0px 10px rgba(0, 0, 0, 0.09);
      width: 100%;
      height: 100%;
      border-radius: 4px;
      display: flex;
      justify-content: center;
      flex-wrap: wrap;
      align-items: center;
      align-items: center;
      position: fixed;
      cursor: pointer;
      transform: scale(0.7);
      top: 0;
      left: 0;
      z-index: 9999;
      opacity: 0;
      visibility: hidden;
      transition: .65s ease;
      padding: 35px 100px;
    }

    .mobile-search @include md-down-device() {
      padding: 20px 20px !important;
    }

    .mobile-search label {
      color: #fff;
      margin-bottom: 20px;
      font-family: var(--font-nunito);

    }

    .mobile-search.slide {
      // transform: translateY(0);
      transform: scale(1);
      opacity: 1;
      visibility: visible;
    }



    .mobile-search .search-cross-btn {
      color: #fff;
      cursor: pointer;
      background: rgba(var(--white), 0.6);
      border-radius: 50%;
      height: 40px;
      width: 40px;
      line-height: 40px;
      text-align: center;
      line-height: 43px;
      transition: 0.5s ease;
    }

    .mobile-search .search-cross-btn:hover {
      /*// background: var(--white);*/
      transform: scale(1.1);
    }

    .mobile-search .search-cross-btn i {
      font-size: 25px;
    }
  </style>
<?php
}, 9999999999999999999);


function skip_acf_polylang_options($future_post_id, $original_post_id)
{
  return $original_post_id;
}

// passle api start
function get_passle_posts($args = array())
{

  $default = array(
    'aPIKey' => '102erbx-ZNDJR8M-XUP93H7',
    'passleShortcode' => '102erby',

    'pageNumber' => '1',
    'itemsPerPage' => '4',
  );

  $args = shortcode_atts($default, $args);

  $q = http_build_query($args);

  $curl = curl_init();

  curl_setopt_array($curl, array(
    CURLOPT_URL => "https://clientsdk.passle.net/clientapi/v1/posts?" . $q,
    CURLOPT_RETURNTRANSFER => true,
    CURLOPT_ENCODING => "",
    CURLOPT_MAXREDIRS => 10,
    CURLOPT_TIMEOUT => 30,
    CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
    CURLOPT_CUSTOMREQUEST => "GET",
    CURLOPT_HTTPHEADER => array(
      "cache-control: no-cache",
      "postman-token: 6fe28027-3ab9-b7c1-9497-66a87a2dfa10"
    ),
  ));

  $response = curl_exec($curl);
  $err = curl_error($curl);

  curl_close($curl);

  if ($err) {
    return "cURL Error #:" . $err;
  } else {
    return json_decode($response);
  }
}

add_action('wp_footer', function () { ?>
  <script type="text/javascript">
    jQuery(function($) {
      // https://dukb55syzud3u.cloudfront.net/PassleView?v=MQbx9OjZ4Dbwugs2wXXVXwuW7CFpLSZzhUZ45wKw1UY1
    });
  </script>
<?php
}, 9999999999999999999);


add_action('init', function () {
  $posts = get_posts(array('post_type' => 'people', 'meta_key' => 'first_name', 'meta_value' => ''));
  foreach ($posts as $post) {
    $title = $post->post_title;
    $titlex = explode(' ', $title);
    $titlex[(count($titlex) - 1)];
    update_post_meta($post->ID, 'first_name', $titlex[(count($titlex) - 2)]);
    update_post_meta($post->ID, 'last_name', $titlex[(count($titlex) - 1)]);
  }
});


add_action('save_post_people', function ($post_id, $post, $update) {
  if ($post->post_type == 'people') {
    $title = $post->post_title;
    $titlex = explode(' ', $title);
    $titlex[(count($titlex) - 1)];
    update_post_meta($post->ID, 'first_name', $titlex[(count($titlex) - 2)]);
    update_post_meta($post->ID, 'last_name', $titlex[(count($titlex) - 1)]);
  }
}, 10, 3);



function get_only_term_id($data)
{
  // Extracting only the term_id into an array
  return array_map(function ($term) {
    return $term->term_id;
  }, $data);
}



// define the pll_rel_hreflang_attributes callback 
function filter_pll_rel_hreflang_attributes($hreflangs)
{
  // for default plugin returns hreflang tags, returning nothing plugin dont write the tags :)
  return '';
};

// add the filter 
add_filter('pll_rel_hreflang_attributes', 'filter_pll_rel_hreflang_attributes', 10, 1);
