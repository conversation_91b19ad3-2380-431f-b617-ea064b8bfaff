<?php
/*
Template Name: All Insights
*/

// [wpif-inline-filter list_filter_wrapper_class="filter_list" list_filter_item_class="filter_item"]
// [wp-post-list-ui id="1120"]

// Exit if accessed directly.
defined('ABSPATH') || exit;
$pll = pll_current_language();
get_header();

?>
<!-- ========== banner-section start============= -->

<div class="banner-section d-flex flex-column align-items-staer justify-content-center position-relative"
    style="background-image: url('<?php echo get_the_post_thumbnail_url(get_the_ID(), 'full'); ?>')">
    <div class="inner-overlay"></div>
    <!-- style="background-image:linear-gradient(to bottom, rgba(255,255,255,0.05) 0%, rgba(0,0,0,0.15) 50%), radial-gradient(at top center, rgba(255,255,255,0.20) 50%, rgba(0,0,0,0.40) 190%), url('<?php echo get_the_post_thumbnail_url(get_the_ID(), 'full'); ?>')"> -->
    <!-- style="background-image:linear-gradient(to bottom, rgba(255,255,255,0.05) 0%, rgba(0,0,0,0.15) 100%), radial-gradient(at top center, rgba(255,255,255,0.40) 0%, rgba(0,0,0,0.40) 120%), url('<?php echo get_the_post_thumbnail_url(get_the_ID(), 'full'); ?>')"> -->
    <div class="container-one position-relative">

    </div>
</div>
<div class="banner-content banner-single-post-title">
    <div class="container-one">
        <h1 class="title-position-design text-white"><?php echo get_field('banner_title') ? get_field('banner_title') : get_the_title(); ?><span></span></h1>
    </div>

</div>
<!-- ========== banner-section end============= -->

<!-- ========== people details section start ============= -->


<!-- ========== people details section end ============= -->


<!-- ========== team section start ============= -->
<style>
    .insight-item-new .image {
        position: relative;
    }

    .insight-item-new .image h6 {
        position: absolute;
        left: 0;
        top: 0;
        margin-bottom: 0;
    }

    .insight-item-new .image h6 a {
        font-size: 13px;
        font-weight: 600;
        color: #ffffff;
        display: block;
        padding: 5px 12px;
        border-radius: 0 100px 100px 0;
        background: var(--primary-blue);
    }
   
</style>
<div class="team-section pt-100">
    <div class="container-one">
        <div class="section-title-two">
            <!-- <h4 ><?php echo __('UPC News', 'picostrap5-child-base'); ?></h4> -->
            &nbsp;
        </div>
        <div class="row row-cols-xl-4 row-cols-lg-4 row-cols-md-3 row-cols-sm-2 row-cols-2 g-4 mb-20" id="people-filter-search-list">
            <?php
            $args = [
                'post_type' => ['upc_news', 'bulletin', 'news'],
                'posts_per_page' => '-1',
                'post_status' => 'publish',
                'orderby' => 'date',
                'order' => 'desc',
            ];

            $q = new WP_Query($args);


            $excerpt = substr($excerpt, 0, 200);
            if ($q->have_posts()) {
                while ($q->have_posts()) {
                    $q->the_post();
                    $post_type = get_post_type();
                    $post_type_obj = get_post_type_object($post_type);
                    $singular_name = $post_type_obj ? $post_type_obj->labels->singular_name : '';

                    if ($post_type === 'bulletin') {
                        $singular_name = 'IP updates';
                    }
                    $excerpt = get_the_excerpt($post);

            ?>
                    <div class="col">
                        <div class="insight-item insight-item-new style-black">
                            <?php if (!empty(get_the_post_thumbnail_url(get_the_ID()))) : ?>
                                <div class="image">
                                    <a hreflang="<?php echo esc_attr($pll); ?>" href="<?php the_permalink(); ?>"><img src="<?php echo get_the_post_thumbnail_url(get_the_ID(), 'full'); ?>" alt="image"></a>
                                    <h6><a class="insight-title" href="<?php echo $permalink; ?>"><?php echo __($singular_name, 'picostrap5-child-base'); ?></a></h6>
                                </div>
                            <?php endif; ?>
                            <div class="content">
                                <h6><a class="insight-title" href="<?php echo $permalink; ?>"><?php the_title(); ?></a></h6>
                                <p><?php echo $excerpt; ?></p>
                            </div>
                        </div>
                    </div>
            <?php
                }
                wp_reset_query();
            }
            ?>
        </div>
    </div>
</div>

<!-- ========== team section end ============= -->


<!-- ========== expertise-section end======== -->

<div class="footer-top-design-one d-none">
    <div class="box"></div>
</div>


<script type="text/javascript">
    jQuery(function($) {
        var ajax_var = null;

        $(document).on('keyup', '#people-filter-search-form [name="name"]', function(e) {
            $(this).closest('form').trigger('submit');
        });
        $(document).on('change', '#people-filter-search-form select[name="role"]', function(e) {
            $(this).closest('form').trigger('submit');
        });
        $(document).on('change', '#people-filter-search-form select[name="location"]', function(e) {
            $(this).closest('form').trigger('submit');
        });
        $(document).on('change', '#people-filter-search-form select[name="sector"]', function(e) {
            $(this).closest('form').trigger('submit');
        });

        $(document).on('submit', '#people-filter-search-form', function(e) {
            e.preventDefault();

            var this_form = $(this);
            var this_data = this_form.serialize();

            ajax_var = $.ajax({
                beforeSend: function() {
                    if (ajax_var != null) {
                        ajax_var.abort();
                        ajax_var = null;
                    }
                },
                url: '<?php echo admin_url("admin-ajax.php"); ?>',
                method: 'POST',
                dataType: 'JSON',
                data: this_data,
                success: function(r) {
                    // $(document).find('#people-filter-search-list').html(r.html);
                    var data = r.data;
                    let output = '';
                    data.forEach(function(item) {
                        output += `
                                <div class="col">
                                    <div class="team-item style-two style-two">
                                        <div class="team-image">
                                            <a href="${item.permalink}">
                                                <img src="${item.image}" alt="${item.title}">
                                            </a>
                                        </div>
                                        <div class="team-content">
                                            <h6><a href="${item.permalink}">${item.title}</a></h6>
                                            <span>${item.roles} </span>
                                            <span>${item.locations}</span>
                                        </div>
                                    </div>
                                </div>
                            `
                    });
                    document.getElementById('people-filter-search-list').innerHTML = output;
                }
            });
        });
    });
</script>

<?php

get_footer();
