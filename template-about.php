<?php 
/*
    Template Name: About Page
*/

// Exit if accessed directly.
defined( 'ABSPATH' ) || exit;

get_header();
$trans['news'] = 'News';
$trans['load-more'] = 'LOAD MORE';

$pll = pll_current_language();

if($pll == 'de'){
    $trans['news'] = 'Insights';
    $trans['load-more'] = 'Mehr erfahren';
}
?>

<!-- ========== banner-section start============= -->

<div class="banner-video-section overflow-hidden">
    <div class="container-fluid px-0">
        <div class="company-vdo position-relative"
            style="background-image: url('<?php echo get_the_post_thumbnail_url(get_the_ID(), 'full'); ?>');">
            <div class="video-banner-bg">
                <?php 
                    /*if($banner_video_url = get_field('banner_video_url')){
                        // check if youtube
                        ?>
                            <div data-vbg-autoplay="false" data-vbg-muted="false" data-vbg-play-button="true" data-vbg="<?php echo $banner_video_url; ?>" data-vbg-poster="<?php echo get_the_post_thumbnail_url(get_the_ID(), 'full'); ?>"></div>
                        <?php 
                    }*/
                ?>
            </div>
            <div class="inner-overlay"></div>
            <!-- style="background-image:linear-gradient(to bottom, rgba(255,255,255,0.05) 0%, rgba(0,0,0,0.15) 50%), radial-gradient(at top center, rgba(255,255,255,0.20) 50%, rgba(0,0,0,0.40) 190%),  url('<?php echo get_the_post_thumbnail_url(get_the_ID(), 'full'); ?>');"> -->
            <!-- style="background-image:linear-gradient(to bottom, rgba(255,255,255,0.05) 0%, rgba(0,0,0,0.15) 100%), radial-gradient(at top center, rgba(255,255,255,0.40) 0%, rgba(0,0,0,0.40) 120%),  url('<?php echo get_the_post_thumbnail_url(get_the_ID(), 'full'); ?>');"> -->
            <h3 ><?php echo get_field('banner_title') ? get_field('banner_title') : get_the_title(); ?></h3>
            <?php 
                if($banner_video_url = get_field('banner_video_url')){
                    ?>
                        <div  class="video-popup-button">
                            <!-- <a href="https://www.youtube.com/watch?v=u31qwQUeGuM" class="video-popup play-icon"><img src="<?php echo get_stylesheet_directory_uri(); ?>/assets/images/icons/play-icon.svg" alt="image"></a> -->
                            <!-- <a href="https://vimeo.com/user48769004/review/837702986/c569277040" class="video-popup play-icon"><img -->
                            <!-- <a href="https://player.vimeo.com/video/579473943" class="video-popup play-icon"><img -->
                            <!-- <a href="<?php echo $banner_video_url; ?>" class="video-popupp play-icon"><img -->
                                    <!-- src="<?php echo get_stylesheet_directory_uri(); ?>/assets/images/icons/play-icon.svg" alt="image"></a> -->
                            <a hreflang="<?php echo esc_attr($pll); ?>" href="<?php echo $banner_video_url; ?>" class="video-popup play-icon"><img src="<?php echo get_stylesheet_directory_uri(); ?>/assets/images/icons/play-icon.svg" alt="image"></a>
                        </div>
                    <?php 
                }
            ?>
        </div>
    </div>
</div>
<h1 style="opacity:0; display: none;">hidden</h1>
<!-- ========== banner-section end============= -->
<?php 
    $about_us_section = get_field('about_us_section');
?>
    <!-- ========== about-section start============= -->

    <div class="about-section pl-container pt-100 pb-100">
        <div class="container-fluid">
            <div class="row justify-content-start">
                <div class="col-lg-5" >
                    <div class="section-title-one style-red">
                        <h2><?php echo $about_us_section['title']; ?></h2>
                    </div>
                    <?php echo $about_us_section['content']; ?>
                    <div class="senior-parnter-single">
                       <?php echo $about_us_section['people_info']; ?>
                    </div>
                </div>
                <div class="col-lg-7" >
                    <div class="quote-box d-none">
                        <p><?php echo $about_us_section['note']; ?></p>
                        <span><?php echo $about_us_section['note_title']; ?></span>
                    </div>
                    <div class="author-image d-none">
                        <img src="<?php echo $about_us_section['image']; ?>" alt="image">
                    </div>
                    <div class="recent-highlight-quotes">
                        <div class="box-with-border-inner"></div>
                        <div class="box-inner-content">
                            <?php echo get_field('quotes'); ?>
                            <?php 
                                $quote_person_image = get_field('quote_person_image');
                                $person_image = get_stylesheet_directory_uri() . '/assets/images/team/team.jpg';
                                if($quote_person_image){
                                    $roles = get_the_terms( $quote_person_image->ID, 'people_role' );
                                    $roles = join(', ', wp_list_pluck($roles, 'name'));

                                    $person_image = get_field('person_photo_1', $quote_person_image->ID) ? get_field('person_photo_1', $quote_person_image->ID)['sizes']['medium_large'] : get_stylesheet_directory_uri() . '/assets/images/team/team.jpg';
                                    ?>
                                        <div class="team-content quote-inner">
                                            <h6><a hreflang="<?php echo esc_attr($pll); ?>" href="<?php echo get_the_permalink($quote_person_image); ?>"><?php echo get_the_title($quote_person_image); ?></a></h6>
                                            <span><?php echo strtoupper($roles); ?></span>
                                        </div>
                                    <?php 
                                }
                            ?>
                        </div>
                        <div class="box-inner-image">
                            <img src="<?php echo $person_image; ?>" alt="image">
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- ========== about-section end============ -->

<!-- ========== counter_section start======== -->
<?php 
    $state_section= get_field('state_section');
    if(!empty($state_section['state_list'])) :
?>
<div class="counter-section pt-100 pb-100">
    <div class="container-one">
    <!-- <div class="container-fluid"> -->
        <div class="row counter-row">
            <?php foreach($state_section['state_list'] as $value) : ?>
            <div class="col-lg-3 col-md-3 col-6 counter-item" >
                <div class="counter-single style-green">
                    <h3 class="odometer" data-odometer-final="<?php echo $value['count_number']; ?>">01</h3>
                    <p><?php echo $value['content']; ?></p>
                </div>
            </div>
            <?php endforeach; ?>
        </div>
    </div>
</div>
<?php endif; ?>
<!-- ========== counter_section start======== -->

<!-- ========== sponsor_section start======== -->
<?php 
    $client_logo_area = get_field('client_logo_area');
    if(!empty($client_logo_area)) :
?>
    <!-- <a  href="<?php echo $value['url']; ?>"><img src="<?php echo $value['logo_image']; ?>" alt="image"></a> -->
<div class="sponsor-section pt-100 pb-100">
    <div class="container-one">
        <div class="sponsor-wrapper">
            <?php foreach ($client_logo_area as $value) : ?>
            <a hreflang="<?php echo esc_attr($pll); ?>"><img src="<?php echo $value['logo_image']; ?>" alt="image"></a>
            <?php endforeach; ?>
        </div>
    </div>
</div>
<?php endif; ?>
<!-- ========== sponsor_section start======== -->

<!-- ========== culture-section start======== -->
<?php 
    $culture_section = get_field('culture_section');
    $responsible_business_section = get_field('responsible_business_section');
?>
<div class="culture-section pl-container pb-100">
    <div class="bottom-box"></div>
    <div class="container-fluid">
        <div class="row justify-content-start">
            <div class="col-lg-5" >
                <div class="section-title-one style-green">
                    <h2><?php echo $culture_section['title']; ?></h2>
                </div>
                <div><?php echo $culture_section['content']; ?></div>
                <div class="culture-content-2"><?php echo $culture_section['content_2']; ?></div>
            </div>
            <div class="col-lg-7" >
                <div class="quote-box d-none">
                    <p><?php // echo $culture_section['quote_content']; ?></p>
                    <span><?php // echo $culture_section['quote_title']; ?></span>
                </div>
                <div class="recent-highlight-quotes">
                    <div class="box-with-border-inner"></div>
                    <div class="box-inner-content">
                        <?php echo $culture_section['quote_content']; ?>
                        <?php 
                            $quote_person_image = $culture_section['quote_person_image']; // get_field('quote_person_image');
                            $person_image = get_stylesheet_directory_uri() . '/assets/images/team/team.jpg';
                            if($quote_person_image){
                                $roles = get_the_terms( $quote_person_image->ID, 'people_role' );
                                $roles = join(', ', wp_list_pluck($roles, 'name'));

                                $person_image = get_field('person_photo_1', $quote_person_image->ID) ? get_field('person_photo_1', $quote_person_image->ID)['sizes']['medium_large'] : get_stylesheet_directory_uri() . '/assets/images/team/team.jpg';
                                ?>
                                    <div class="team-content quote-inner">
                                        <h6><a hreflang="<?php echo esc_attr($pll); ?>" href="<?php echo get_the_permalink($quote_person_image); ?>"><?php echo get_the_title($quote_person_image); ?></a></h6>
                                        <span><?php echo strtoupper($roles); ?></span>
                                    </div>
                                <?php 
                            }
                        ?>
                    </div>
                    <div class="box-inner-image">
                        <img src="<?php echo $person_image; ?>" alt="image">
                    </div>
                </div>
                <div class="value-card">
                    <?php echo $culture_section['our_value']; ?>
                </div>
            </div>
        </div>
        <div class="row">
            <div class="col-md-12 col-lg-12 col-xl-5" >
                <div class="atrb_43">
                    <div class="section-title-one style-blue">
                        <h2><?php echo $responsible_business_section['title']; ?></h2>
                    </div>
                    <?php echo $responsible_business_section['content']; ?>
                    <?php 
                        if(is_array($responsible_business_section['link']) && !empty($responsible_business_section['link'])){
                            ?>
                                <a hreflang="<?php echo esc_attr($pll); ?>" href="<?php echo $responsible_business_section['link']['url']; ?>" class="eg-btn btn--primary-green btn--lg2"><?php echo $responsible_business_section['link']['title']; ?> <i
                                        class="bi bi-arrow-right"></i></a>
                            <?php 
                        }
                    ?>
                </div>
                
            </div>
        </div>
    </div>
</div>

<!-- ========== culture-section start======== -->

    <!-- ========== service section start ============= -->

    <div class="news-section position-relative">
        <div class="about-news-box"></div>
        <div class="container-one">
            <div class="section-title-one style-yellow">
                <h2 ><?php echo __($trans['news'], 'picostrap5-child-base'); ?></h2>
            </div>
            <div class="row justify-content-center mb-40 g-lg-4 g-3" >
                <?php 
                    $args = array(
                        'post_type' => 'news',
                        'posts_per_page' => 4,
                        'orderby' => 'date',
                        'order' => 'DESC',
                        'post_status' => 'publish',
                    );
                    
                    $query = new WP_Query( $args );
                    
                    if ( $query->have_posts() ) {
                        while ( $query->have_posts() ) {
                            $query->the_post();
                            $post_type = get_post_type();
                            $permalink = '';
                            if($pll == 'es'){
                                if($post_type == 'bulletin'){
                                    $permalink = str_replace("/es/Bolet%C3%ADn/","/bulletin/", get_permalink());
                                }elseif($post_type == 'news'){
                                    $permalink = str_replace("/es/noticias/","/news/", get_permalink());
                                }
                            }elseif($pll == 'de'){
                                if($post_type == 'bulletin'){
                                    $permalink = str_replace("/de/bulletin/","/bulletin/", get_permalink());
                                }elseif($post_type == 'news'){
                                    $permalink = str_replace("/de/news/","/news/", get_permalink());
                                }
                            }else{
                                $permalink = get_permalink();
                            }
                            ?>
                            <div class="col-lg-3 col-md-6 col-12">
                                <div class="insight-item style-yellow">
                                    <div class="image">
                                        <img src="<?php echo get_the_post_thumbnail_url(get_the_ID(), 'full'); ?>" alt="image">
                                    </div>
                                    <div class="content">
                                        <h6><a hreflang="<?php echo esc_attr($pll); ?>" href="<?php echo $permalink; ?>"><?php echo get_the_title(); ?></a></h6>
                                        <p><?php echo get_the_excerpt(); ?></p>
                                        <!-- <a hreflang="</?php echo esc_attr($pll); ?>" href="</?php  echo $permalink; ?>"></?php echo __('MORE', 'picostrap5-child-base'); ?></a> -->
                                    </div>
                                </div>
                            </div>
                            <?php 
                        }
                    }
                        wp_reset_postdata();
                ?>
                
            </div>
            <?php 
                $newsAll = '';
                if($pll == 'de'){
                    $newsAll = home_url('/de/all-news/'); 
                }elseif($pll == 'es'){
                    $newsAll = home_url('/es/all-news/'); 
                }else{
                    $newsAll = home_url('/all-news/'); 
                }
            ?>
            <a hreflang="<?php echo esc_attr($pll); ?>" target="_blank"  href="<?php echo home_url('/all-news/'); ?>" class="eg-btn btn--primary-yellow btn--lg2"><?php echo __($trans['load-more'], 'picostrap5-child-base'); ?><i class="bi bi-arrow-right"></i></a>
        </div>
    </div>

    <!-- ========== service section end ============= -->

<!-- ========== trending-topic-section start======== -->
<?php 
    $trending_topics_section = get_field('trending_topics_section');
    $auto_list = isset($trending_topics_section['auto_list']) ? $trending_topics_section['auto_list'] : false;
    if($auto_list) :
        ?>
            <div class="trending-topic-section pt-100 position-relative">
                <div class="trending-right-box"></div>
                <div class="container-one">
                    <div class="section-title-one style-blue">
                        <h2 ><?php echo $trending_topics_section['title']; ?></h2>
                    </div>
                    <div class="row justify-content-lg-start justify-content-center g-sm-4 g-2">
                        <?php 
                            $args = array(
                                'post_type' => array('news', 'bulletin', 'upc_news', 'tribe_events'),
                                'posts_per_page' => 4,
                                'post_status' => 'publish',
                                'orderby' => 'date',
                                'order' => 'DESC',

                                'meta_query' => array(
                                    array(
                                        'key' => 'trending',
                                        'value' => 1,
                                        'compare' => '=',
                                    ),
                                ),
                            );

                            $q = new WP_Query( $args );
                            // echo '<pre>';
                            // print_r($q);
                            // echo '</pre>';
                    
                            if ( $q->have_posts() ) {
                                while ( $q->have_posts() ) {
                                    $q->the_post();
                                    ?>
                                    <div class="col-lg-3 col-md-6 col-6" >
                                        <div class="sector-item style-blue">
                                            <h6 class="title"><?php echo get_the_title(); ?></h6>
                                            <p><?php echo get_the_excerpt(); ?></p>
                                            <div class="arrow-btn">
                                                <a hreflang="<?php echo esc_attr($pll); ?>" href="<?php echo get_the_permalink(); ?>"><img src="<?php echo get_stylesheet_directory_uri(  );?>/assets/images/icons/arrow-white.svg" alt="arrow"></a>
                                            </div>
                                        </div>
                                    </div>
                                    <?php 
                                }
                            }
                                wp_reset_postdata();


                        ?>
                    </div>
                </div>
            </div>
        <?php 
    elseif(!empty($trending_topics_section['list'])) :
?>
<div class="trending-topic-section pt-100 position-relative">
    <div class="trending-right-box"></div>
    <div class="container-one">
        <div class="section-title-one style-blue">
            <h2 ><?php echo $trending_topics_section['title']; ?></h2>
        </div>
        <div class="row justify-content-lg-start justify-content-center g-sm-4 g-2">
            <?php foreach($trending_topics_section['list'] as $value) : ?>
            <div class="col-lg-3 col-md-6 col-6" >
                <div class="sector-item style-blue">
                    <h6 class="title"><?php echo $value['title']?></h6>
                    <p><?php echo $value['content']?></p>
                    <div class="arrow-btn">
                        <a hreflang="<?php echo esc_attr($pll); ?>" href="<?php echo $value['link']; ?>"><img src="<?php echo get_stylesheet_directory_uri(  );?>/assets/images/icons/arrow-white.svg" alt="arrow"></a>
                    </div>
                </div>
            </div>
            <?php endforeach; ?>
        </div>
    </div>
</div>
<?php endif;?>
<!-- ========== trending-topic-section start======== -->


<?php get_footer();