<?php

// Exit if accessed directly.
defined('ABSPATH') || exit;


$pll = pll_current_language();


$trans['people'] = 'People';
$trans['expertise'] = 'Expertise';
$trans['insights'] = 'Insights';
$trans['phone'] = '+44 (0)20 7430 7500';
$trans['email'] = '<EMAIL>';

if ($pll == 'es') {
  $trans['people'] = 'Equipo';
  $trans['expertise'] = 'Experiencia';
  $trans['insights'] = 'Actualidad';
  $trans['phone'] = '+34 (0)919 269 970';
  $trans['email'] = '<EMAIL>';
} elseif ($pll == 'de') {
  $trans['phone'] = '+49 (0)69 506 08 733';
  $trans['email'] = '<EMAIL>';
}
?>


<?php
$custom_logo_id = get_theme_mod('custom_logo');
$logo_url = wp_get_attachment_image_src($custom_logo_id, 'full');


// Retrive slug 
$current_url = $_SERVER["REQUEST_URI"];
$url_parts = explode('/', $current_url);
$first_slug = '';
if (!empty($url_parts[1])) {
  $first_slug = $url_parts[1];
}
$home_url = home_url('/');
if ($first_slug == 'de') {
  $home_url = site_url() . '/' . 'ger';
}

?>

<!doctype html>
<html <?php language_attributes(); ?>>

<head>
  <!-- Required meta tags -->
  <meta charset="<?php bloginfo('charset'); ?>">
  <meta name="viewport" content="width=device-width, initial-scale=1">
  <!-- Google Tag Manager -->
  <script>
    (function(w, d, s, l, i) {
      w[l] = w[l] || [];
      w[l].push({
        'gtm.start': new Date().getTime(),
        event: 'gtm.js'
      });
      var f = d.getElementsByTagName(s)[0],
        j = d.createElement(s),
        dl = l != 'dataLayer' ? '&l=' + l : '';
      j.async = true;
      j.src =
        'https://www.googletagmanager.com/gtm.js?id=' + i + dl;
      f.parentNode.insertBefore(j, f);
    })(window, document, 'script', 'dataLayer', 'GTM-P3ZDSJTT');
  </script>
  <!-- End Google Tag Manager -->
  <!-- wp_head begin -->
  <?php wp_head(); ?>
  <!-- wp_head end -->
</head>

<body <?php body_class(); ?>>
  <?php wp_body_open(); ?>

  <!-- Google Tag Manager (noscript) -->
  <noscript><iframe src="https://www.googletagmanager.com/ns.html?id=GTM-P3ZDSJTT"
      height="0" width="0" style="display:none;visibility:hidden"></iframe></noscript>
  <!-- End Google Tag Manager (noscript) -->
  <!-- =============== search-area start =============== -->

  <div class="mobile-search">
    <div class="container-one">
      <form  id="searchForm" method="get" action="<?php echo site_url('/'); ?>">
        <div class="row d-flex justify-content-center">
          <div class="col-11 col-sm-11 col-md-11">
            <label><?php echo __('What are you looking for?', 'picostrap5-child-base'); ?></label>
            <input type="text" id="main_search" placeholder="<?php echo __('Search', 'picostrap5-child-base'); ?>">
            <button type="reset" class="d-none">Reset</button>
          </div>
          <div class="col-1 d-flex justify-content-end align-items-center gap-2">
            <div class="search-cross-btn search-btnn"  id="searchButton">
              <i class='bx bx-search-alt-2'></i>
            </div>
            <div class="search-cross-btn search-close-btn">
              <i class="bi bi-x"></i>
            </div>
          </div>
          <div class="col-md-12">
            <div class="search-option-filters">
              <ul>
                <li>
                  <label>
                    <input type="radio" name="type" value="all" checked> <span><?php echo __('All', 'picostrap5-child-base'); ?></span>
                  </label>
                </li>
                <li>
                  <label>
                    <input type="radio" name="type" value="people"> <span><?php echo __($trans['people'], 'picostrap5-child-base'); ?></span>
                  </label>
                </li>
                <li>
                  <label>
                    <input type="radio" name="type" value="expertise"> <span><?php echo __($trans['expertise'], 'picostrap5-child-base'); ?></span>
                  </label>
                </li>
                <li>
                  <label>
                    <input type="radio" name="type" value="articles"> <span><?php echo __($trans['insights'], 'picostrap5-child-base'); ?></span>
                  </label>
                </li>
              </ul>
            </div>
          </div>
          <div class="col-md-12">
            <div id="filtered-data">

            </div>
          </div>
        </div>
      </form>
    </div>
  </div>

  <!-- =============== search-area end  =============== -->


  <!-- ========== header============= -->

  <header class="style-1">
    <!-- <div class="container-fluid position-relative  d-flex justify-content-between  align-items-center"> -->
    <div class="container-one position-relative  d-flex justify-content-between  align-items-center menu-container-box">
      <div class="logo-area only-for-mobile">
        <!-- <a href="index.html"><img src="assets/images/logo/header-logo.svg" alt="image"></a> -->
        <?php if (!empty($logo_url[0])) : ?>
          <a href="<?php echo esc_url($home_url); ?>" rel="home">
            <img src="<?php echo esc_url($logo_url[0]); ?>" alt="<?php bloginfo('name'); ?>">
          </a>
        <?php endif ?>

        <div class="breadcrumps-wrap">

          <?php
          $upc_link = '';
          $upc_text = '';
          $sector_link = '';
          $sector_text = '';
          $service_link = '';
          $service_text = '';

          $news_link = '';
          $bulletin_link = '';
          $bulletin_text = '';
          if ($pll == 'es') {
            $upc_link = "/es/tpu/";
            $upc_text = "TPU";

            $sector_link = "/expertise/#sectors";
            $sector_text = "Sectores";

            $service_link = "/es/expertise/#services";
            $service_text = "Servicios";

            $news_link = '/es/noticia/';
            $bulletin_link = '/es/bulletins2/';
            $bulletin_text = 'Boletines';
          } elseif ($pll == 'de') {
            $upc_link = "/de/upc/";
            $upc_text = "UPC";

            $sector_link = "/de/expertise/#sectors";
            $sector_text = "Sector";

            $service_link = "/de/expertise/#services";
            $service_text = "Service";

            $news_link = '/all-news/';

            $bulletin_link = '/de/bulletins3/';
            $bulletin_text = 'Bulletins';
          } else {
            $upc_link = "/upc/";
            $upc_text = "UPC";

            $sector_link = "/expertise/#sectors";
            $sector_text = "Sector";

            $service_link = "/expertise/#services";
            $service_text = "Service";

            $news_link = '/all-news/';

            $bulletin_link = '/bulletins/';
            $bulletin_text = 'Bulletins';
          }
          if (is_single() && 'upc_news' == get_post_type()) {
          ?>
            <span typeof="v:Breadcrumb"><a rel="v:url" property="v:title" title="<?php echo $upc_text; ?>" href="<?php echo $upc_link; ?>"><?php echo $upc_text; ?></a></span> »
            <span typeof="v:Breadcrumb"><span property="v:title"><?php the_title(); ?></span></span>
          <?php
          } elseif (
            get_the_ID() === 3048 ||
            get_the_ID() === 636 ||
            get_the_ID() === 639 ||
            get_the_ID() === 651 ||
            get_the_ID() === 645 ||
            get_the_ID() === 654 ||
            get_the_ID() === 1233 ||
            get_the_ID() === 9022 ||
            get_the_ID() === 9076 ||
            get_the_ID() === 9080 ||
            get_the_ID() === 9094 ||
            get_the_ID() === 9078 ||
            get_the_ID() === 8268 ||
            get_the_ID() === 8250 ||
            get_the_ID() === 10028 ||
            get_the_ID() === 8270 ||
            get_the_ID() === 8260
          ) {
          ?>
            <span typeof="v:Breadcrumb"><a rel="v:url" property="v:title" title="Careers" href="/careers/">Careers</a></span> »
            <span typeof="v:Breadcrumb"><span property="v:title"><?php the_title(); ?></span></span>
          <?php
          } elseif (is_single() && 'people' == get_post_type()) {
          ?>
            <span typeof="v:Breadcrumb"><a rel="v:url" property="v:title" title="People" href="/people/">People</a></span> »
            <span typeof="v:Breadcrumb"><span property="v:title"><?php the_title(); ?></span></span>
          <?php
          } elseif (is_single() && 'sector' == get_post_type()) {
          ?>
            <span typeof="v:Breadcrumb"><a rel="v:url" property="v:title" title="<?php echo $sector_text; ?>" href="<?php echo $sector_link; ?>"><?php echo $sector_text; ?></a></span> »
            <span typeof="v:Breadcrumb"><span property="v:title"><?php the_title(); ?></span></span>
          <?php
          } elseif (is_single() && 'service' == get_post_type()) {
          ?>
            <span typeof="v:Breadcrumb"><a rel="v:url" property="v:title" title="<?php echo $service_text; ?>" href="<?php echo $service_link; ?>"><?php echo $service_text; ?></a></span> »
            <span typeof="v:Breadcrumb"><span property="v:title"><?php the_title(); ?></span></span>
          <?php
          } elseif (is_single() && 'news' == get_post_type()) {
          ?>
            <span typeof="v:Breadcrumb"><a rel="v:url" property="v:title" title="News" href="<?php echo $news_link; ?>">News</a></span> »
            <span typeof="v:Breadcrumb"><span property="v:title"><?php the_title(); ?></span></span>
          <?php
          } elseif (is_single() && 'bulletin' == get_post_type()) {
          ?>
            <span typeof="v:Breadcrumb"><a rel="v:url" property="v:title" title="<?php echo $bulletin_text; ?>" href="<?php echo $bulletin_link; ?>"><?php echo $bulletin_text; ?></a></span> »
            <span typeof="v:Breadcrumb"><span property="v:title"><?php the_title(); ?></span></span>
          <?php
          } elseif (is_single() && 'ip_basics' == get_post_type()) {
          ?>
            <span typeof="v:Breadcrumb"><a rel="v:url" property="v:title" title="IP Basics" href="#">IP Basics</a></span> »
            <span typeof="v:Breadcrumb"><span property="v:title"><?php the_title(); ?></span></span>
          <?php
          } elseif (is_single() && 'patent_essential' == get_post_type()) {
          ?>
            <span typeof="v:Breadcrumb"><a rel="v:url" property="v:title" title="Patent Essential" href="#">Patent Essential</a></span> »
            <span typeof="v:Breadcrumb"><span property="v:title"><?php the_title(); ?></span></span>
          <?php
          } else {
            if (function_exists('yoast_breadcrumb')) {
              yoast_breadcrumb('<p id="breadcrumbs">', '</p>');
              if ($first_slug) {
                echo " / " . $first_slug;
              }
            }
          }

          ?>
        </div>
        <?php if (is_single() && 'news' == get_post_type() || is_single() && 'bulletin' == get_post_type()) :  ?>
          <div class="breadcrumb-news-buletins">
            <ul class="banner-breadcrumb">
              <?php if ('news' == get_post_type()) :  ?>
                <?php if ($pll == 'es') : ?>
                  <li><a href="<?php echo home_url() . '/actualidad/'; ?>">Regresa</a></li>
                <?php else : ?>
                  <li><a href="<?php echo home_url() . '/insights'; ?>">Back to Insights</a></li>
                <?php endif ?>
              <?php else : ?>
                <?php if ($pll == 'es') : ?>
                  <li><a href="<?php echo home_url() . '/bulletins2/'; ?>">Regresa</a></li>
                <?php else : ?>
                  <li><a href="<?php echo home_url() . '/bulletins'; ?>">Back to Bulletins</a></li>
                <?php endif ?>
              <?php endif ?>
            </ul>
          </div>
        <?php endif ?>
      </div>
      <div
        class="main-nav text-end d-lg-flex d-block justify-content-start justify-content-start align-items-center">
        <!-- mobile-nav -->
        <div class="mobile-logo-area d-lg-none d-flex justify-content-between align-items-center">
          <div class="mobile-logo-wrap">
            <!-- <a href="index.html"><img alt="image" src="assets/images/logo/header-logo.svg"></a> -->
            <?php
            the_custom_logo();
            ?>
          </div>
          <div class="menu-close-btn">
            <i class="bi bi-x-lg text-dark"></i>
          </div>
        </div>
        <?php
        if (is_page(11087)) {
          wp_nav_menu(array(
            'theme_location' => 'chinese_main_menu',
            'container' => false,
            'menu_class' => '',
            'fallback_cb' => '__return_false',
            'items_wrap' => '<ul id="%1$s" class="menu-list %2$s">%3$s</ul>',
            'walker' => new bootstrap_5_wp_nav_menu_walker_extended()
          ));
        } elseif (is_page(1255)) {
          wp_nav_menu(array(
            'theme_location' => 'japanese_main_menu',
            'container' => false,
            'menu_class' => '',
            'fallback_cb' => '__return_false',
            'items_wrap' => '<ul id="%1$s" class="menu-list %2$s">%3$s</ul>',
            'walker' => new bootstrap_5_wp_nav_menu_walker_extended()
          ));
        } elseif (is_page(1259)) {
          wp_nav_menu(array(
            'theme_location' => 'korean_main_menu',
            'container' => false,
            'menu_class' => '',
            'fallback_cb' => '__return_false',
            'items_wrap' => '<ul id="%1$s" class="menu-list %2$s">%3$s</ul>',
            'walker' => new bootstrap_5_wp_nav_menu_walker_extended()
          ));
        } elseif (is_page(14797)) {
          wp_nav_menu(array(
            'theme_location' => 'german_main_menu',
            'container' => false,
            'menu_class' => '',
            'fallback_cb' => '__return_false',
            'items_wrap' => '<ul id="%1$s" class="menu-list %2$s">%3$s</ul>',
            'walker' => new bootstrap_5_wp_nav_menu_walker_extended()
          ));
        } else {
          wp_nav_menu(array(
            'theme_location' => 'primary',
            'container' => false,
            'menu_class' => '',
            'fallback_cb' => '__return_false',
            'items_wrap' => '<ul id="%1$s" class="menu-list %2$s">%3$s</ul>',
            'walker' => new bootstrap_5_wp_nav_menu_walker_extended()
          ));
        }
        ?>
        <!-- <ul class="menu-list">
                    <li class="menu-item-has-children">
                        <a href="people.html" class="drop-down">People</a><i
                            class='bi bi-chevron-down dropdown-icon'></i>
                            <ul class="sub-menu">
                                <li><a href="people.html">People One</a></li>
                                <li><a href="people-two.html">People Two</a></li>
                            </ul>
                    </li>
                    <li class="menu-item-has-children">
                        <a href="expertise-realstate.html" class="active">Experise</a><i class='bi bi-chevron-down dropdown-icon'></i>
                        <ul class="sub-menu">
                            <li><a href="expertise-one.html">Expertise One</a></li>
                            <li><a href="expertise-two.html">Expertise Two</a></li>
                            <li><a href="expertise-three.html">Expertise Three</a></li>
                            <li><a href="expertise-four.html">Expertise Four</a></li>
                            <li><a href="expertise-five.html">Expertise Five</a></li>
                        </ul>
                    </li>
                    <li class="menu-item-has-children">
                        <a href="insights-one.html">Insights</a><i class='bi bi-chevron-down dropdown-icon'></i>
                    </li>
                    <li><a href="upc.html">UPC</a></li>
                    <li class="menu-item-has-children">
                        <a href="career.html" class="drop-down">Careers</a><i
                            class='bi bi-chevron-down dropdown-icon'></i>
                            <ul class="sub-menu">
                                <li><a href="career.html">Career One</a></li>
                                <li><a href="career-two.html">Career Two</a></li>
                                <li><a href="career-three.html">Career Three</a></li>
                                <li><a href="career-four.html">Career Four</a></li>
                                <li><a href="career-five.html">Career Five</a></li>
                            </ul>
                    </li>
                    <li class="menu-item-has-children">
                        <a href="about.html">About</a>
                    </li>
                    <li><a href="responisble-business.html">RESPONSIBLE BUSINESS</a></li>
                </ul> -->
      </div>

      <ul class="language-list"  style="padding-right: 25px;">
        <?php

        // add_filter( 'acf/validate_post_id', 'skip_acf_polylang_options', 10, 2 );
        // remove_filter( 'acf/validate_post_id', 'skip_acf_polylang_options' );
        $german_homepage_link = get_field('german_homepage_link', 'option');
        $japanese_homepage_link = get_field('japanese_homepage_link', 'option');
        $korean_homepage_link = get_field('korean_homepage_link', 'option');
        $chinese_homepage_link = get_field('korean_homepage_link', 'option');

        $german_homepage_link = get_post('14797');
        $japanese_homepage_link = get_post('1255');
        $korean_homepage_link = get_post('1259');
        $chinese_homepage_link = get_post('11087');

        global $wp_query;


        $pll_the_languages = pll_the_languages(array('raw' => 1));
        if ($pll_the_languages) {
          foreach ($pll_the_languages as $l) {

            $url = $l['url'];
            $slug = $l['slug'];
            if ($wp_query->post->ID == $japanese_homepage_link->ID || $wp_query->post->ID == $german_homepage_link->ID || $wp_query->post->ID == $korean_homepage_link->ID || $wp_query->post->ID == $chinese_homepage_link->ID) {

              if (strtolower($slug) == 'en') {
                // $url = home_url('/');
                $url = site_url();
              } else if (strtolower($slug) == 'es') {
                $url = home_url('/es/');
              } else if (strtolower($slug) == 'de') {
                $url = home_url('/ger/');
              }
            }


            if (in_array($first_slug, ['jp', 'kr', 'cn']) && $slug == 'en' && $l['current_lang']) {
              $l['current_lang'] = false;
            }

            if (($l['current_lang'] && $first_slug == $slug) || ($l['current_lang'] && empty($first_slug)) || ($slug == 'de' && $first_slug == 'ger') || ($l['current_lang'] && $slug == 'en')) {
              continue;
            }
            if (is_singular('people')) {
              $url = str_replace("/es/people/", "/es/equipo/", $url);
            }
            // if(is_singular( 'bulletin' )){
            //   $url = str_replace("/es/Boletín/","/bulletin", $url); 
            //   // if($url === "https://www.boult.com/es/" || $url === "https://www.boult.com/"){
            //   //   $url  = get_permalink($wp_query->post->ID);
            //   // }
            // }
            // if(is_singular( 'news' )){
            //   // if($url === "https://www.boult.com/es/" || $url === "https://www.boult.com/"){
            //   //   $url  = get_permalink($wp_query->post->ID);
            //   // }
            // }
            //echo  $url;
        ?>
            <li class="<?php echo $slug; ?>">
              <a href="<?php echo $url; ?>"><?php echo $slug; ?></a>
            </li>
        <?php
          }
        }
        ?>
        <?php /* 
                <li>
                    <a href="https://uat.boult.com.cn/en/">CN</a>
                </li> */ ?>
        <?php
        // if(is_home() || is_front_page() || $wp_query->post->ID == $japanese_homepage_link->ID || $wp_query->post->ID == $korean_homepage_link->ID){
        if (true) {
          if ($german_homepage_link && $wp_query->post->ID != $german_homepage_link->ID || ($slug == 'de' && $first_slug == 'ger')) {
        ?>
            <?php if ($slug != $first_slug) :  ?>
              <li class="<?php echo is_singular('people') && $first_slug == 'de' ? 'd-none' : '' ?>">
                <a href="<?php echo get_permalink($german_homepage_link); ?>">DE</a>
              </li>
            <?php endif ?>
          <?php
          }
          if ($japanese_homepage_link && $wp_query->post->ID != $japanese_homepage_link->ID) {
          ?>
            <li>
              <a href="<?php echo get_permalink($japanese_homepage_link); ?>">JP</a>
            </li>
          <?php
          }
          if ($korean_homepage_link && $wp_query->post->ID != $korean_homepage_link->ID) {
          ?>
            <li>
              <a href="<?php echo get_permalink($korean_homepage_link); ?>">KR</a>
            </li>
          <?php
          }
          if ($chinese_homepage_link && $wp_query->post->ID != $chinese_homepage_link->ID) {
          ?>
            <li>
              <a href="<?php echo get_permalink($chinese_homepage_link); ?>">CN</a>
            </li>
        <?php
          }
        }
        ?>
      </ul>

      <div class="header-icons d-lg-flex d-none justify-content-end" style="position: relative; top: unset; right: unset; max-width: 260px;">
        <ul>
          <li>
            <a href="<?php echo get_permalink(pll_get_post(1121/*get_field('locations_page_link', 'option')*/)); ?>">
              <i class="bi bi-geo-alt"></i>
            </a>
          </li>
          <li><i class="bi bi-search search-btn"></i></li>
          <li><a class="contact-button" href="mailto:<?php echo __($trans['email'], 'picostrap5-child-base'); ?>"><i class="bi bi-envelope"></i></a></li>
          <li><a class="contact-info" href="tel:<?php echo __($trans['phone'], 'picostrap5-child-base'); ?>"><i class="bi bi-telephone"></i> <span class="d-xl-flex d-none"><?php echo __($trans['phone'], 'picostrap5-child-base'); ?></span> </a></li>
        </ul>
        <div class="flag-img">
          <?php
          $url = $_SERVER['REQUEST_URI'];
          $urlExplode = explode('/', $url);

          $pll = pll_current_language();
          if ($urlExplode[1] == 'en') {
          ?>
            <img src="<?php echo get_stylesheet_directory_uri() . '/assets/images/flag/en.png'; ?>" alt="en-flag">
          <?php
          } elseif ($urlExplode[1] == 'de') {
          ?>
            <img src="<?php echo get_stylesheet_directory_uri() . '/assets/images/flag/german.png'; ?>" alt="german-flag">
          <?php
          } elseif ($urlExplode[1] == 'es') {
          ?>
            <img src="<?php echo get_stylesheet_directory_uri() . '/assets/images/flag/es.png'; ?>" alt="es-flag">
          <?php
          } elseif ($urlExplode[1] == 'jp') {
          ?>
            <img src="<?php echo get_stylesheet_directory_uri() . '/assets/images/flag/jp.png'; ?>" alt="jp-flag">
          <?php
          } elseif ($urlExplode[1] == 'kr') {
          ?>
            <img src="<?php echo get_stylesheet_directory_uri() . '/assets/images/flag/kr.png'; ?>" alt="kr-flag">
          <?php
          } elseif ($urlExplode[1] == 'cn') {
          ?>
            <img src="<?php echo get_stylesheet_directory_uri() . '/assets/images/flag/cn.png'; ?>" alt="cn-flag">
          <?php
          } else {
          ?>
            <img src="<?php echo get_stylesheet_directory_uri() . '/assets/images/flag/en.png'; ?>" alt="en-flag">
          <?php
          }
          ?>
        </div>
      </div>

      <div class="mobile-menu-btn d-lg-none d-block text-end">
        <i class="bi bi-list text-dark"></i>
      </div>
    </div>
    <div class="container-one position-relative  d-flex justify-content-between  align-items-center">
      <div class="logo-area not-for-mobile">
        <!-- <a href="index.html"><img src="assets/images/logo/header-logo.svg" alt="image"></a> -->
        <?php if (!empty($logo_url[0])) : ?>
          <a href="<?php echo esc_url($home_url); ?>" rel="home">
            <img src="<?php echo esc_url($logo_url[0]); ?>" alt="<?php bloginfo('name'); ?>">
          </a>
        <?php endif ?>
        <div class="breadcrumps-wrap">

          <?php
          $upc_link = '';
          $upc_text = '';
          $sector_link = '';
          $sector_text = '';
          $service_link = '';
          $service_text = '';

          $news_link = '';
          $bulletin_link = '';
          $bulletin_text = '';
          if ($pll == 'es') {
            $upc_link = "/es/tpu/";
            $upc_text = "TPU";

            $sector_link = "/expertise/#sectors";
            $sector_text = "Sectores";

            $service_link = "/es/expertise/#services";
            $service_text = "Servicios";

            $news_link = '/es/noticia/';
            $bulletin_link = '/es/bulletins2/';
            $bulletin_text = 'Boletines';
          } elseif ($pll == 'de') {
            $upc_link = "/de/upc/";
            $upc_text = "UPC";

            $sector_link = "/de/expertise/#sectors";
            $sector_text = "Sector";

            $service_link = "/de/expertise/#services";
            $service_text = "Service";

            $news_link = '/all-news/';

            $bulletin_link = '/de/bulletins3/';
            $bulletin_text = 'Bulletins';
          } else {
            $upc_link = "/upc/";
            $upc_text = "UPC";

            $sector_link = "/expertise/#sectors";
            $sector_text = "Sector";

            $service_link = "/expertise/#services";
            $service_text = "Service";

            $news_link = '/all-news/';

            $bulletin_link = '/bulletins/';
            $bulletin_text = 'Bulletins';
          }
          if (is_single() && 'upc_news' == get_post_type()) {
          ?>
            <span typeof="v:Breadcrumb"><a rel="v:url" property="v:title" title="<?php echo $upc_text; ?>" href="<?php echo $upc_link; ?>"><?php echo $upc_text; ?></a></span> »
            <span typeof="v:Breadcrumb"><span property="v:title"><?php the_title(); ?></span></span>
          <?php
          } elseif (
            get_the_ID() === 3048 ||
            get_the_ID() === 636 ||
            get_the_ID() === 639 ||
            get_the_ID() === 651 ||
            get_the_ID() === 645 ||
            get_the_ID() === 654 ||
            get_the_ID() === 1233 ||
            get_the_ID() === 9022 ||
            get_the_ID() === 9076 ||
            get_the_ID() === 9080 ||
            get_the_ID() === 9094 ||
            get_the_ID() === 9078 ||
            get_the_ID() === 8268 ||
            get_the_ID() === 8250 ||
            get_the_ID() === 10028 ||
            get_the_ID() === 8270 ||
            get_the_ID() === 8260
          ) {
          ?>
            <span typeof="v:Breadcrumb"><a rel="v:url" property="v:title" title="Careers" href="/careers/">Careers</a></span> »
            <span typeof="v:Breadcrumb"><span property="v:title"><?php the_title(); ?></span></span>
          <?php
          } elseif (is_single() && 'people' == get_post_type()) {
          ?>
            <span typeof="v:Breadcrumb"><a rel="v:url" property="v:title" title="People" href="/people/">People</a></span> »
            <span typeof="v:Breadcrumb"><span property="v:title"><?php the_title(); ?></span></span>
          <?php
          } elseif (is_single() && 'sector' == get_post_type()) {
          ?>
            <span typeof="v:Breadcrumb"><a rel="v:url" property="v:title" title="<?php echo $sector_text; ?>" href="<?php echo $sector_link; ?>"><?php echo $sector_text; ?></a></span> »
            <span typeof="v:Breadcrumb"><span property="v:title"><?php the_title(); ?></span></span>
          <?php
          } elseif (is_single() && 'service' == get_post_type()) {
          ?>
            <span typeof="v:Breadcrumb"><a rel="v:url" property="v:title" title="<?php echo $service_text; ?>" href="<?php echo $service_link; ?>"><?php echo $service_text; ?></a></span> »
            <span typeof="v:Breadcrumb"><span property="v:title"><?php the_title(); ?></span></span>
          <?php
          } elseif (is_single() && 'news' == get_post_type()) {
          ?>
            <span typeof="v:Breadcrumb"><a rel="v:url" property="v:title" title="News" href="<?php echo $news_link; ?>">News</a></span> »
            <span typeof="v:Breadcrumb"><span property="v:title"><?php the_title(); ?></span></span>
          <?php
          } elseif (is_single() && 'bulletin' == get_post_type()) {
          ?>
            <span typeof="v:Breadcrumb"><a rel="v:url" property="v:title" title="<?php echo $bulletin_text; ?>" href="<?php echo $bulletin_link; ?>"><?php echo $bulletin_text; ?></a></span> »
            <span typeof="v:Breadcrumb"><span property="v:title"><?php the_title(); ?></span></span>
          <?php
          } elseif (is_single() && 'ip_basics' == get_post_type()) {
          ?>
            <span typeof="v:Breadcrumb"><a rel="v:url" property="v:title" title="IP Basics" href="#">IP Basics</a></span> »
            <span typeof="v:Breadcrumb"><span property="v:title"><?php the_title(); ?></span></span>
          <?php
          } elseif (is_single() && 'patent_essential' == get_post_type()) {
          ?>
            <span typeof="v:Breadcrumb"><a rel="v:url" property="v:title" title="Patent Essential" href="#">Patent Essential</a></span> »
            <span typeof="v:Breadcrumb"><span property="v:title"><?php the_title(); ?></span></span>
          <?php
          } else {
            if (function_exists('yoast_breadcrumb')) {
              yoast_breadcrumb('<p id="breadcrumbs">', '</p>');
              if ($first_slug) {
                echo "/" . $first_slug;
              }
            }
          }
          ?>
        </div>
        <?php if (is_single() && 'news' == get_post_type() || is_single() && 'bulletin' == get_post_type()) :  ?>
          <div class="breadcrumb-news-buletins">
            <ul class="banner-breadcrumb">
              <?php if ('news' == get_post_type()) :  ?>
                <?php if ($pll == 'es') : ?>
                  <li><a href="<?php echo home_url() . '/actualidad/'; ?>">Regresa</a></li>
                <?php else : ?>
                  <li><a href="<?php echo home_url() . '/insights'; ?>">Back to Insights</a></li>
                <?php endif ?>
              <?php else : ?>
                <?php if ($pll == 'es') : ?>
                  <li><a href="<?php echo home_url() . '/bulletins2/'; ?>">Regresa</a></li>
                <?php else : ?>
                  <li><a href="<?php echo home_url() . '/bulletins'; ?>">Back to Bulletins</a></li>
                <?php endif ?>
              <?php endif ?>
            </ul>
          </div>
        <?php endif ?>
        <?php  /*
                <?php 
                    $homeTitle = get_field('banner_section', 666);
                    $peopleTitle = get_field('banner_title', 416);
                    $responsibleTitle = get_field('banner_title', 8503);
                   
                ?>
                <?php  if(is_page(666)){ ?>
                  <h4><?php echo $homeTitle['banner_image'][0]['title']; ?></h4>
                <?php } ?>
                <?php  if(is_page(416)){ ?>
                  <h4><?php echo $peopleTitle; ?></h4>
                <?php } ?>
                <?php  if(is_page(8503)){ ?>
                  <h4><?php echo $responsibleTitle; ?></h4>
                <?php } ?> */ ?>
      </div>
    </div>
    <style>
      @media (max-width: 1199px) {
          header.style-1 .header-icons {
            max-width: 140px !important;
          }
      }
      .contact-bar {
        display: inline-flex;
        align-items: center;
        gap: 15px;
      }

      .contact-info {
        /* border: 1px solid #ddd; */
        display: flex;
        align-items: center;
      }

      /* .contact-info span {
        display: block;
        font-size: 13px;
        font-weight: 600;
        color: #000;
        line-height: 1;
      } */

      .contact-info  {
        text-decoration: none;
        font-size: 12px;
        font-weight: 600;
        line-height: 1;
        color: #000;
        display: flex;
        align-items: center;
        gap: 2px;
      }
      .contact-info  i{
        color: #000;
        margin-right: 5px;
        transform: rotate(8deg);
      }
    </style>
  </header>

  <!-- ========== header end============= -->

  <?php /*if(function_exists('lc_custom_header')) lc_custom_header(); else {
      
      //STANDARD NAV
      
      if (get_theme_mod("enable_topbar") ) : ?>
        <!-- ******************* The Topbar Area ******************* -->
        <div id="wrapper-topbar" class="py-2 <?php echo get_theme_mod('topbar_bg_color_choice','bg-light') ?> <?php echo get_theme_mod('topbar_text_color_choice','text-dark') ?>">
          <div class="container">
            <div class="row">
              <div id="topbar-content" class="col-md-12 text-center small"> <?php echo do_shortcode(get_theme_mod('topbar_content')) ?>	</div>
            </div>
          </div>
        </div>
        <?php endif; ?>
        

        <!-- ******************* The Navbar Area ******************* -->
        <div id="wrapper-navbar" itemscope itemtype="http://schema.org/WebSite">

          <a class="skip-link visually-hidden-focusable" href="#theme-main"><?php esc_html_e( 'Skip to content', 'picostrap5-child-base' ); ?></a>

          
          <nav class="navbar <?php echo get_theme_mod('picostrap_header_navbar_expand','navbar-expand-lg'); ?> <?php echo get_theme_mod('picostrap_header_navbar_position')." ". get_theme_mod('picostrap_header_navbar_color_scheme','navbar-dark').' '. get_theme_mod('picostrap_header_navbar_color_choice','bg-dark'); ?>" aria-label="Main Navigation" >
            <div class="container">
              <div id="logo-tagline-wrap">
                  <!-- Your site title as branding in the menu -->
                  <?php if ( ! has_custom_logo() ) { ?>

                    <?php if ( is_front_page() && is_home() ) : ?>

                      <div class="navbar-brand mb-0 h3"><a rel="home" href="<?php echo esc_url( home_url( '/' ) ); ?>" title="<?php echo esc_attr( get_bloginfo( 'name', 'display' ) ); ?>" itemprop="url"><?php bloginfo( 'name' ); ?></a></div>

                    <?php else : ?>

                      <a class="navbar-brand mb-0 h3" rel="home" href="<?php echo esc_url( home_url( '/' ) ); ?>" title="<?php echo esc_attr( get_bloginfo( 'name', 'display' ) ); ?>" itemprop="url"><?php bloginfo( 'name' ); ?></a>

                    <?php endif; ?>


                  <?php } else {
                    the_custom_logo();
                  } ?><!-- end custom logo -->

                
                  <?php if (!get_theme_mod('header_disable_tagline')): ?>
                    <small id="top-description" class="text-muted d-none d-md-block mt-n2">
                      <?php bloginfo("description") ?>
                    </small>
                  <?php endif ?>
              
              
                  </div> <!-- /logo-tagline-wrap -->



              <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNavDropdown" aria-controls="navbarsExample05" aria-expanded="false" aria-label="Toggle navigation">
                <span class="navbar-toggler-icon"></span>
              </button>

              <div class="collapse navbar-collapse" id="navbarNavDropdown">
                <?php 
                  wp_nav_menu(array(
                    'theme_location' => 'primary',
                    'container' => false,
                    'menu_class' => '',
                    'fallback_cb' => '__return_false',
                    'items_wrap' => '<ul id="%1$s" class="navbar-nav me-auto mb-2 mb-md-0 %2$s">%3$s</ul>',
                    'walker' => new bootstrap_5_wp_nav_menu_walker()
                ));
                ?>
                
                <?php if (get_theme_mod('enable_search_form')): ?>
                  <form action="<?php echo bloginfo('url') ?>" method="get" id="header-search-form">
                    <input class="form-control" type="text" placeholder="Search" aria-label="Search" name="s" value="<?php the_search_query(); ?>">
                  </form> 
                <?php endif ?>
                <div class="search-form-icon">
                  <a href="javascript:;" class="search-btn"><i class="fa fa-search"></i></a>
                </div>

              </div> <!-- .collapse -->
            </div> <!-- .container -->
          </nav> <!-- .site-navigation -->
          <?php

          //AS A TEST / DEMO for a mock-up megamenu
          //include("nav-static-mega.php");
          ?>
        </div><!-- #wrapper-navbar end -->
        <!-- ******************* Search Area Start ******************* -->
          <div class="mobile-search">
              <div class="container-one">
                  <form method="get" action="<?php echo home_url('/'); ?>">
                    <div class="row d-flex justify-content-center">
                        <div class="col-md-11">
                            <label>What are you looking for?</label>
                            <input id="main_search" type="text" placeholder="Search">
                        </div>
                        <div class="col-1 d-flex justify-content-end align-items-center gap-2">
                            <div class="search-cross-btn">
                                <i class="bx bx-search-alt-2 fa fa-search"></i>
                            </div>
                            <div class="search-cross-btn">
                                <i class="bi bi-x fa fa-times"></i>
                            </div>
                        </div>
                        <div class="col-md-12">
                          <div id="filtered-data">

                          </div> 
                        </div>

                    </div>
                  </form>
              </div>
          </div>
          <!-- ******************* Search Area End ******************* -->
      
    <?php 
    } // END ELSE CASE */
  ?>
  <style>
    .banner-breadcrumb {
      margin-top: 10px;
      padding: 0;
      list-style: none;
      display: none;
    }

    .banner-breadcrumb a {
      color: rgb(227, 6, 19);
    }

    .cky-box-bottom-left {
      display: none;
    }
  </style>
  <main id='theme-main'>