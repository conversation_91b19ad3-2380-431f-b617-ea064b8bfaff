<?php 
/*
Template Name: Sector Details View
*/

// Exit if accessed directly.
defined( 'ABSPATH' ) || exit;
$pll = pll_current_language();
get_header();

?>

<div class="logo-section">
        <div class="container-one">
            <div class="row justify-content-start">
                <div class="col-lg-6">
                    <div class="logo-area invisible">
                        <!-- <img src="assets/images/logo/header-logo.svg" alt="image"> -->
                        <?php 
                          the_custom_logo();
                        ?>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- ========== software-section start============= -->

    <div class="software-section pt-120 pb-100">
        <div class="container-fluid">
            <div class="row gy-4">
                <div class="col-lg-6">
                    <div class="section-title-one style-blue pl-container">
                        <h1 ><?php echo get_the_title(); ?></h1>
                    </div>
                    <div class="focus-list-area">
                        <h6 ><?php echo get_field('area_of_focus_label') ? get_field('area_of_focus_label') : 'Areas of focus:'; ?></h6>
                        <ul class="focus-list" >
                            <?php 
                                $sub_sectors = wp_get_post_terms(get_the_ID(), 'sub_sector');
                                if(get_field('area_of_focus_content')){
                                    echo get_field('area_of_focus_content');
                                }
                                else if(!empty($sub_sectors)){
                                    foreach ($sub_sectors as $key => $value) {
                                        ?>
                                            <li><a  hreflang="<?php echo esc_attr($pll); ?>" href="<?php echo get_term_link($value); ?>"><?php echo $value->name; ?></a></li>
                                        <?php 
                                    }
                                }
                                else{
                                    $sub_sectors = get_posts(['post_type' => 'sector', 'posts_per_page' => '-1', 'post_parent' => get_the_ID()]);
                                    if(!empty($sub_sectors)){
                                        foreach ($sub_sectors as $key => $value) {
                                            ?>
                                                <li><a hreflang="<?php echo esc_attr($pll); ?>" href="javascript:;"><?php echo $value->post_title; ?></a></li>
                                            <?php 
                                        }
                                    }
                                    else{
                                        ?>
                                            <!-- <li><a href="#">AI</a></li>
                                            <li><a href="#">Bioinformatics</a></li>
                                            <li><a href="#">Blockchain and distributed ledgers</a></li>
                                            <li><a href="#">Communications and network</a></li>
                                            <li><a href="#">Computer games</a></li>
                                            <li><a href="#">Data and software security, cryptography and digital rights management (DRM)</a></li>
                                            <li><a href="#">Data management and storage, databases and data compression</a></li>
                                            <li><a href="#">Digital assistants, virtual assistants and software agents</a></li>
                                            <li><a href="#">Fintech and adtech</a></li>
                                            <li><a href="#">Machine vision</a></li>
                                            <li><a href="#">Metaverse virtual reality (VR) and augmented reality (AR)</a></li>
                                            <li><a href="#">Motor capture</a></li>
                                            <li><a href="#">Multimedia, audio/video processing and animation</a></li>
                                            <li><a href="#">Natural language processing</a></li>
                                            <li><a href="#">Quantum computing</a></li>
                                            <li><a href="#">Robotic process automation</a></li>
                                            <li><a href="#">Search engines</a></li>
                                            <li><a href="#">Signal processing</a></li>
                                            <li><a href="#">Software applications and systems, mobile applications user interfaces</a></li> -->
                                        <?php 
                                    }
                                }
                            ?>
                        </ul>
                    </div>
                    <!-- <div class="about-people-card">
                        <div  class="quote-box">
                            <div class="quote-box-inner">
                                <?php // echo get_field('quotes'); ?>
                            </div>
                        </div>
                    </div> -->
                </div>
                <div class="col-lg-6">
                    <div class="software-content">
                        <!-- <h6 >The <?php echo trim(get_the_title()); ?> Team</h6> -->
                        <div >
                            <?php 
                                the_content();
                                echo get_field('the_team');
                            ?>
                        </div>
                        <h6  class="<?php echo get_field('what_sets_us_apart') ? '' : 'd-none'; ?>"><?php echo __('What sets us apart?', 'picostrap5-child-base'); ?></h6>
                        <div  class="<?php echo get_field('what_sets_us_apart') ? '' : 'd-none'; ?>">
                            <?php 
                                echo get_field('what_sets_us_apart');
                            ?>
                            
                        </div>
                        <h6  class="<?php echo get_field('case_studies') ? '' : 'd-none'; ?>"><?php echo __('Case Studies', 'picostrap5-child-base'); ?></h6>
                        <div  class="<?php echo get_field('case_studies') ? '' : 'd-none'; ?>">
                            <?php 
                                echo get_field('case_studies');
                            ?>
                            
                        </div>
                    </div>                       
                    <!-- <div class="blue-box">
                    </div> -->
                </div>
            </div>
        </div>
    </div>

    <!-- ========== software-section end============= -->

    <!-- ========== team section start ============= -->

    <?php 
        $partners_list = get_field('partners_list');
        if(!$partners_list){
            $partners_list = 'auto';
        }
        $partners = get_field('partners');
    ?>

    <div class="team-section <?php echo $partners_list != 'auto' && empty($partners) ? '' : 'd-none'; ?>">
        <div class="container-one">
            <div class="section-title-two">
                <h4 ><?php echo __('Partners', 'picostrap5-child-base'); ?></h4>
            </div>
            <div class="row row-cols-xl-4 row-cols-lg-4 row-cols-md-3 row-cols-sm-2 row-cols-2 g-4 mb-30">
                <?php 
                    if($partners_list == 'auto'){
                        $current_sector_id = get_the_ID();
                        $args = array(
                            'post_type' => 'people',
                            'posts_per_page' => '-1',
                            'orderby' => 'title',
                            'order' => 'ASC',
                            'post_status' => 'publish',
                            'meta_query' => array(
                                array(
                                    'key'     => 'sectors',
                                    'value'   => sprintf(':"%s";', $current_sector_id),
                                    'compare' => 'LIKE',
                                )
                            ),
                        );

                        $q = new WP_Query($args);
                        if($q->have_posts()){
                            while($q->have_posts()){
                                $q->the_post();

                                $roles = get_the_terms( get_the_ID(), 'people_role' );
                                $roles = join(', ', wp_list_pluck($roles, 'name'));

                                $locations = get_the_terms( get_the_ID(), 'location' );
                                $locations = join(', ', wp_list_pluck($locations, 'name'));
                                ?>
                                    <div class="col" >
                                        <div class="team-item style-two style-two">
                                            <div class="team-image">
                                                <a hreflang="<?php echo esc_attr($pll); ?>" href="<?php echo get_the_permalink(); ?>?referer=sector">
                                                    <img src="<?php echo get_field('person_photo_1') ? get_field('person_photo_1')['sizes']['medium_large'] : get_stylesheet_directory_uri() . '/assets/images/team/team.jpg'; ?>" alt="image">
                                                </a>
                                            </div>
                                            <div class="team-content">
                                                <h6><a hreflang="<?php echo esc_attr($pll); ?>" href="<?php echo get_the_permalink(); ?>?referer=sector"><?php echo get_the_title(); ?></a></h6>
                                                <span><?php echo $roles; ?> </span>
                                                <span><?php echo $locations; ?> </span>
                                            </div>
                                        </div>
                                    </div>
                                <?php 
                            }
                        }
                        wp_reset_query();
                    }
                    else{
                        if(!empty($partners)){
                            foreach ($partners as $value) {
                                $pid = $value->ID;

                                $roles = get_the_terms( $pid, 'people_role' );
                                $roles = join(', ', wp_list_pluck($roles, 'name'));

                                $locations = get_the_terms( $pid, 'location' );
                                $locations = join(', ', wp_list_pluck($locations, 'name'));
                                ?>
                                    <div class="col" >
                                        <div class="team-item style-two style-two">
                                            <div class="team-image">
                                                <a hreflang="<?php echo esc_attr($pll); ?>" href="<?php echo get_the_permalink($pid); ?>?referer=sector">
                                                    <img src="<?php echo get_field('person_photo_1', $pid) ? get_field('person_photo_1', $pid)['sizes']['medium_large'] : get_stylesheet_directory_uri() . '/assets/images/team/team.jpg'; ?>" alt="image">
                                                </a>
                                            </div>
                                            <div class="team-content">
                                                <h6><a hreflang="<?php echo esc_attr($pll); ?>" href="<?php echo get_the_permalink($pid); ?>?referer=sector"><?php echo get_the_title($pid); ?></a></h6>
                                                <span><?php echo $roles; ?> </span>
                                                <span><?php echo $locations; ?> </span>
                                            </div>
                                        </div>
                                    </div>
                                <?php 
                            }
                        }
                    }
                    
                ?>
            </div>
        </div>
    </div>

    <!-- ========== team section end ============= -->

    <!-- ========== recent-highlightsection start ============= -->

    <div class="recent-highlight pt-100 <?php echo get_field('recent_highlights') ? '' : 'd-none'; ?>">
        <div class="container-one">
            <div class="row">
                <div class="col-lg-6">
                    <div class="section-title-one style-yellow">
                        <h2 ><?php echo __('Recent highlights', 'picostrap5-child-base'); ?></h2>
                        <div class="box-with-border p-3" >
                            <?php echo get_field('quotes'); ?>
                        </div>
                    </div>
                </div>
                <div class="col-lg-6">
                    <div >
                        <?php 
                            echo get_field('recent_highlights');
                        ?>
                        
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- ========== recent-highlight section end ============= -->


    <!-- ========== service section start ============= -->
    <?php $bulletins = get_field('bulletins'); ?>
    <div class="insights-section pt-120 <?php echo $bulletins ? '' : 'd-none'; ?>">
        <div class="container-one">
            <div class="section-title-one style-red">
                <h2 ><?php echo __('Insights', 'picostrap5-child-base'); ?> </h2>
            </div> 
            <div class="row justify-content-center mb-40 gy-4">
                <?php 
                    if($bulletins){
                        foreach ($bulletins as $value) {
                            ?>
                                <div class="col-lg-3 col-md-6 col-sm-6 col-6" >
                                    <div class="insight-item">
                                        <div class="image">
                                            <img src="<?php echo get_the_post_thumbnail_url($value, 'full'); ?>" alt="image">
                                        </div>
                                        <div class="content">
                                            <h6><a hreflang="<?php echo esc_attr($pll); ?>" href="<?php echo get_the_permalink($value); ?>"><?php echo get_the_title($value); ?></a></h6>
                                            <a hreflang="<?php echo esc_attr($pll); ?>" href="<?php echo get_the_permalink($value); ?>">MORE</a>
                                        </div>
                                    </div>
                                </div>
                            <?php 
                        }
                    }
                ?>
            </div>
            <a  hreflang="<?php echo esc_attr($pll); ?>" href="<?php echo home_url('insights'); ?>" class="eg-btn btn--primary-red btn--lg2"><?php echo __('INSIGHTS', 'picostrap5-child-base'); ?><i class="bi bi-arrow-right"></i></a>
        </div>
    </div>

    <!-- ========== service section end ============= -->

    <!-- ========== sector section start ============= -->

    <?php $other_sectors = get_field('other_sectors'); ?>

    <div class="sector-section pt-100 bg-light-box pb-100 overflow-hidden <?php echo $other_sectors ? '' : 'd-none'; ?>">
        <div class="container-one">
            <div class="section-title-one style-yellow">
                <h2 ><?php echo __('Sectors', 'picostrap5-child-base'); ?></h2>
            </div>  
            <div class="row justify-content-lg-start justify-content-center g-md-4 g-2" >
                <?php 
                    if($other_sectors){
                        foreach ($other_sectors as $value) {
                            ?>
                                <div class="col-lg-3 col-md-6 col-sm-6 col-6">
                                    <div class="sector-item style-yellow">
                                        <h6 class="title"><?php echo get_the_title($value); ?></h6>
                                        <p><?php echo get_the_excerpt($value); ?></p>
                                            <div class="arrow-btn">
                                                <a hreflang="<?php echo esc_attr($pll); ?>" href="<?php echo get_the_permalink($value); ?>"><img src="<?php echo get_stylesheet_directory_uri(); ?>/assets/images/icons/arrow-white.svg" alt="arrow"></a>
                                            </div>
                                    </div>
                                </div>
                            <?php 
                        }
                    }
                ?>
            </div>
        </div>
    </div>

    <!-- ========== sector section start ============= -->


<?php get_footer();