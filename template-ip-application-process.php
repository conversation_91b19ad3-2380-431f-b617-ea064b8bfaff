<?php
/*
Template Name: IP Application Process
*/

// Exit if accessed directly.
defined( 'ABSPATH' ) || exit;
$pll = pll_current_language();
get_header();

?>
<!-- ========== banner-section start============= -->

<div class="banner-section d-flex flex-column align-items-staer justify-content-center position-relative"
    style="background-image:url('<?php echo get_the_post_thumbnail_url(get_the_ID(), 'full'); ?>')">
    <div class="inner-overlay"></div>
    <!-- style="background-image:linear-gradient(to bottom, rgba(255,255,255,0.05) 0%, rgba(0,0,0,0.15) 50%), radial-gradient(at top center, rgba(255,255,255,0.20) 50%, rgba(0,0,0,0.40) 190%), url('<?php echo get_the_post_thumbnail_url(get_the_ID(), 'full'); ?>')"> -->
    <!-- style="background-image:linear-gradient(to bottom, rgba(255,255,255,0.05) 0%, rgba(0,0,0,0.15) 100%), radial-gradient(at top center, rgba(255,255,255,0.40) 0%, rgba(0,0,0,0.40) 120%), url('<?php echo get_the_post_thumbnail_url(get_the_ID(), 'full'); ?>')"> -->
    <div class="container-one position-relative">
       
    </div>
</div>
<div class="banner-content banner-single-post-title title-bg-yellow">
    <div class="container-one">
        <h1 class="title-position-design text-white" ><?php echo get_field('banner_title') ? get_field('banner_title') : get_the_title(); ?></h1>
    </div>
    
</div>
<!-- ========== banner-section end============= -->

<!-- ========== people details section start ============= -->
<?php 
    $expertise_section = get_field('expertise_section');
?>
<?php if(!empty($expertise_section['content'])) : ?>
<div class="people-section pt-60 pb-60">
    <div class="container-one">
        <div class="row">
            <div class="col-lg-8">
                <div class="section-title-one style-yellow">
                    <?php if(!empty($expertise_section['title'])) : ?>
                        <h2 ><?php echo $expertise_section['title']; ?></h2>
                    <?php endif;?>
                    <div >
                        
                    <?php echo $expertise_section['content']; ?>
                    </div>
                </div>
            </div>
            <div class="col-lg-4">
                <div class="right-box d-none">

                </div>
            </div>
        </div>
    </div>
</div>
<?php endif; ?>
<!-- ========== people details section end ============= -->

    <!-- ========== sector section start ============= -->

<div class="sector-section pt-60 pb-60">
    <div class="container-one">
        <div class="row justify-content-lg-start justify-content-center g-4">
            <?php 
                $list_all_sectors = true;
                if(!get_field('list_all_sectors')){
                    $list_all_sectors = false;
                }

                if($list_all_sectors){
                    $args = array(
                        'post_type' => 'ip_app_process',
                        'posts_per_page' => '-1',
                        'post_parent' => '0',
                        'post_status' => 'publish',
                        'orderby' => 'title',
                        'orderby' => 'menu_order',
                        'order' => 'ASC',
                    );

                    $q = new WP_Query($args);
                    if($q->have_posts()){
                        while($q->have_posts()){
                            $q->the_post();
                            ?>
                                <div class="col-lg-3 col-md-6 col-sm-6" >
                                    <a  hreflang="<?php echo esc_attr($pll); ?>" href="<?php echo get_the_permalink(); ?>">
                                    <div class="sector-item">
                                        <h6 class="title"><?php echo get_the_title(); ?></h6>
                                        <p><?php echo get_the_excerpt(); ?></p>
                                        <div class="arrow-btn">
                                            <img src="<?php echo get_stylesheet_directory_uri(); ?>/assets/images/icons/arrow-white.svg" alt="arrow">
                                        </div>
                                    </div>
                                    </a>
                                </div>
                            <?php 
                        }
                        wp_reset_postdata( );
                    }
                }
                else{
                    $sectors = get_field('sectors_section');
                    if(!empty($sectors)){
                        foreach ($sectors as $value) {
                            ?>
                                <div class="col-lg-3 col-md-6 col-sm-10" >
                                    <a hreflang="<?php echo esc_attr($pll); ?>" href="<?php echo isset($value['link']['url']) ? $value['link']['url'] : ''; ?>">
                                    <div class="sector-item">
                                        <h6 class="title"><?php echo $value['title']; ?></h6>
                                        <p><?php echo $value['excerpt']; ?></p>
                                        <div class="arrow-btn">
                                            <img src="<?php echo get_stylesheet_directory_uri(); ?>/assets/images/icons/arrow-white.svg" alt="arrow">
                                        </div>
                                    </div>
                                    </a>
                                </div>
                            <?php 
                        }
                    }
                }

                
            ?>
        </div>
    </div>
</div>

    <!-- ========== sector section start ============= -->

<!-- ========== service section start ============= -->

<div class="service-section-1 pt-120 d-none">
    <div class="container-one">
        <div class="section-title-one style-blue">
            <h2 ><?php echo __('Services', 'picostrap5-child-base'); ?></h2>
        </div>
        <div class="row justify-content-center">
            <?php 
                $args = array(
                    'post_type' => 'service',
                    'post_status' => 'publish',
                    'posts_per_page' => '4',
                    'orderby' => 'date',
                    'order' => 'DESC',
                );

                $query = new WP_Query($args);

                if($query->have_posts()){
                    while($query->have_posts()){
                        break;
                        $query->the_post();
                        $l = pll_current_language();
                        if($l == 'en'){
                            $l = '';
                        }
                        else{
                            $l = '/' . $l;
                        }
                        ?>
                            <div class="col-lg-3 col-md-6 col-sm-10" >
                                <div class="service-item">
                                    <div class="image">
                                        <img src="<?php echo get_the_post_thumbnail_url(get_the_ID(), 'full'); ?>" alt="image">
                                    </div>
                                    <div class="content">
                                        <!-- <h6><a href="<?php echo home_url($l . '/services/?view-service=' . (get_post(get_the_ID())->post_name)); // get_the_permalink(); ?>"><?php echo get_the_title(); ?></a></h6> -->
                                        <h6><a hreflang="<?php echo esc_attr($pll); ?>" href="<?php echo get_the_permalink(); ?>"><?php echo get_the_title(); ?></a></h6>
                                        <p><?php echo get_the_excerpt(); ?></p>
                                        <!-- <a href="<?php echo home_url($l . '/services/?view-service=' . (get_post(get_the_ID())->post_name)); // get_the_permalink(); ?>">MORE</a> -->
                                        <a hreflang="<?php echo esc_attr($pll); ?>" href="<?php echo get_the_permalink(); ?>">MORE</a>
                                    </div>
                                </div>
                            </div>
                        <?php 
                    }
                }
                wp_reset_postdata();
            ?>
        </div>
    </div>
</div>

<?php 

    $services_section = get_field('services_section');
 
    if(!empty($services_section)) :
?>
<!-- <div class="service-section-1 pt-120">
    <div class="container-one">
        <div class="section-title-one style-blue">
            <h2 >Services</h2>
        </div>
        <div class="row justify-content-center">
            <?php foreach($services_section as $value) : ?>
                
            <div class="col-lg-3 col-md-6 col-sm-10" >
                <div class="service-item">
                    <div class="image">
                        <img src="<?php echo $value['image']; ?>" alt="image">
                    </div>
                    <div class="content">
                        <h6><a href="<?php // echo $value['link']['url']; ?>"><?php echo $value['title']; ?></a></h6>
                        <p><?php echo $value['content']?></p>
                        <a href="<?php // echo $value['link']['url']; ?>"><?php // echo $value['link']['title']; ?></a>
                    </div>
                </div>
            </div>
            <?php endforeach; ?>
        </div>
    </div>
</div> -->
<?php endif; ?>
<!-- ========== service section end ============= -->

    <!-- ========== search section start ============= -->

    <div class="search-section mt-100">
        <div class="container-one" >
            <form method="get" action="" id="home-page-banner-search-form">
                <div class="search-block">
                    <button type="search"><i class="bi bi-search"></i></button>
                    <input type="text" placeholder="<?php echo __('SEARCH OUR TEAM', 'picostrap5-child-base'); ?>" name="keyword">
                </div>
                <div class="search-block bg-black p-0">
                    <div class="home-page-banner-search-list" id="home-page-banner-search-list"></div>
                </div>
            </form>
        </div>
    </div>

    <!-- ========== search section start ============= -->
<?php 

get_footer();