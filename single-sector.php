<?php
// Exit if accessed directly.
defined('ABSPATH') || exit;

get_header();
$pll = pll_current_language();
?>
<style>
    .postid-2522 .team-section .section-title-two.srt5 {
        display: none;
    }

    .postid-2522 .team-section .row.row-cols-xl-6.srt5 {
        display: none;
    }
</style>
<div class="logo-section d-none">
    <div class="container-one">
        <div class="row justify-content-start">
            <div class="col-lg-6">
                <div class="logo-area invisible">
                    <!-- <img src="assets/images/logo/header-logo.svg" alt="image"> -->
                    <?php
                    the_custom_logo();
                    ?>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- ========== software-section start============= -->

<!-- <div class="software-section pt-120 pb-100"> -->
<div class="software-section pt-240">
    <!-- <div class="container-fluid"> -->
    <div class="container-one">
        <div class="row gy-4">
            <div class="col-lg-6">
                <div class="section-title-one style-blue pl-container">
                    <h1><?php echo get_the_title(); ?></h1>
                </div>
                <div class="focus-list-area<?php echo get_field('style_area_of_focus_like_case_study') ? '-2' : ''; ?> <?php echo get_field('hide_area_of_focus') ? 'd-none' : ''; ?>">
                    <?php
                    if (get_field('style_area_of_focus_like_case_study')) {
                    ?>
                        <div class="recruitment-process-quotes-single mb-30">
                            <div class="section-title-one style-pink">
                                <h2><?php echo get_field('area_of_focus_label') ? get_field('area_of_focus_label') : __('Areas of focus:', 'picostrap5-child-base'); ?></h2>
                            </div>
                            <div class="quote-content">
                                <?php
                                $sub_sectors = wp_get_post_terms(get_the_ID(), 'sub_sector', array('orderby' => 'name', 'order' => 'ASC'));
                                if (get_field('area_of_focus_content')) {
                                    echo get_field('area_of_focus_content');
                                } else if (!empty($sub_sectors)) {

                                    foreach ($sub_sectors as $key => $value) {
                                ?>

                                        <li><a hreflang="<?php echo esc_attr($pll); ?>" href="<?php echo get_term_link($value); ?>"><?php echo $value->name; ?></a></li>
                                        <?php
                                    }
                                } else {

                                    $sub_sectors = get_posts(['post_type' => 'sector', 'posts_per_page' => '-1', 'post_parent' => get_the_ID(), 'orderby' => 'name', 'order' => 'ASC']);
                                    if (!empty($sub_sectors)) {
                                        foreach ($sub_sectors as $key => $value) {
                                        ?>
                                            <!-- <li><a href="javascript:;"><?php echo $value->post_title; ?></a></li> -->
                                            <li><?php echo $value->post_title; ?></li>
                                        <?php
                                        }
                                    } else {
                                        ?>
                                        <li><a hreflang="<?php echo esc_attr($pll); ?>" href="#"><?php echo __('AI', 'picostrap5-child-base'); ?></a></li>
                                        <li><a hreflang="<?php echo esc_attr($pll); ?>" href="#"><?php echo __('Bioinformatics', 'picostrap5-child-base'); ?></a></li>
                                        <li><a hreflang="<?php echo esc_attr($pll); ?>" href="#"><?php echo __('Blockchain and distributed ledgers', 'picostrap5-child-base'); ?></a></li>
                                        <li><a hreflang="<?php echo esc_attr($pll); ?>" href="#"><?php echo __('Communications and network', 'picostrap5-child-base'); ?></a></li>
                                        <li><a hreflang="<?php echo esc_attr($pll); ?>" href="#"><?php echo __('Computer games', 'picostrap5-child-base'); ?></a></li>
                                        <li><a hreflang="<?php echo esc_attr($pll); ?>" href="#"><?php echo __('Data and software security, cryptography and digital rights management (DRM)', 'picostrap5-child-base'); ?></a></li>
                                        <li><a hreflang="<?php echo esc_attr($pll); ?>" href="#"><?php echo __('Data management and storage, databases and data compression', 'picostrap5-child-base'); ?></a></li>
                                        <li><a hreflang="<?php echo esc_attr($pll); ?>" href="#"><?php echo __('Digital assistants, virtual assistants and software agents', 'picostrap5-child-base'); ?></a></li>
                                        <li><a hreflang="<?php echo esc_attr($pll); ?>" href="#"><?php echo __('Fintech and adtech', 'picostrap5-child-base'); ?></a></li>
                                        <li><a hreflang="<?php echo esc_attr($pll); ?>" href="#"><?php echo __('Machine vision', 'picostrap5-child-base'); ?></a></li>
                                        <li><a hreflang="<?php echo esc_attr($pll); ?>" href="#"><?php echo __('Metaverse virtual reality (VR) and augmented reality (AR)', 'picostrap5-child-base'); ?></a></li>
                                        <li><a hreflang="<?php echo esc_attr($pll); ?>" href="#"><?php echo __('Motor capture', 'picostrap5-child-base'); ?></a></li>
                                        <li><a hreflang="<?php echo esc_attr($pll); ?>" href="#"><?php echo __('Multimedia, audio/video processing and animation', 'picostrap5-child-base'); ?></a></li>
                                        <li><a hreflang="<?php echo esc_attr($pll); ?>" href="#"><?php echo __('Natural language processing', 'picostrap5-child-base'); ?></a></li>
                                        <li><a hreflang="<?php echo esc_attr($pll); ?>" href="#"><?php echo __('Quantum computing', 'picostrap5-child-base'); ?></a></li>
                                        <li><a hreflang="<?php echo esc_attr($pll); ?>" href="#"><?php echo __('Robotic process automation', 'picostrap5-child-base'); ?></a></li>
                                        <li><a hreflang="<?php echo esc_attr($pll); ?>" href="#"><?php echo __('Search engines', 'picostrap5-child-base'); ?></a></li>
                                        <li><a hreflang="<?php echo esc_attr($pll); ?>" href="#"><?php echo __('Signal processing', 'picostrap5-child-base'); ?></a></li>
                                        <li><a hreflang="<?php echo esc_attr($pll); ?>" href="#"><?php echo __('Software applications and systems, mobile applications user interfaces', 'picostrap5-child-base'); ?></a></li>
                                <?php
                                    }
                                }
                                ?>
                            </div>
                            <div class="section-title-one style-pink">
                                <h2></h2>
                            </div>
                        </div>
                    <?php
                    } else {
                    ?>
                        <h6><?php echo get_field('area_of_focus_label') ? get_field('area_of_focus_label') : 'Areas of focus:'; ?></h6>
                        <ul class="focus-list">
                            <?php
                            if (get_field('area_of_focus_content')) {
                                echo get_field('area_of_focus_content');
                            } else {
                            ?>
                                <li><a hreflang="<?php echo esc_attr($pll); ?>" href="#"><?php echo __('AI', 'picostrap5-child-base'); ?></a></li>
                                <li><a hreflang="<?php echo esc_attr($pll); ?>" href="#"><?php echo __('Bioinformatics', 'picostrap5-child-base'); ?></a></li>
                                <li><a hreflang="<?php echo esc_attr($pll); ?>" href="#"><?php echo __('Blockchain and distributed ledgers', 'picostrap5-child-base'); ?></a></li>
                                <li><a hreflang="<?php echo esc_attr($pll); ?>" href="#"><?php echo __('Communications and network', 'picostrap5-child-base'); ?></a></li>
                                <li><a hreflang="<?php echo esc_attr($pll); ?>" href="#"><?php echo __('Computer games', 'picostrap5-child-base'); ?></a></li>
                                <li><a hreflang="<?php echo esc_attr($pll); ?>" href="#"><?php echo __('Data and software security, cryptography and digital rights management (DRM)', 'picostrap5-child-base'); ?></a></li>
                                <li><a hreflang="<?php echo esc_attr($pll); ?>" href="#"><?php echo __('Data management and storage, databases and data compression', 'picostrap5-child-base'); ?></a></li>
                                <li><a hreflang="<?php echo esc_attr($pll); ?>" href="#"><?php echo __('Digital assistants, virtual assistants and software agents', 'picostrap5-child-base'); ?></a></li>
                                <li><a hreflang="<?php echo esc_attr($pll); ?>" href="#"><?php echo __('Fintech and adtech', 'picostrap5-child-base'); ?></a></li>
                                <li><a hreflang="<?php echo esc_attr($pll); ?>" href="#"><?php echo __('Machine vision', 'picostrap5-child-base'); ?></a></li>
                                <li><a hreflang="<?php echo esc_attr($pll); ?>" href="#"><?php echo __('Metaverse virtual reality (VR) and augmented reality (AR)', 'picostrap5-child-base'); ?></a></li>
                                <li><a hreflang="<?php echo esc_attr($pll); ?>" href="#"><?php echo __('Motor capture', 'picostrap5-child-base'); ?></a></li>
                                <li><a hreflang="<?php echo esc_attr($pll); ?>" href="#"><?php echo __('Multimedia, audio/video processing and animation', 'picostrap5-child-base'); ?></a></li>
                                <li><a hreflang="<?php echo esc_attr($pll); ?>" href="#"><?php echo __('Natural language processing', 'picostrap5-child-base'); ?></a></li>
                                <li><a hreflang="<?php echo esc_attr($pll); ?>" href="#"><?php echo __('Quantum computing', 'picostrap5-child-base'); ?></a></li>
                                <li><a hreflang="<?php echo esc_attr($pll); ?>" href="#"><?php echo __('Robotic process automation', 'picostrap5-child-base'); ?></a></li>
                                <li><a hreflang="<?php echo esc_attr($pll); ?>" href="#"><?php echo __('Search engines', 'picostrap5-child-base'); ?></a></li>
                                <li><a hreflang="<?php echo esc_attr($pll); ?>" href="#"><?php echo __('Signal processing', 'picostrap5-child-base'); ?></a></li>
                                <li><a hreflang="<?php echo esc_attr($pll); ?>" href="#"><?php echo __('Software applications and systems, mobile applications user interfaces', 'picostrap5-child-base'); ?></a></li>
                            <?php
                            }
                            ?>

                            <?php
                            /*
                            $sub_sectors = wp_get_post_terms(get_the_ID(), 'sub_sector', array('orderby' => 'name', 'order' => 'ASC'));
                            if (get_field('area_of_focus_content')) {
                                echo get_field('area_of_focus_content');
                            } else if (!empty($sub_sectors)) {

                                foreach ($sub_sectors as $key => $value) {
                            ?>

                                    <li><a href="<?php echo get_term_link($value); ?>"><?php echo $value->name; ?></a></li>
                                    <?php
                                }
                            } else {

                                $sub_sectors = get_posts(['post_type' => 'sector', 'posts_per_page' => '-1', 'post_parent' => get_the_ID(), 'orderby' => 'name', 'order' => 'ASC']);
                                if (!empty($sub_sectors)) {
                                    foreach ($sub_sectors as $key => $value) {
                                    ?>
                                        <!-- <li><a href="javascript:;"><?php echo $value->post_title; ?></a></li> -->
                                        <li><?php echo $value->post_title; ?></li>
                                    <?php
                                    }
                                } else {
                                    ?>
                                    <li><a href="#"><?php echo __('AI', 'picostrap5-child-base'); ?></a></li>
                                    <li><a href="#"><?php echo __('Bioinformatics', 'picostrap5-child-base'); ?></a></li>
                                    <li><a href="#"><?php echo __('Blockchain and distributed ledgers', 'picostrap5-child-base'); ?></a></li>
                                    <li><a href="#"><?php echo __('Communications and network', 'picostrap5-child-base'); ?></a></li>
                                    <li><a href="#"><?php echo __('Computer games', 'picostrap5-child-base'); ?></a></li>
                                    <li><a href="#"><?php echo __('Data and software security, cryptography and digital rights management (DRM)', 'picostrap5-child-base'); ?></a></li>
                                    <li><a href="#"><?php echo __('Data management and storage, databases and data compression', 'picostrap5-child-base'); ?></a></li>
                                    <li><a href="#"><?php echo __('Digital assistants, virtual assistants and software agents', 'picostrap5-child-base'); ?></a></li>
                                    <li><a href="#"><?php echo __('Fintech and adtech', 'picostrap5-child-base'); ?></a></li>
                                    <li><a href="#"><?php echo __('Machine vision', 'picostrap5-child-base'); ?></a></li>
                                    <li><a href="#"><?php echo __('Metaverse virtual reality (VR) and augmented reality (AR)', 'picostrap5-child-base'); ?></a></li>
                                    <li><a href="#"><?php echo __('Motor capture', 'picostrap5-child-base'); ?></a></li>
                                    <li><a href="#"><?php echo __('Multimedia, audio/video processing and animation', 'picostrap5-child-base'); ?></a></li>
                                    <li><a href="#"><?php echo __('Natural language processing', 'picostrap5-child-base'); ?></a></li>
                                    <li><a href="#"><?php echo __('Quantum computing', 'picostrap5-child-base'); ?></a></li>
                                    <li><a href="#"><?php echo __('Robotic process automation', 'picostrap5-child-base'); ?></a></li>
                                    <li><a href="#"><?php echo __('Search engines', 'picostrap5-child-base'); ?></a></li>
                                    <li><a href="#"><?php echo __('Signal processing', 'picostrap5-child-base'); ?></a></li>
                                    <li><a href="#"><?php echo __('Software applications and systems, mobile applications user interfaces', 'picostrap5-child-base'); ?></a></li>
                            <?php
                                }
                            } */
                            ?>
                        </ul>
                    <?php
                    }
                    ?>
                </div>

                <div class="d-none mbl-open">
                    <?php
                    the_content();
                    echo get_field('the_team');
                    ?>
                </div>
                <!-- <div class="about-people-card">
                        <div  class="quote-box">
                            <div class="quote-box-inner">
                                <?php // echo get_field('quotes'); 
                                ?>
                            </div>
                        </div>
                    </div> -->



                <!-- ========== recent-highlightsection start ============= -->

                <div class="recent-highlight pt-100 <?php echo get_field('quotes') || get_field('recent_highlights') ? '' : 'd-none'; ?>">
                    <div class="container-one">
                        <div class="row">
                            <div class="col-lg-12">
                                <div class="recent-highlight-quotes">
                                    <div class="box-with-border-inner"></div>
                                    <div class="box-inner-content">
                                        <?php echo get_field('quotes'); ?>
                                        <?php
                                        $quote_person_image = get_field('quote_person_image');
                                        $person_image = get_stylesheet_directory_uri() . '/assets/images/team/team.jpg';
                                        if ($quote_person_image) {
                                            $roles = get_the_terms($quote_person_image->ID, 'people_role');
                                            $roles = join(', ', wp_list_pluck($roles, 'name'));

                                            $person_image = get_field('person_photo_2', $quote_person_image->ID) ? get_field('person_photo_2', $quote_person_image->ID)['sizes']['medium_large'] : get_stylesheet_directory_uri() . '/assets/images/team/team.jpg';
                                        ?>
                                            <div class="team-content quote-inner">
                                                <h6><a hreflang="<?php echo esc_attr($pll); ?>" href="<?php echo get_the_permalink($quote_person_image); ?>"><?php echo get_the_title($quote_person_image); ?></a></h6>
                                                <span><?php echo strtoupper($roles); ?></span>
                                            </div>
                                        <?php
                                        }
                                        ?>
                                    </div>
                                    <div class="box-inner-image">
                                        <img src="<?php echo $person_image; ?>" alt="image">
                                    </div>
                                </div>
                                <!-- <div class="section-title-one style-yellow">
                                        <div class="box-with-border p-3" >
                                            <?php // echo get_field('quotes'); 
                                            ?>
                                        </div>
                                    </div> -->


                            </div>
                            <!-- <div class="col-lg-6">
                                    <div >
                                        
                                        
                                    </div>
                                </div> -->
                        </div>
                    </div>
                </div>

                <!-- ========== recent-highlight section end ============= -->
                <?php //if(get_post_field('post_name') == 'trade-marks' || get_post_field('post_name') == 'aerospace'){ 
                ?>
                <div class="recruitment-process-quotes mt-50 <?php echo get_field('case_studies') ? '' : 'd-none'; ?>">
                    <?php
                    foreach ((array)get_field('case_studies') as $case_study) {
                        if (!isset($case_study['title'])) {
                            $case_study['content'] = $case_study;
                            $case_study['title'] = '';
                        }
                    ?>
                        <div class="recruitment-process-quotes-single mb-30">
                            <div class="section-title-one style-pink">
                                <h2><?php echo $case_study['title']; ?></h2>
                            </div>
                            <div class="quote-content">
                                <?php echo $case_study['content']; ?>
                            </div>
                            <div class="section-title-one style-pink">
                                <h2></h2>
                            </div>
                        </div>
                    <?php
                    }
                    ?>
                </div>
                <?php //} 
                ?>
                
                <div class="business-card-section1 sectors">
                    <div class="business-card-box  m-hide">
                        <div class="business-card-box-content">
                            <div><strong><?php echo "Sign up for our <span>" . get_the_title() . "</span> insights <a hreflang='<?php echo esc_attr($pll); ?>' href='" . get_the_permalink(10520) . "'>here</a>"; ?></strong></div>
                            <?php // echo get_field('download_box_content'); 
                            ?>
                        </div>
                    </div>
                </div>
                <?php
                $pdf_box_area = get_field('pdf_box_area');
                ?>
                <div class="pdf-flip-area ">
                    <?php if (!empty($pdf_box_area['box_title'])): ?>
                        <h6><?php echo $pdf_box_area['box_title'] ?></h6>
                    <?php endif; ?>
                    <div class="row g-4" style="padding-top: 20px;">
                        <?php
                        if (!empty($pdf_box_area['box_list'])) {
                            foreach ($pdf_box_area['box_list'] as $value) {
                        ?>
                                <div class="col-md-12">
                                    <div class="ecpertise-img-cont sectors">
                                        <?php if (!empty($value['title'])): ?>
                                        <h6><?php echo $value['title']; ?></h6>
                                        <?php endif; ?>
                                        <a hreflang="<?php echo esc_attr($pll); ?>" target="_blank" href="<?php echo $value['link']; ?>"><img src="<?php echo $value['image']; ?>" alt="<?php echo $value['title']; ?>"></a>
                                    </div>
                                </div>
                        <?php
                            }
                        }
                        ?>
                    </div>
                </div>

            </div>
            <div class="col-lg-6">
                <div class="software-content">
                    <!-- <h6 >The <?php echo trim(get_the_title()); ?> Team</h6> -->
                    <div class="mbl-hdn">
                        <?php
                        the_content();
                        echo get_field('the_team');
                        ?>
                    </div>
                    <!-- <h6  class="<?php echo get_field('what_sets_us_apart') ? '' : 'd-none'; ?>"><?php echo __('What sets us apart?', 'picostrap5-child-base'); ?></h6> -->
                    <div class="<?php echo get_field('what_sets_us_apart') ? '' : 'd-none'; ?>">
                        <?php
                        echo get_field('what_sets_us_apart');
                        ?>

                    </div>
                    <!-- <h6  class="<?php echo get_field('case_studies') ? '' : 'd-none'; ?>"><?php echo __('Case Studies', 'picostrap5-child-base'); ?></h6> -->
                    <!-- <div class="recruitment-process-quotes mt-50 <?php echo get_field('case_studies') ? '' : 'd-none'; ?>"> -->
                    <!-- <div class="recruitment-process-quotes-single mb-30" > -->
                    <?php
                    // echo get_field('case_studies');
                    ?>
                    <!-- </div> -->
                    <!-- </div> -->

                    <?php /* if(get_post_field('post_name') != 'aerospace'){ ?>
                        <div class="recruitment-process-quotes mt-50 <?php echo get_field('case_studies') ? '' : 'd-none'; ?>" >
                            <?php
                                if(get_post_field('post_name') != 'trade-marks'){
                                    foreach((array)get_field('case_studies') as $case_study){
                                        if(!isset($case_study['title'])){
                                            $case_study['content'] = $case_study;
                                            $case_study['title'] = '';
                                        }
                                        ?>
                                            <div class="recruitment-process-quotes-single mb-30">
                                                <div class="section-title-one style-pink">
                                                    <h2><?php echo $case_study['title']; ?></h2>
                                                </div>
                                                <div class="quote-content">
                                                    <?php echo $case_study['content']; ?>
                                                </div>
                                                <div class="section-title-one style-pink">
                                                    <h2></h2>
                                                </div>
                                            </div>
                                        <?php 
                                    }
                                }
                            ?>
                            <?php }  ?>
                        </div> */ ?>
                </div>

                <div class="business-card-section1">
                    <?php if (get_the_ID() == 1360) { ?>
                        <div class="recent-highlight-inner <?php echo get_field('recent_highlights') ? '' : 'd-none'; ?>">
                            <h2><?php echo __('Recent highlights', 'picostrap5-child-base'); ?><span class="border-bar"></span></h2>
                            <div>

                                <?php echo get_field('recent_highlights'); ?>
                            </div>
                        </div>
                    <?php } ?>
                    <?php if (get_the_ID() != 1360) { ?>
                        <div class="recent-highlight-inner <?php echo get_field('recent_highlights') ? '' : 'd-none'; ?>">
                            <h2><?php echo __('Recent highlights', 'picostrap5-child-base'); ?><span class="border-bar"></span></h2>
                            <div>

                                <?php
                                echo get_field('recent_highlights');
                                ?>
                            </div>
                        </div>
                    <?php } ?>
                    <?php
                    // if(get_field('download_box_content')){
                    ?>

                    <?php
                    // }
                    ?>
                </div>

                <!-- <div class="blue-box">
                    </div> -->
            </div>
        </div>
    </div>
</div>

<!-- ========== software-section end============= -->

<!-- ========== team section start ============= -->

<?php
$partners_list = get_field('partners_list');
if (!$partners_list) {
    $partners_list = 'auto';
}

$partners = get_field('partners');

?>
<!-- <div class="team-section mt-30 mt-1000 <?php echo $post->post_name; ?> <?php echo $partners_list != 'auto' && empty($partners) ? 'd-none' : ''; ?>">
    <div class="container-one">
        <div class="section-title-one style-green pl-container mb-4">
            <?php
            if (pll_current_language() == 'es') {
            ?>
                <h2><?php echo __('team', 'picostrap5-child-base') . ' ' . trim(get_the_title()); // __('Partners', 'picostrap5-child-base'); 
                    ?></h2>
            <?php
            } else {
            ?>
                <h2><?php echo trim(get_the_title()) . ' ' . __('team', 'picostrap5-child-base'); // __('Partners', 'picostrap5-child-base'); 
                    ?></h2>
            <?php
            }
            ?>
        </div>
        <?php

        if ($partners_list == 'auto') {
            $current_sector_id = get_the_ID();

            $terms = get_terms(array(
                'taxonomy'   => 'people_role',
                'taxonomy'   => 'group_listing',
                'hide_empty' => true,
                'orderby' => 'name',
                'orderby' => 'count',
                'orderby' => 'meta_value',
                'meta_key' => 'order',
                'order' => 'DESC',
                'order' => 'ASC',
                'number' => 0,
                'fields' => 'all',

            ));

            if (!empty($terms)) {
                $num = 1;
                foreach ($terms as $t) {
                    $args = array(
                        'post_type' => 'people',
                        'posts_per_page' => '-1',
                        'orderby' => 'title',
                        'orderby' => 'meta_value',
                        'meta_key' => 'last_name',
                        'order' => 'ASC',
                        'post_status' => 'publish',

                        'meta_query' => array(
                            array(
                                'key'     => 'sectors',
                                'value'   => sprintf(':"%s";', $current_sector_id),
                                'compare' => 'LIKE',
                            )
                        ),

                        'tax_query' => array(
                            array(
                                'taxonomy' => 'people_role',
                                'taxonomy' => 'group_listing',
                                'field' => 'slug',
                                'terms' => array($t->slug),
                                'operator' => 'IN',
                            ),
                        ),
                    );

                    $q = new WP_Query($args);

        ?>
                    <div class="section-title-two <?php echo $q->have_posts() ? '' : 'd-none'; ?> <?php echo 'srt' . $num; ?>">
                        <h4><?php echo $t->name; // __('Partners', 'picostrap5-child-base'); 
                            ?></h4>
                    </div>
                    <div class="row row-cols-xl-4 row-cols-lg-4 row-cols-md-3 row-cols-sm-2 row-cols-2 g-4 mb-30 <?php echo $q->have_posts() ? '' : 'd-none'; ?> <?php echo 'srt' . $num; ?>">
                        <?php
                        if ($q->have_posts()) {
                            while ($q->have_posts()) {
                                $q->the_post();

                                $roles = get_the_terms(get_the_ID(), 'people_role');
                                $roles = join(', ', wp_list_pluck($roles, 'name'));

                                $locations = get_the_terms(get_the_ID(), 'location');
                                $locations = join(', ', wp_list_pluck($locations, 'name'));
                        ?>
                                <div class="col">
                                    <div class="team-item style-two style-two">
                                        <div class="team-image">
                                            <a href="<?php echo get_the_permalink(); ?>?referer=sector"><img src="<?php echo get_field('person_photo_2') ? get_field('person_photo_2')['sizes']['medium_large'] : get_stylesheet_directory_uri() . '/assets/images/team/team.jpg'; ?>" alt="image"></a>
                                        </div>
                                        <div class="team-content">
                                            <h6><a href="<?php echo get_the_permalink(); ?>?referer=sector"><?php echo get_the_title(); ?></a></h6>
                                            <span><?php echo $roles; ?> </span>
                                            <span><?php echo $locations; ?> </span>
                                        </div>
                                    </div>
                                </div>
                        <?php
                            }
                        }
                        ?>
                    </div>
                    <?php
                    $num++;
                }
            }

            wp_reset_query();
        } else {

            if (!empty($partners)) {

                $partner_ids = wp_list_pluck($partners, 'ID');

                $terms = get_terms(array(
                    'taxonomy'   => 'people_role',
                    'taxonomy'   => 'group_listing',
                    'hide_empty' => true,
                    'orderby' => 'name',
                    'orderby' => 'count',
                    'orderby' => 'meta_value',
                    'meta_key' => 'order',
                    'order' => 'DESC',
                    'order' => 'ASC',
                    'number' => 0,
                    'fields' => 'all',

                ));

                if (!empty($terms)) {
                    foreach ($terms as $t) {

                        $args = array(
                            'post_type' => 'people',
                            'posts_per_page' => '-1',
                            'orderby' => 'title',
                            'orderby' => 'meta_value',
                            'meta_key' => 'last_name',
                            'order' => 'ASC',
                            'post_status' => 'publish',
                            'post__in' => $partner_ids,

                            'tax_query' => array(
                                array(
                                    'taxonomy' => 'people_role',
                                    'taxonomy' => 'group_listing',
                                    'field' => 'slug',
                                    'terms' => array($t->slug),
                                    'operator' => 'IN',
                                ),
                            ),
                        );
                        $partners = get_posts($args);

                        if (!$partners) {
                            continue;
                        }

                    ?>
                        <div class="section-title-two">
                            <h4><?php echo $t->name; // __('Partners', 'picostrap5-child-base'); 
                                ?></h4>
                        </div>
                        <div class="row row-cols-xl-4 row-cols-lg-4 row-cols-md-3 row-cols-sm-2 row-cols-2 g-4 mb-30">
                            <?php
                            foreach ($partners as $value) {
                                $pid = $value->ID;

                                $roles = get_the_terms($pid, 'people_role');
                                $roles = join(', ', wp_list_pluck($roles, 'name'));

                                $locations = get_the_terms($pid, 'location');
                                $locations = join(', ', wp_list_pluck($locations, 'name'));
                            ?>
                                <div class="col">
                                    <div class="team-item style-two style-two">
                                        <div class="team-image">
                                            <a href="<?php echo get_the_permalink($pid); ?>?referer=sector"><img src="<?php echo get_field('person_photo_2', $pid) ? get_field('person_photo_2', $pid)['sizes']['medium_large'] : get_stylesheet_directory_uri() . '/assets/images/team/team.jpg'; ?>" alt="image"></a>
                                        </div>
                                        <div class="team-content">
                                            <h6><a href="<?php echo get_the_permalink($pid); ?>?referer=sector"><?php echo get_the_title($pid); ?></a></h6>
                                            <span><?php echo $roles; ?> </span>
                                            <span><?php echo $locations; ?> </span>
                                        </div>
                                    </div>
                                </div>
                            <?php
                            }
                            ?>
                        </div>
        <?php
                    }
                }
            }
        }
        wp_reset_query();

        ?>
    </div>
</div>

<?php //echo get_field('partners_html') 
?> -->

<!-- ========== team section start ============= -->
<?php
$the_team = get_field('the_team_list');

//AI Page 
$aiParam = '';
if (get_the_ID() == 1360 || get_the_ID() == 2520 || get_the_ID() == 8754) {
    $aiParam = '&ref=ai';
}

$current_slug = get_post_field('post_name', get_post());
$select_partner         = [];
$senior_associate       = [];
$attorney               = [];
$assistants_and_trainee = [];

if( $current_slug != 'trade-marks' ) {
    $posts = get_posts([
        'name'        => 'trade-marks',
        'post_type'   => 'sector',
        'post_status' => 'publish',
        'numberposts' => 1,
    ]);

    if( !empty( $posts[0]->ID ) ) {
        $trade_team = get_field('the_team_list',$posts[0]->ID);
    }
    // Ignore only for 'select_partners' 
    if( !empty( $trade_team['select_partners'] ) ) {
        $select_partners = $trade_team['select_partners'];
        $select_partner = array_map(function ($single_parterns) {
            return $single_parterns->ID;
        }, $select_partners);
    }
    // Ignore only for 'senior_associates' 
    if( !empty( $trade_team['senior_associates'] ) ) {
        $senior_associates = $trade_team['senior_associates'];
        $senior_associate = array_map(function ($single_parterns) {
            return $single_parterns->ID;
        }, $senior_associates);
    }
    // Ignore only for 'attorneys' 
    if( !empty( $trade_team['attorneys'] ) ) {
        $attorneys = $trade_team['attorneys'];
        $attorney = array_map(function ($single_attorney) {
            return $single_attorney->ID;
        }, $attorneys);
    }
    // Ignore only for 'assistants_and_trainees' 
    if( !empty( $trade_team['assistants_and_trainees'] ) ) {
        $assistants_and_trainees = $trade_team['assistants_and_trainees'];
        $assistants_and_trainee = array_map(function ($single_assis) {
            return $single_assis->ID;
        }, $assistants_and_trainees);
    }

}

?>
<div class="team-section position-relative mt-100 <?php echo empty($the_team['select_partners']) && empty($the_team['senior_associates']) ? 'd-none' : ''; ?>">
    <div class="box-design-14">
    </div>
    <div class="container-one">
        <div class="section-title-one mb-30 style-green">
            <h2><?php echo $the_team['title']; ?></h2>
        </div>
        <div class="section-title-two <?php echo !empty($the_team['select_partners']) ? '' : 'd-none'; ?>">
            <h4><?php echo __('Partners', 'picostrap5-child-base'); ?></h4>
        </div>

        <div class="row row-cols-xl-4 row-cols-lg-4 row-cols-md-3 row-cols-sm-2 row-cols-2 g-lg-4 g-3 mb-30 <?php echo !empty($the_team['select_partners']) ? '' : 'd-none'; ?>">
            <?php
            if (!empty($the_team['select_partners'])) :
                foreach ($the_team['select_partners'] as $value) :
                if( count($select_partner) > 0 && in_array( $value->ID, $select_partner ) ) {
                    continue;
                }
            ?>
                    <div class="col">
                        <div class="team-item style-two style-two">
                            <?php
                            $thumImg = '';
                            $upc_image = get_field('upc_image', $value->ID);
                            $thum_img = get_field('person_photo_2', $value->ID);
                            if (!empty($upc_image)) {
                                $thumImg = $upc_image;
                            } else {
                                $thumImg = $thum_img['url'];
                            }


                            ?>
                            <div class="team-image">
                                <a hreflang="<?php echo esc_attr($pll); ?>" href="<?php echo get_the_permalink($value->ID); ?>?referer=sector<?php echo $aiParam; ?>">
                                    <?php if (!empty($thumImg)) : ?>
                                        <img src="<?php echo $thumImg; ?>" alt="image">
                                    <?php endif; ?>
                                </a>
                            </div>

                            <?php
                            $role =  get_the_terms($value->ID, 'people_role');
                            $location =  get_the_terms($value->ID, 'location');

                            ?>
                            <div class="team-content">
                                <h6><a hreflang="<?php echo esc_attr($pll); ?>" href="<?php echo get_the_permalink($value->ID); ?> ?referer=sector<?php echo $aiParam; ?>"><?php echo get_the_title($value->ID); ?></a></h6>
                                <span>
                                    <?php
                                    if (!empty($role)) {
                                        echo $role[0]->name;
                                    }
                                    ?>
                                </span>
                                <span>
                                    <?php
                                    if (!empty($location)) {
                                        echo $location[0]->name;
                                    }
                                    ?>
                                </span>
                            </div>
                        </div>
                    </div>
            <?php
                endforeach;
            endif;
            ?>
        </div>

        <div class="section-title-two <?php echo !empty($the_team['senior_associates']) ? '' : 'd-none'; ?>">
            <h4><?php echo __('Senior associates', 'picostrap5-child-base'); ?></h4>
        </div>
        <div class="row row-cols-xl-4 row-cols-lg-4 row-cols-md-3 row-cols-sm-2 row-cols-2 g-lg-4 g-3 mb-30 <?php echo !empty($the_team['senior_associates']) ? '' : 'd-none'; ?>">
            <?php
            if (!empty($the_team['senior_associates'])) :
                foreach ($the_team['senior_associates'] as $value) :
                if( count($senior_associate) > 0 && in_array( $value->ID, $senior_associate ) ) {
                    continue;
                }
            ?>
                    <div class="col">
                        <div class="team-item style-two style-two">
                            <?php
                            $thum_img = get_field('person_photo_2', $value->ID);
                            ?>
                            <div class="team-image">
                                <a href="<?php echo get_the_permalink($value->ID); ?>?referer=sector<?php echo $aiParam; ?>">
                                    <?php if (!empty($thum_img)) : ?>
                                        <img src="<?php echo $thum_img['url']; ?>" alt="image">
                                    <?php endif; ?>
                                </a>
                            </div>
                            <?php
                            $role =  get_the_terms($value->ID, 'people_role');
                            $location =  get_the_terms($value->ID, 'location');

                            ?>
                            <div class="team-content">
                                <h6><a hreflang="<?php echo esc_attr($pll); ?>" href="<?php echo get_the_permalink($value->ID); ?>?referer=sector<?php echo $aiParam; ?>"><?php echo get_the_title($value->ID); ?></a></h6>
                                <span>
                                    <?php
                                    if (!empty($role)) {
                                        echo $role[0]->name;
                                    }
                                    ?>
                                </span>
                                <span>
                                    <?php
                                    if (!empty($location)) {
                                        echo $location[0]->name;
                                    }
                                    ?>
                                </span>
                            </div>
                        </div>
                    </div>
            <?php
                endforeach;
            endif;
            ?>
        </div>
        <div class="section-title-two <?php echo !empty($the_team['attorneys']) ? '' : 'd-none'; ?>">
            <h4><?php echo __('Attorneys', 'picostrap5-child-base'); ?></h4>
        </div>
        <div class="row row-cols-xl-4 row-cols-lg-4 row-cols-md-3 row-cols-sm-2 row-cols-2 g-lg-4 g-3 mb-60 <?php echo !empty($the_team['attorneys']) ? '' : 'd-none'; ?>">
            <?php
            if (!empty($the_team['attorneys'])) :
                foreach ($the_team['attorneys'] as $value) :
                if( count($attorney) > 0 && in_array( $value->ID, $attorney ) ) {
                    continue;
                }
            ?>
                    <div class="col">
                        <div class="team-item style-two style-two">
                            <?php
                            $thum_img = get_field('person_photo_2', $value->ID);
                            ?>
                            <div class="team-image">
                                <a href="<?php echo get_the_permalink($value->ID); ?>?referer=sector<?php echo $aiParam; ?>">
                                    <?php if (!empty($thum_img)) : ?>
                                        <img src="<?php echo $thum_img['url']; ?>" alt="image">
                                    <?php endif; ?>
                                </a>
                            </div>
                            <?php
                            $role =  get_the_terms($value->ID, 'people_role');
                            $location =  get_the_terms($value->ID, 'location');

                            ?>
                            <div class="team-content">
                                <h6><a hreflang="<?php echo esc_attr($pll); ?>" href="<?php echo get_the_permalink($value->ID); ?>?referer=sector<?php echo $aiParam; ?>"><?php echo get_the_title($value->ID); ?></a></h6>
                                <span>
                                    <?php
                                    if (!empty($role)) {
                                        echo $role[0]->name;
                                    }
                                    ?>
                                </span>
                                <span>
                                    <?php
                                    if (!empty($location)) {
                                        echo $location[0]->name;
                                    }
                                    ?>
                                </span>
                            </div>
                        </div>
                    </div>
            <?php
                endforeach;
            endif;
            ?>
        </div>
        <div class="section-title-two <?php echo !empty($the_team['assistants_and_trainees']) ? '' : 'd-none'; ?>">
            <h4><?php echo __('Assistants and trainees', 'picostrap5-child-base'); ?></h4>
        </div>
        <div class="row row-cols-xl-4 row-cols-lg-4 row-cols-md-3 row-cols-sm-2 row-cols-2 g-lg-4 g-3 mb-60 <?php echo !empty($the_team['attorneys']) ? '' : 'd-none'; ?>">
            <?php
            if (!empty($the_team['assistants_and_trainees'])) :
                foreach ($the_team['assistants_and_trainees'] as $value) :
                if( count($assistants_and_trainee) > 0 && in_array( $value->ID, $assistants_and_trainee ) ) {
                    continue;
                }
            ?>
                    <div class="col">
                        <div class="team-item style-two style-two">
                            <?php
                            $thum_img = get_field('person_photo_2', $value->ID);
                            ?>
                            <div class="team-image">
                                <a href="<?php echo get_the_permalink($value->ID); ?>?referer=sector<?php echo $aiParam; ?>">
                                    <?php if (!empty($thum_img)) : ?>
                                        <img src="<?php echo $thum_img['url']; ?>" alt="image">
                                    <?php endif; ?>
                                </a>
                            </div>
                            <?php
                            $role =  get_the_terms($value->ID, 'people_role');
                            $location =  get_the_terms($value->ID, 'location');

                            ?>
                            <div class="team-content">
                                <h6><a hreflang="<?php echo esc_attr($pll); ?>" href="<?php echo get_the_permalink($value->ID); ?>?referer=sector<?php echo $aiParam; ?>"><?php echo get_the_title($value->ID); ?></a></h6>
                                <span>
                                    <?php
                                    if (!empty($role)) {
                                        echo $role[0]->name;
                                    }
                                    ?>
                                </span>
                                <span>
                                    <?php
                                    if (!empty($location)) {
                                        echo $location[0]->name;
                                    }
                                    ?>
                                </span>
                            </div>
                        </div>
                    </div>
            <?php
                endforeach;
            endif;
            ?>
        </div>
    </div>
</div>

<!-- ========== team section end ============= -->

</div>

<!-- ========== team section end ============= -->

<!-- ========== recent-highlightsection start ============= -->

<!-- <div class="recent-highlight pt-100 <?php echo get_field('quotes') || get_field('recent_highlights') ? '' : 'd-none'; ?>">
        <div class="container-one">
            <div class="row">
                <div class="col-lg-6">
                    <div class="section-title-one style-yellow">
                        <h2 ><?php echo __('Recent highlights', 'picostrap5-child-base'); ?></h2>
                        <div class="box-with-border p-3" >
                            <?php // echo get_field('quotes'); 
                            ?>
                        </div>
                    </div>
                </div>
                <div class="col-lg-6">
                    <div >
                        <?php
                        // echo get_field('recent_highlights');
                        ?>
                        
                    </div>
                </div>
            </div>
        </div>
    </div> -->

<!-- ========== recent-highlight section end ============= -->


<!-- ========== service section start ============= -->

<style>
    .insight-item-new .image {
        position: relative;
    }

    .insight-item-new .image h6 {
        position: absolute;
        left: 0;
        top: 0;
        margin-bottom: 0;
    }

    .insight-item-new .image h6 a {
        font-size: 13px;
        font-weight: 600;
        color: #ffffff;
        display: block;
        padding: 5px 12px;
        border-radius: 0 100px 100px 0;
        background: var(--primary-blue);
    }
   
</style>
<?php
$bulletins = get_field('bulletins');
$auto_list_of_insights = get_field('auto_list_of_insights');
$auto_list_of_insights = $auto_list_of_insights === NULL || $auto_list_of_insights ? true : false;

// Initialize arrays to avoid null errors
$bulletin_posts = [];
$latest_posts = [];

if (empty($auto_list_of_insights)) {
    if (!empty($bulletins)) {
        $bulletins_count = count($bulletins);
        $bulletinsId = [];

        foreach ($bulletins as $value) {
            $bulletinsId[] = $value->ID;
        }

        // Get the selected bulletin posts
        $args = array(
            'post_type' => array('bulletin', 'news', 'upc_news'),
            'post__in' => $bulletinsId,
            'posts_per_page' => $bulletins_count,
            'orderby' => 'post__in', // Keep the order of selected posts
            'post_status' => 'publish',
        );

        $bulletin_query = new WP_Query($args);

        // Store selected posts in $bulletin_posts array if there are any
        if ($bulletin_query->have_posts()) {
            $bulletin_posts = $bulletin_query->posts;
        }

        // If selected posts are fewer than 4, fetch the latest posts to fill up
        if ($bulletins_count < 4) {
            $remaining_posts = 4 - $bulletins_count;

            $latest_args = array(
                'post_type' => array('bulletin', 'news', 'upc_news'),
                'posts_per_page' => $remaining_posts,
                'post__not_in' => $bulletinsId, // Exclude already selected posts
                'orderby' => 'date',
                'order' => 'DESC',
                'post_status' => 'publish',
            );

            $latest_query = new WP_Query($latest_args);

            // Store the latest posts in $latest_posts array if there are any
            if ($latest_query->have_posts()) {
                $latest_posts = $latest_query->posts;
            }
        }
    }
} else {
    // If no bulletins are selected, show the latest posts, limit to 4
    $args = array(
        'post_type' => array('bulletin', 'news', 'upc_news'),
        'posts_per_page' => 4,
        'orderby' => 'date',
        'order' => 'DESC',
        'post_status' => 'publish',
    );
    $query = new WP_Query($args);

    // Store the latest posts if available
    if ($query->have_posts()) {
        $latest_posts = $query->posts;
    }
}

// Merge selected and latest posts if needed
$posts = array_merge($bulletin_posts, $latest_posts);
?>

<div class="insights-section pt-30 <?php echo $bulletins || ($auto_list_of_insights && !empty($posts)) ? '' : 'd-none'; ?>">
    <div class="container-one">
        <div class="section-title-one style-red">
            <h2><?php echo __('Insights', 'picostrap5-child-base'); ?> </h2>
        </div>
        <div class="row justify-content-centerr mb-40 gy-4">
            <?php
            // Check if there are posts in the $posts array
            if (!empty($posts)) {
                foreach ($posts as $post) {
                    setup_postdata($post);
                    $post_type = get_post_type();
                    $post_type_obj = get_post_type_object($post_type);
                    $singular_name = $post_type_obj ? $post_type_obj->labels->singular_name : '';

                    if ($post_type === 'bulletin') {
                        $singular_name = 'IP updates';
                    }
                    $excerpt = get_the_excerpt($post);
                    $permalink = '';

                    if ($pll == 'de') {
                        if ($post_type == 'bulletin') {
                            $permalink = str_replace("/de/bulletin/", "/bulletin/", get_permalink());
                        } elseif ($post_type == 'news') {
                            $permalink = str_replace("/de/news/", "/news/", get_permalink());
                        }
                    } else {
                        $permalink = get_permalink();
                    }
            ?>
                    <div class="col-lg-3 col-md-6 col-sm-6 col-12">
                        <div class="insight-item insight-item-new style-black">
                            <div class="image">
                                <?php if ('upc_news' == $post_type) : ?>
                                    <?php echo get_the_post_thumbnail(get_the_ID()) ?>
                                <?php else : ?>
                                    <img src="<?php echo get_the_post_thumbnail_url(get_the_ID(), 'full'); ?>" alt="image">
                                <?php endif ?>
                                 <h6><a class="insight-title" href="<?php echo $permalink; ?>"><?php echo __($singular_name, 'picostrap5-child-base'); ?></a></h6>
                            </div>
                            <div class="content">
                                <?php if ('upc_news' == $post_type) : ?>
                                    <h6><a class="insight-title" href="<?php echo get_the_permalink(); ?>"><?php echo get_the_title(get_the_ID()); ?></a></h6>
                                    <p><?php echo $excerpt; ?></p>
                                <?php else : ?>
                                    <h6><a class="insight-title" href="<?php echo $permalink; ?>"><?php echo get_the_title(get_the_ID()); ?></a></h6>
                                    <p><?php echo $excerpt; ?></p>
                                <?php endif ?>
                            </div>
                        </div>
                    </div>
            <?php
                }
                wp_reset_postdata();
            }
            ?>
        </div>
        <a hreflang="<?php echo esc_attr($pll); ?>" target="_blank" href="<?php echo home_url('insights'); ?>" class="eg-btn btn--primary-red btn--lg2"><?php echo __('INSIGHTS', 'picostrap5-child-base'); ?><i class="bi bi-arrow-right"></i></a>
    </div>
</div>



<!-- ========== service section end ============= -->

<!-- ========== sector section start ============= -->

<?php $other_sectors = get_field('other_sectors'); ?>

<div class="sector-section pt-100 bg-light-box pb-100 overflow-hidden <?php echo $other_sectors ? '' : 'd-none'; ?>">
    <div class="container-one">
        <div class="section-title-one style-yellow">
            <h2><?php echo __('Related sectors', 'picostrap5-child-base'); ?></h2>
        </div>
        <div class="row justify-content-lg-start justify-content-center g-md-4 g-2">
            <?php
            if ($other_sectors) {
                foreach ($other_sectors as $value) {
            ?>
                    <div class="col-lg-3 col-md-6 col-sm-6 col-6">
                        <a hreflang="<?php echo esc_attr($pll); ?>" href="<?php echo get_the_permalink($value); ?>">
                            <div class="sector-item style-yellow">
                                <h6 class="title"><?php echo get_the_title($value); ?></h6>
                                <p><?php echo get_the_excerpt($value); ?></p>
                                <div class="arrow-btn">
                                    <img src="<?php echo get_stylesheet_directory_uri(); ?>/assets/images/icons/arrow-white.svg" alt="arrow">
                                </div>
                            </div>
                        </a>
                    </div>
            <?php
                }
            }
            ?>
        </div>
    </div>
</div>

<!-- ========== sector section start ============= -->


<?php get_footer();
