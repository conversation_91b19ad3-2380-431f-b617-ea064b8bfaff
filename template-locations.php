<?php 
/*
    Template Name: Locations Page
*/

// Exit if accessed directly.
defined( 'ABSPATH' ) || exit;

get_header();

?>

<!-- ========== banner-section start============= -->

    <div class="banner-section position-relative">
        <div class="container-fluid px-0">
            <div class="swiper banner-slider">
                <div class="swiper-wrapper">
                    <?php 
                        $banner = get_field('banner');
                        if(!empty($banner)){
                            foreach ($banner as $value) {
                                ?>
                                    <div class="swiper-slide" style="background-image: url('<?php echo $value['image']['url']; ?>');background-size: cover;">
                                        <div class="banner-slider-content style-dark">
                                            <h1 ><?php echo $value['title']; ?></h1>
                                        </div>
                                    </div>
                                <?php 
                            }
                        }
                    ?>
                </div>
            </div>
        </div>
        <div class="banner-pagination d-flex justify-content-end align-items-end"></div>
    </div>

    <!-- ========== banner-section end============= -->

    <!-- ========== page-content-section start============= -->

    <div class="content-section pl-container pt-100">
        <div class="container-fluid">
            <div class="row">
                <div class="col-lg-5">
                    <div class="section-title-one style-red">
                        <h2 ><?php echo get_field('after_banner_title'); ?></h2>
                    </div>
                    <p class="mb-0" ><?php echo get_field('after_banner_content'); ?> </p>
                </div>
                <div class="col-lg-7">
                    <div class="box-design-16"> </div>
                </div>
            </div>
        </div>
    </div>
    <div class="location-card-section pb-100 pt-100">
        <div class="container-one">
            <div class="row g-lg-4 g-3">
                <?php 
                    $location_countries = get_field('location_countries');
                    if(!empty($location_countries)){
                        foreach ($location_countries as $value) {

                            ?>
                                <div class="col-lg-4 col-md-6 col-sm-6 col-12" >
                                    <div class="service-item">
                                        <div class="image">
                                            <img src="<?php echo get_field('image', $value)['url']; ?>" alt="image">
                                        </div>
                                        <div class="content">
                                            <h6><a hreflang="<?php echo esc_attr($pll); ?>"  data-country="<?php echo $value->slug; ?>" class="open-country-box" href="javascript:;"><?php echo $value->name; ?></a></h6>
                                            <p><?php echo $value->description; ?></p>
                                            <!-- <a  hreflang="</?php echo esc_attr($pll); ?>" data-country="</?php echo $value->slug; ?>" class="open-country-box" href="javascript:;"><i class='bx bx-plus'></i></?php echo __('MORE', 'picostrap5-child-base'); ?></a> -->
                                        </div>
                                    </div>
                                </div>
                            <?php 
                        }
                    }
                ?>
            </div>
        </div>
    </div>

    <!-- ========== page-content-section start=========== -->

    <!-- ========== office-address-section start======== -->

    <div class="address-section pt-100 pb-100 location-countries-boxes" style="display: none;">
        <div class="container-one">
            <?php 
                if(!empty($location_countries)){

                    $first = true;

                    foreach ($location_countries as $value) {
                        ?>
                            <div class="location-countries-box" data-country="<?php echo $value->slug; ?>" <?php echo $first ? '' : 'style="display:none;"'; ?>>
                                <?php 
                                    // $country_data = get_field('country_data');
                                    $country_data = $value;
                                ?>
                                <div class="section-title-one style-yellow mb-50">
                                    <h2 ><?php echo $country_data->name; ?></h2>
                                    <p ><?php echo get_field('content', $country_data); ?></p>
                                </div>
                                <div class="row gy-4">
                                    <?php 
                                        $args = array(
                                            'post_type' => 'location',
                                            'posts_per_page' => '-1',

                                            'orderby' => 'menu_order',
                                            'order' => 'ASC',

                                            'tax_query' => array(
                                                array(
                                                    'taxonomy' => 'location_country',
                                                    'terms' => [$country_data->slug],
                                                    'field' => 'slug',
                                                    // 'include_children' => true,
                                                    'operator' => 'IN',
                                                ),
                                            ),
                                        );
                                        $locations = new WP_query($args);
                                        if($locations->have_posts()){
                                            while($locations->have_posts()){
                                                $locations->the_post();
                                                ?>
                                                    <div class="col-lg-4 col-md-4 col-sm-4 col-12 order-lg-1 order-3" >
                                                        <div class="address-single">
                                                            <h6><?php echo implode(', ', wp_get_post_terms(get_the_ID(), 'location_city', ['fields' => 'names'])); ?></h6>
                                                            <div class="details">
                                                                <p><?php echo get_field('description'); ?></p>
                                                            </div>
                                                                <ul class="address-list">
                                                                    <li>
                                                                        <strong><?php echo get_the_title(); ?></strong> <br>
                                                                        <?php echo nl2br(get_field('address')); ?></li>
                                                                     <li><a  hreflang="<?php echo esc_attr($pll); ?>" href="tel: <?php echo get_field('telephone'); ?>"><!-- T: --> <?php echo get_field('telephone'); ?></a></li>   
                                                                     <li><a hreflang="<?php echo esc_attr($pll); ?>" href="mailto:<?php echo get_field('email'); ?>"><!-- E: --> <?php echo get_field('email'); ?></a></li>   
                                                                </ul>
                                                                <?php
                                                                    $key_contacts = get_field('key_contacts');
                                                                    if (!empty($key_contacts)) {
                                                                ?>
                                                                    <ul class="key-contact-list">
                                                                        <li><?php echo __('Key contact', 'picostrap5-child-base'); ?>:</li>
                                                                        <?php 
                                                                            foreach ($key_contacts as $value) {
                                                                                ?>
                                                                                <li>
                                                                                    <a hreflang="<?php echo esc_attr($pll); ?>" href="<?php echo get_permalink($value->ID); ?>">
                                                                                        <?php echo esc_html($value->post_title); ?>
                                                                                    </a>
                                                                                    <span>
                                                                                        <?php echo implode(', ', wp_get_post_terms($value->ID, 'people_role', ['fields' => 'names'])); ?>
                                                                                    </span>
                                                                                </li>   
                                                                                <?php 
                                                                            }
                                                                        ?>
                                                                    </ul>
                                                                <?php } ?>

                                                                <!-- <a href="#" class="eg-btn btn--primary-yellow btn--lg2 mt-70">CLOSE</a> -->
                                                        </div>
                                                    </div>
                                                <?php 
                                            }
                                        }
                                        wp_reset_query();
                                    ?>
                                </div>
                            </div>
                        <?php 
                        if($first) $first = false;
                    }
                }
            ?>
        </div>
    </div>

    <!-- ========== office-address-section start======== -->

<script type="text/javascript">
    jQuery(function($){
        $(document).on('click', '.open-country-box', function(e){
            e.preventDefault();

            var this_a = $(this);
            var this_country = this_a.data('country');

            $(document).find('.location-countries-boxes:not(:visible)').fadeIn('fast');

            $(document).find('.location-countries-boxes .location-countries-box:visible').fadeOut('fast');
            $(document).find('.location-countries-boxes .location-countries-box[data-country="'+ this_country +'"]').fadeIn('slow');
            $('html, body').animate({
                scrollTop: $(document).find('.location-countries-boxes').offset().top - 100
            }, 500);
        });
    });
</script>

<?php get_footer();