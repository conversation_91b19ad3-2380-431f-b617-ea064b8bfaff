<?php

// Exit if accessed directly.
defined( 'ABSPATH' ) || exit;

get_header();

$pll = pll_current_language();


if ( have_posts() ) : 
    while ( have_posts() ) : the_post();

if(get_field('banner_video_url')){?>
<!-- ========== banner-section start============= -->
<div class="d-flex container-fluid banner-section video-banner" style="background:url(<?php echo get_the_post_thumbnail_url(); ?>)  center / cover no-repeat;">
    <div class="container-one position-relative">
        <div class="company-vdo position-relative">
            <div class="video-banner-bg"> </div>
            <?php 
                if($banner_video_url = get_field('banner_video_url')){
                    ?>
                        <div  class="video-popup-button">
                            <a hreflang="<?php echo esc_attr($pll); ?>" href="<?php echo $banner_video_url; ?>" class="video-popup play-icon"><img src="<?php echo get_stylesheet_directory_uri(); ?>/assets/images/icons/play-icon.svg" alt="image"></a>
                        </div>
                    <?php 
                }
            ?>
        </div>

        
    </div>
</div>
<div class="banner-single-post-title <?php if(empty($people = get_field('people'))){echo 'no-author';}?>">
    <div class="container-one">
        <h1 class="display-44"><?php the_title(); ?></h1>
        <div class="banner-single-post-meta">
            <span class="post-date title-tag"><strong><?php echo __('News', 'picostrap5-child-base'); ?></strong> </span>
            <span class="post-date"><?php the_date('d F Y'); ?> </span>
        </div>
        <?php 
            if($people = get_field('people')){
                $person = $people[0];

                $roles = get_the_terms( $person->ID, 'people_role' );
                $roles = join(', ', wp_list_pluck($roles, 'name'));

                $reverse_person_photo_places = get_field('reverse_person_photo_places', $person->ID);
                $image_url = get_field('person_photo_1', $person->ID) ? get_field('person_photo_1', $person->ID)['sizes']['medium_large'] : get_stylesheet_directory_uri() . '/assets/images/team/team.jpg';
                if($reverse_person_photo_places){
                    $image_url = get_field('person_photo_2', $person->ID) ? get_field('person_photo_2', $person->ID)['sizes']['medium_large'] : get_stylesheet_directory_uri() . '/assets/images/team/team.jpg';
                }

                ?>
                    <div class="banner-auther">
                        <div class="banner-auther-image">
                            <img src="<?php echo $image_url; ?>" alt="<?php echo get_the_title($person); ?>">
                        </div>
                        <div class="banner-auther-meta">
                            <div class="row">
                                <div class="col-sm-12">
                                    <div class="author-name-section">
                                        <p><?php echo __('Author:', 'picostrap5-child-base'); ?></p>
                                        <h6><a hreflang="<?php echo esc_attr($pll); ?>" href="<?php echo get_the_permalink($person); ?>"><?php echo get_the_title($person); ?></a></h6>
                                        <span><?php echo $roles; ?> </span>
                                        <span class="border-bottom-el"></span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                <?php 
            }
        ?>
    </div>
   
</div>


<?php } else if (get_the_post_thumbnail_url()){ 
        ?><div class="d-flex container-fluid banner-section" style="background:url(<?php echo get_the_post_thumbnail_url(); ?>)  center / cover no-repeat;">
            <div class="container-one position-relative">
                
            </div>
        </div>
        <div class="banner-single-post-title <?php if(empty($people = get_field('people'))){echo 'no-author';}?>">
            <div class="container-one">
                <h1 class="display-44"><?php the_title(); ?></h1>
                <div class="banner-single-post-meta">
                    <span class="post-date title-tag"><strong><?php echo __('News', 'picostrap5-child-base'); ?></strong> </span>
                    <span class="post-date"><?php the_date('d F Y'); ?> </span>
                </div>
                <?php 
                    if($people = get_field('people')){
                        $person = $people[0];

                        $roles = get_the_terms( $person->ID, 'people_role' );
                        $roles = join(', ', wp_list_pluck($roles, 'name'));

                        $reverse_person_photo_places = get_field('reverse_person_photo_places', $person->ID);
                        $image_url = get_field('person_photo_1', $person->ID) ? get_field('person_photo_1', $person->ID)['sizes']['medium_large'] : get_stylesheet_directory_uri() . '/assets/images/team/team.jpg';
                        if($reverse_person_photo_places){
                            $image_url = get_field('person_photo_2', $person->ID) ? get_field('person_photo_2', $person->ID)['sizes']['medium_large'] : get_stylesheet_directory_uri() . '/assets/images/team/team.jpg';
                        }

                        ?>
                            <div class="banner-auther">
                                <div class="banner-auther-image">
                                    <img src="<?php echo $image_url; ?>" alt="<?php echo get_the_title($person); ?>">
                                </div>
                                <div class="banner-auther-meta">
                                    <div class="row">
                                        <div class="col-sm-12">
                                            <div class="author-name-section">
                                                <p><?php echo __('Author:', 'picostrap5-child-base'); ?></p>
                                                <h6><a hreflang="<?php echo esc_attr($pll); ?>" href="<?php echo get_the_permalink($person); ?>"><?php echo get_the_title($person); ?></a></h6>
                                                <span><?php echo $roles; ?> </span>
                                                <span class="border-bottom-el"></span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        <?php 
                    }
                ?>
            </div>
            
        </div>
<?php } else { ?><div class="d-flex container-fluid banner-section">
            <div class="container-one position-relative">
                <div class="banner-single-post-title <?php if(empty($people = get_field('people'))){echo 'no-author';}?>">
                    <h2 class="display-44"><?php the_title(); ?></h2>
                    <span class="post-date title-tag"><strong><?php echo __('News', 'picostrap5-child-base'); ?></strong> </span>
                    <span class="post-date"><?php the_date('d F Y'); ?> </span>
                    <?php 
                        if($people = get_field('people')){
                            $person = $people[0];

                            $roles = get_the_terms( $person->ID, 'people_role' );
                            $roles = join(', ', wp_list_pluck($roles, 'name'));

                            $reverse_person_photo_places = get_field('reverse_person_photo_places', $person->ID);
                            $image_url = get_field('person_photo_1', $person->ID) ? get_field('person_photo_1', $person->ID)['sizes']['medium_large'] : get_stylesheet_directory_uri() . '/assets/images/team/team.jpg';
                            if($reverse_person_photo_places){
                                $image_url = get_field('person_photo_2', $person->ID) ? get_field('person_photo_2', $person->ID)['sizes']['medium_large'] : get_stylesheet_directory_uri() . '/assets/images/team/team.jpg';
                            }

                            ?>
                                <div class="banner-auther">
                                    <div class="banner-auther-image">
                                        <img src="<?php echo $image_url; ?>" alt="<?php echo get_the_title($person); ?>">
                                    </div>
                                    <div class="banner-auther-meta">
                                        <div class="row">
                                            <div class="col-sm-12">
                                                <div class="author-name-section">
                                                    <p><?php echo __('Author:', 'picostrap5-child-base'); ?></p>
                                                    <h6><a hreflang="<?php echo esc_attr($pll); ?>" href="<?php echo get_the_permalink($person); ?>"><?php echo get_the_title($person); ?></a></h6>
                                                    <span><?php echo $roles; ?> </span>
                                                    <span class="border-bottom-el"></span>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            <?php 
                        }
                    ?>
                </div>
            </div>
        </div>
    <?php } ?>
    
    <!-- <div class="container p-5 bg-light" style="margin-top:-100px"> -->
    <div class="container-one <?php echo $people ? 'people-meta-exists' : 'people-meta-not-exists'; ?> content-wrapper">
        <?php 
            /*if($people = get_field('people')){
                $person = $people[0];

                $roles = get_the_terms( $person->ID, 'people_role' );
                $roles = join(', ', wp_list_pluck($roles, 'name'));

                ?>
                    <div class="row">
                        <div class="col-sm-12">
                            <div class="author-name-section">
                                <p><?php echo __('Author:', 'picostrap5-child-base'); ?></p>
                                <h6><a href="<?php echo get_the_permalink($person); ?>"><?php echo get_the_title($person); ?></a></h6>
                                <span><?php echo $roles; ?> </span>
                                <span class="border-bottom-el"></span>
                            </div>
                        </div>
                    </div>
                <?php 
            }*/
        ?>

                <?php 
                //CATS
                if (!get_theme_mod("singlepost_disable_entry_cats") &&  has_category() ) {
                        ?>
                        <div class="row text-center">
                            <div class="col-md-12">
                                <div class="entry-categories">
                                    <span class="screen-reader-text"><?php _e( 'Categories', 'picostrap5-child-base' ); ?></span>
                                    <div class="entry-categories-inner">
                                        <?php the_category( ' ' ); ?>
                                    </div><!-- .entry-categories-inner -->
                                </div><!-- .entry-categories -->
                            </div><!-- /col -->
                        </div>
                        <?php
                }
                
                ?>

                
                
                <?php if (!get_theme_mod("singlepost_disable_date") OR !get_theme_mod("singlepost_disable_author")  ): ?>
                    <div class="post-meta" id="single-post-meta">
                        <p class="lead text-secondary">
                            
                            <?php if (!get_theme_mod("singlepost_disable_date") ): ?>
                                <!-- <span class="post-date"><?php the_date(); ?> </span> -->
                            <?php endif; ?>

                            <?php if (!get_theme_mod("singlepost_disable_author") ): ?>
                                <!-- <span class="text-secondary post-author"> <?php _e( 'by', 'picostrap5-child-base' ) ?> <?php the_author(); ?></span> -->
                            <?php endif; ?>
                        </p>
                    </div> 
                <?php endif; ?>


        <div class="row">
            <!-- <div class="col-md-8 offset-md-2"> -->
            <div class="col-md-12">
            <style>
                    .social-share-list{
                        list-style: none;
                        padding: 0;
                        margin: 0;
                        margin-bottom: 15px;
                    }
                    .social-share-list li a {
                        color: #000;
                        font-size: 18px;
                        margin-right: 10px;
                        color: #000 !important;
                    }
                    
                </style>
                <ul class="social-share-list d-flex align-items-center">
                    <li><a hreflang="<?php echo esc_attr($pll); ?>" href="https://www.linkedin.com/shareArticle?mini=true&url=<?php the_permalink(); ?>&title=<?php the_title(); ?>&summary=&source=" target="_blank"><i class="fab fa-linkedin-in"></i></a></li>
                    <li><a hreflang="<?php echo esc_attr($pll); ?>" href="mailto:?subject=<?php the_title(); ?>&body=<?php the_permalink(); ?>" target="_blank"><i class="fas fa-envelope"></i></a></li>
                    <li><a hreflang="<?php echo esc_attr($pll); ?>" href="javascript:window.print()"><i class="fas fa-print"></i></a></li>
                </ul>
                <?php 
                
                the_content();

                
                if( get_theme_mod("enable_sharing_buttons")) picostrap_the_sharing_buttons();
                
                edit_post_link( __( 'Edit this post', 'picostrap5-child-base' ), '<p class="text-end">', '</p>' );
                
                // If comments are open or we have at least one comment, load up the comment template.
                if (!get_theme_mod("singlepost_disable_comments")) if ( comments_open() || get_comments_number() ) {
                    comments_template();
                }
                
                ?>

            </div><!-- /col -->
        </div>


        <div class="row">
            <?php 
                /*if(get_field('people')){
                    ?>
                        <div class="col-sm-12 mt-5">
                            <h3 class="pt-3 mb-50 bt-black border-top border-dark"><?php echo __('People', 'picostrap5-child-base'); ?></h3>
                        </div>
                        <div class="row">
                            <div class="col-md-8 offset-md-2">
                                <div class="row">
                                    <?php 
                                        if(get_field('people')){
                                            global $post;
                                            foreach (get_field('people') as $post) {
                                                setup_postdata($post);
                                                ?>
                                                    <div class="col-sm-3">
                                                        <div class="team-item style-two style-two">
                                                            <div class="team-image">
                                                                <a href="<?php echo get_the_permalink(); ?>">
                                                                    <img src="<?php echo get_field('person_photo_1') ? get_field('person_photo_1')['sizes']['medium_large'] : get_stylesheet_directory_uri() . '/assets/images/team/team.jpg'; ?>" alt="<?php echo get_the_title(); ?>">
                                                                </a>
                                                            </div>
                                                            <div class="team-content">
                                                                <h6><a href="<?php echo get_the_permalink(); ?>"><?php echo get_the_title(); ?></a></h6>
                                                                <span><?php echo $roles; ?> </span>
                                                                <span><?php echo $locations; ?></span>
                                                            </div>
                                                        </div>
                                                    </div>
                                                <?php 
                                            }
                                            wp_reset_postdata();
                                        }
                                    ?>
                                </div>
                            </div>
                        </div>
                    <?php 
                }*/
            ?>
        </div>
    </div>

    <div class="container-one bulletins <?php echo $people ? 'people-meta-exists' : 'people-meta-not-exists'; ?> content-wrapper">
       
        

       

        <?php 
            $manual_sectors_list = get_field('manual_sectors_list');
            $manual_sectors_list_data = get_field('manual_sectors_list_data');
        ?>
        <div class="row">
            <div class="col-sm-12">
                <div class="experience-box <?php echo get_field('sector_sub_sector') || ($manual_sectors_list && $manual_sectors_list_data) ? '' : 'd-none'; ?>">
                    <h5 class="title" ><?php echo __('Relevant sectors', 'picostrap5-child-base'); ?> </h5>
                    <?php
                        if ($manual_sectors_list) : ?>
                            <ul>
                                <?php foreach ($manual_sectors_list_data as $_sector) : ?>
                                    <li>
                                        <?php if (!empty($_sector['url'])) : ?>
                                            <a href="<?php echo esc_url($_sector['url']); ?>"><?php echo esc_html($_sector['title']); ?></a>
                                        <?php else : ?>
                                            <?php echo esc_html($_sector['title']); ?>
                                        <?php endif; ?>
                                    </li>
                                <?php endforeach; ?>
                            </ul>
                            <?php else :
                            $sectors = get_field('sector_sub_sector');
                            $_sectors = wp_list_pluck($sectors, 'ID');

                            $args = [
                                'post_type'      => 'sector',
                                'posts_per_page' => -1,
                                'post_status'    => 'publish',
                                'post_parent'    => 0,
                                'post__in'       => $_sectors,
                                'orderby'        => 'title',
                                'order'          => 'ASC',
                            ];

                            $main_sectors = get_posts($args);

                            if (empty($main_sectors)) {
                                unset($args['post_parent']);
                                $main_sectors = get_posts($args);
                            }

                            if ($main_sectors) :
                                foreach ($main_sectors as $sector) :
                                    $has_parent = $sector->post_parent != 0;
                                    $has_page_parent = wp_get_post_parent_id(get_the_ID());
                            ?>
                                    <h6 class="subtitle">
                                        <?php if (!$has_page_parent) : ?>
                                            <a href="<?php echo get_permalink($sector); ?>"><?php echo esc_html($sector->post_title); ?></a>
                                        <?php else : ?>
                                            <?php echo esc_html($sector->post_title); ?>
                                        <?php endif; ?>
                                    </h6>
                                    <?php
                                    $sub_args = [
                                        'post_type'      => 'sector',
                                        'posts_per_page' => -1,
                                        'post_status'    => 'publish',
                                        'post_parent'    => $sector->ID,
                                        'post__in'       => $_sectors,
                                        'orderby'        => 'title',
                                        'order'          => 'ASC',
                                    ];

                                    $sub_sectors = get_posts($sub_args);

                                    if ($sub_sectors) :
                                        echo '<ul>';
                                        foreach ($sub_sectors as $sub) : ?>
                                            <li><?php echo esc_html($sub->post_title); ?></li>
                                <?php endforeach;
                                        echo '</ul>';
                                    endif;
                                endforeach;
                            else : ?>
                                <h6 class="subtitle"><?php echo esc_html__('No sectors listed.', 'picostrap5-child-base'); ?></h6>
                        <?php endif;
                        endif;
                        ?>
                </div>
            </div>
        </div>

        <div class="row mt-4">
            <div class="col-sm-12">
                <div class="experience-box <?php echo get_field('after_sectors_html') ? '' : 'd-none'; ?>">
                    <h5 class="title" ><?php echo __('Relevant sectors', 'picostrap5-child-base'); ?> </h5>
                    <?php 
                        echo get_field('after_sectors_html');
                    ?>
                </div>
            </div>
        </div>

        <div class="row d-none">
            <?php 
                /*if(get_field('people')){
                    ?>
                        <div class="col-sm-12 mt-5">
                            <h3 class="pt-3 mb-50 bt-black border-top border-dark"><?php echo __('People', 'picostrap5-child-base'); ?></h3>
                        </div>
                        <div class="row">
                            <div class="col-md-8 offset-md-2">
                                <div class="row">
                                    <?php 
                                        if(get_field('people')){
                                            global $post;
                                            foreach (get_field('people') as $post) {
                                                setup_postdata($post);
                                                ?>
                                                    <div class="col-sm-3">
                                                        <div class="team-item style-two style-two">
                                                            <div class="team-image">
                                                                <a href="<?php echo get_the_permalink(); ?>">
                                                                    <img src="<?php echo get_field('person_photo_1') ? get_field('person_photo_1')['sizes']['medium_large'] : get_stylesheet_directory_uri() . '/assets/images/team/team.jpg'; ?>" alt="<?php echo get_the_title(); ?>">
                                                                </a>
                                                            </div>
                                                            <div class="team-content">
                                                                <h6><a href="<?php echo get_the_permalink(); ?>"><?php echo get_the_title(); ?></a></h6>
                                                                <span><?php echo $roles; ?> </span>
                                                                <span><?php echo $locations; ?></span>
                                                            </div>
                                                        </div>
                                                    </div>
                                                <?php 
                                            }
                                            wp_reset_postdata();
                                        }
                                    ?>
                                </div>
                            </div>
                        </div>
                    <?php 
                }*/
            ?>
        </div>
    </div>  

<?php
    endwhile;
 else :
     _e( 'Sorry, no posts matched your criteria.', 'picostrap5-child-base' );
 endif;
 ?>


 

<?php get_footer();
