<?php 
/*
    Template Name: Career Early Careers Page
*/

// Exit if accessed directly.
defined( 'ABSPATH' ) || exit;
$pll = pll_current_language();
get_header();

?>


<!-- ========== banner-section start============= -->

<div class="banner-video-section overflow-hidden">
    <div class="container-fluid px-0">
        <div class="company-vdo position-relative"
            style="background-image:  url('<?php echo get_the_post_thumbnail_url(get_the_ID(), 'full'); ?>');">
            <div class="inner-overlay"></div>
            <!-- style="background-image: linear-gradient(to bottom, rgba(255,255,255,0.05) 0%, rgba(0,0,0,0.15) 50%), radial-gradient(at top center, rgba(255,255,255,0.20) 50%, rgba(0,0,0,0.40) 190%), url('<?php echo get_the_post_thumbnail_url(get_the_ID(), 'full'); ?>');"> -->
            <!-- style="background-image: linear-gradient(to bottom, rgba(255,255,255,0.05) 0%, rgba(0,0,0,0.15) 100%), radial-gradient(at top center, rgba(255,255,255,0.40) 0%, rgba(0,0,0,0.40) 120%), url('<?php echo get_the_post_thumbnail_url(get_the_ID(), 'full'); ?>');"> -->
            <h3 ><?php echo get_field('banner_title');?></h3>
            <?php if(get_field('banner_video_link')){ ?>
            <div  class="video-popup-button">
                <a hreflang="<?php echo esc_attr($pll); ?>" href="<?php echo get_field('banner_video_link');?>" class="video-popup play-icon"><img
                        src="<?php echo get_stylesheet_directory_uri();?>/assets/images/icons/play-icon.svg" alt="image"></a>
            </div>
            <?php } ?>
        </div>
    </div>
</div>
<h1 style="opacity:0; display: none;">hidden</h1>
<!-- ========== banner-section end============= -->
<!-- ========== software-section start============= -->
<?php 
    $why_boult = get_field('why_boult');
    $careers_description = get_field('careers_description');
?>
<div class="career-details-section pt-100 pr-container" id="next-section">
    <div class="container-fluid">
        <div class="row g-4">
            <div class="col-md-6 col-lg-6 col-xl-7">
                <div class="choose-us-card d-none">
                    <h2 ><?php echo $why_boult['title']; ?></h2>
                    <div >
                        
                    <?php echo $why_boult['content']; ?>
                    </div>
                </div>
                <div class="career-left-image mt-100" >
                    <img src="<?php echo $careers_description['image'];?>" alt="image">
                </div>
                <div class="choose-card-box"></div>
                <div class="business-card-section new-style">
                    <div class="business-card-box">
                        <div class="business-card-box-content"><?php echo get_field('download_box_content') ?></div>
                    </div>
                </div>
            </div>
            <div class="col-md-6 col-lg-6 col-xl-5 ps-lg-3 ps-xl-5">
                <div class="section-title-one style-yellow" >
                    <h2><?php echo $careers_description['title']; ?></h2>
                </div>
                <div class="content animated-section" >
                    <?php echo $careers_description['content']; ?>
                </div>
                <!-- <div class="blue-box">
                </div> -->
            </div>
        </div>
    </div>
</div>

<!-- ========== software-section end============= -->
<?php 
    $careers_category = get_field('careers_category');
    if(!empty($careers_category)) :
?>
<div class="career-card-section pt-100">
    <div class="container-one">
        <div class="row g-lg-4 g-2">
            <?php foreach($careers_category as $value) : ?>
            <div class="col-lg-4 col-md-6 col-12" >
                <div style="background: <?php echo $value['color']; ?>" class="career-card-two style-blue">
                    <div class="title">
                        <h5><?php echo $value['title']; ?></h5>
                    </div>
                    <p><?php echo $value['content']; ?></p>
                    <a hreflang="<?php echo esc_attr($pll); ?>" href="<?php echo isset($value['link']) && isset($value['link']['url']) ? $value['link']['url'] : ''; ?>" class="eg-btn btn--primary-black style-two btn--lg2"><?php echo isset($value['link']) && isset($value['link']['title']) ? $value['link']['title'] : ''; ?><i
                            class="bi bi-arrow-right"></i></a>
                </div>
            </div>
            <?php endforeach; ?>
        </div>
    </div>
</div>
<?php endif; ?>
<?php 
    $skills_card = get_field('skills_card');
?>
<div class="skill-section pt-100">
    <div class="skill-box-design"></div>
    <div class="container-one">
        <div class="section-title-one style-blue">
            <h2 ><?php echo $skills_card['title']; ?></h2>
        </div>
        <div class="row g-lg-4 g-2">
            <?php foreach($skills_card['skills_list'] as $value) : ?>
            <div class="col-sm-6 col-md-6 col-lg-4 col-12" >
                <div class="skill-card">
                    <div class="content">
                        <h5><?php echo $value['title']; ?> </h5>
                        <p><?php echo $value['content']; ?></p>
                    </div>
                    <div class="icon">
                        <?php echo $value['image_svg_code']; ?>
                    </div>
                </div>
            </div>
            <?php endforeach; ?>
        </div>
    </div>
</div>

<?php 
        $people_quote = get_field('people_quote');
        $extra_linkx = get_field('extra_link');
        $extra_link = explode('~', $extra_linkx);
    ?>
<div class="testimonial-section mb-1000 <?php // echo $people_quote ? '' : 'd-none'; ?>">
    <div class="testi-top-image" >
        <div class="extra_link">
            <img src="<?php echo $skills_card['image']['url']; ?>" alt="image">
        <?php if($extra_linkx) { ?>
        <h3>
            <a hreflang="<?php echo esc_attr($pll); ?>" href="<?php echo $extra_link[0] ?>" target="_blank" class="text-white"><?php echo $extra_link[1] ?></a>
        </h3>
        <?php } ?>
        </div>
    </div>
    
    <div class="container-one">
        <div class="row">
            <div class="col-lg-6">

                

                <div class="career-a recent-highlight-quotes">
                    <div class="box-with-border-inner"></div>
                    <div class="box-inner-content">
                        <?php echo get_field('quotes_1'); ?>
                        <?php 
                            $quote_person_image = get_field('quote_person_image_1');
                            $person_image = get_stylesheet_directory_uri() . '/assets/images/team/team.jpg';
                            if($quote_person_image){
                                $roles = get_the_terms( $quote_person_image->ID, 'people_role' );
                                $roles = join(', ', wp_list_pluck($roles, 'name'));

                                $person_image = get_field('person_photo_1', $quote_person_image->ID) ? get_field('person_photo_1', $quote_person_image->ID)['sizes']['medium_large'] : get_stylesheet_directory_uri() . '/assets/images/team/team.jpg';
                                ?>
                                    <div class="team-content quote-inner">
                                        <h6><a hreflang="<?php echo esc_attr($pll); ?>" href="<?php echo get_the_permalink($quote_person_image); ?>"><?php echo get_the_title($quote_person_image); ?></a></h6>
                                        <span><?php echo strtoupper($roles); ?></span>
                                    </div>
                                <?php 
                            }
                        ?>
                    </div>
                    <div class="box-inner-image">
                        <img src="<?php echo $person_image; ?>" alt="image">
                    </div>
                </div>
                <!-- <div class="download-card-box-content">
                    <div class="business-card-box-content"><?php // echo get_field('download_box_content') ?></div>
                </div> -->
                <div class="expertise-section-1">
                    <div class="section-title-one style-blue">
                        <h2 ><?php echo get_field('expertise_title'); ?></h2>
                        <p class="mb-35" ><?php echo get_field('expertise_description'); ?></p>
                        <?php 
                            $expertise_button = get_field('expertise_button');
                            if($expertise_button && isset($expertise_button['url'])){
                                ?>
                                    <a hreflang="<?php echo esc_attr($pll); ?>" href="<?php echo $expertise_button['url']; ?>" class="eg-btn btn--primary-blue btn--lg2"><?php echo $expertise_button['title']; ?> <i
                                            class="bi bi-arrow-right"></i></a>
                                <?php 
                            }
                        ?>
                    </div>
                </div>
                
            </div>
            <div class="col-lg-6">
                <div class="career-b recent-highlight-quotes">
                    <div class="box-with-border-inner"></div>
                    <div class="box-inner-content bg-primary-green-dark-light">
                        <?php echo get_field('quotes_2'); ?>
                        <?php 
                            $quote_person_image = get_field('quote_person_image_2');
                            $person_image = get_stylesheet_directory_uri() . '/assets/images/team/team.jpg';
                            if($quote_person_image){
                                $roles = get_the_terms( $quote_person_image->ID, 'people_role' );
                                $roles = join(', ', wp_list_pluck($roles, 'name'));

                                $person_image = get_field('person_photo_1', $quote_person_image->ID) ? get_field('person_photo_1', $quote_person_image->ID)['sizes']['medium_large'] : get_stylesheet_directory_uri() . '/assets/images/team/team.jpg';
                                ?>
                                    <div class="team-content quote-inner">
                                        <h6><a hreflang="<?php echo esc_attr($pll); ?>" href="<?php echo get_the_permalink($quote_person_image); ?>"><?php echo get_the_title($quote_person_image); ?></a></h6>
                                        <span><?php echo strtoupper($roles); ?></span>
                                    </div>
                                <?php 
                            }
                        ?>
                    </div>
                    <div class="box-inner-image">
                        <img src="<?php echo $person_image; ?>" alt="image">
                    </div>
                </div>
                
            </div>
        </div>
        <?php 
            foreach($people_quote as $value) :
        ?>
        <div style="background-color: <?php echo $value['background_color']; ?>" class="big-card testimonial-single style-yellow mb-90">
            <div class="content" >
                <div class="title">
                    <h4><?php echo $value['title']; ?></h4>
                </div>
                <div style="border-color: <?php echo $value['border_color']; ?>" class="body">
                    <?php echo $value['content']; ?>
                </div>
            </div>
            <?php 
                $thum_img = get_field('person_photo_1', $value['select_people']->ID);
                $role =  get_the_terms( $value['select_people']->ID, 'people_role' );

                $location =  get_the_terms( $value['select_people']->ID, 'location' );
            ?>
            <div class="author" >
                <?php if(!empty($thum_img)) :?>
                    <div class="author-img">
                        <img src="<?php echo $thum_img['url']; ?>" alt="image">
                    </div>
                    <?php else: ?>
                        <?php 
                        $classes = array('bg-primary-red', 'bg-primary-green', 'bg-primary-yellow', 'bg-primary-blue');
                        shuffle($classes);
                        $c = array_rand($classes, 1);
                        ?>
                        <div class="no-person-image-box <?php echo $classes[$c]; ?>"></div>
                <?php endif; ?>
                <h6><?php echo get_the_title($value['select_people']->ID);?></h6>
                <span>
                    <?php 
                        if(!empty($role)){
                            echo $role[0]->name;
                        }
                    ?>
                </span>
                <span>
                    <?php 
                        if(!empty($location)){
                            echo $location[0]->name;
                        }
                    ?>
                </span>
                <span><?php echo $value['people_text']; ?></span>
            </div>
        </div>
        <?php endforeach; ?>
       
    </div>
</div>


<?php get_footer();