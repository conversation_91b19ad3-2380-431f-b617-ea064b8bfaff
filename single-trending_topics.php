<?php

// Exit if accessed directly.
defined('ABSPATH') || exit;

get_header();

$pll = pll_current_language();


if (have_posts()) :
    while (have_posts()) : the_post();

        if (get_the_post_thumbnail_url()) {
?><div class="d-flex container-fluid banner-section" style="background:url(<?php echo get_the_post_thumbnail_url(); ?>)  center / cover no-repeat;">
                <div class="container-one position-relative">

                </div>
            </div>
            <div class="banner-single-post-title">
                <div class="container-one">
                    <h1 class="display-44"><?php the_title(); ?></h1>
                    <div class="banner-single-post-meta">
                        <span class="post-date title-tag"><strong><?php echo __('Trending topics', 'picostrap5-child-base'); ?></strong> </span>
                        <span class="post-date"><?php the_date('d F Y'); ?> </span>
                    </div>
                </div>

            </div>
        <?php } else {
        ?><div class="d-flex container-fluid banner-section">
                <div class="container-one position-relative">
                    <div class="banner-single-post-title">
                        <h2 class="display-44"><?php the_title(); ?></h2>
                        <span class="post-date title-tag"><strong><?php echo __('Trending topics', 'picostrap5-child-base'); ?></strong> </span>
                        <span class="post-date"><?php the_date('d F Y'); ?> </span>
                    </div>
                </div>
            </div>
        <?php } ?>

        <div class="bulletin-people-wrap <?php echo empty(get_field('people')) ? 'd-none' : '' ?>">
            <div class="container-one">
                <div class="row">
                    <div class="col-md-12">
                        <div class="people-list-bulletin">
                            <div class="row g-4 row-cols-lg-6 row-cols-md-4 row-cols-sm-3 row-cols-2">
                                <?php
                                if ($people = get_field('people')) {
                                    foreach ($people as $person) :

                                        //$person = $people[0];

                                        $roles = get_the_terms($person->ID, 'people_role');
                                        $roles = join(', ', wp_list_pluck($roles, 'name'));

                                        $reverse_person_photo_places = get_field('reverse_person_photo_places', $person->ID);
                                        $image_url = get_field('person_photo_1', $person->ID) ? get_field('person_photo_1', $person->ID)['sizes']['medium_large'] : get_stylesheet_directory_uri() . '/assets/images/team/team.jpg';
                                        if ($reverse_person_photo_places) {
                                            $image_url = get_field('person_photo_2', $person->ID) ? get_field('person_photo_2', $person->ID)['sizes']['medium_large'] : get_stylesheet_directory_uri() . '/assets/images/team/team.jpg';
                                        }

                                ?>

                                        <div class="col">
                                            <div class="banner-auther">
                                                <div class="banner-auther-image">
                                                    <img src="<?php echo $image_url; ?>" alt="<?php echo get_the_title($person); ?>">
                                                </div>
                                                <div class="banner-auther-meta">
                                                    <div class="row">
                                                        <div class="col-sm-12">
                                                            <div class="author-name-section">
                                                                <?php /* <p><?php echo __('Author:', 'picostrap5-child-base'); ?></p> */ ?>
                                                                <h6><a hreflang="<?php echo esc_attr($pll); ?>" href="<?php echo get_the_permalink($person); ?>"><?php echo get_the_title($person); ?></a></h6>
                                                                <span><?php echo $roles; ?> </span>
                                                                <span class="border-bottom-el"></span>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                <?php
                                    endforeach;
                                }
                                ?>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- <div class="container p-5 bg-light" style="margin-top:-100px"> -->
        <div class="container-one <?php echo $people ? 'people-meta-exists' : 'people-meta-not-exists'; ?> content-wrapper">
            <?php
            /*if($people = get_field('people')){
                $person = $people[0];

                $roles = get_the_terms( $person->ID, 'people_role' );
                $roles = join(', ', wp_list_pluck($roles, 'name'));

                ?>
                    <div class="row">
                        <div class="col-sm-12">
                            <div class="author-name-section">
                                <p><?php echo __('Author:', 'picostrap5-child-base'); ?></p>
                                <h6><a href="<?php echo get_the_permalink($person); ?>"><?php echo get_the_title($person); ?></a></h6>
                                <span><?php echo $roles; ?> </span>
                                <span class="border-bottom-el"></span>
                            </div>
                        </div>
                    </div>
                <?php 
            }*/
            ?>

            <?php
            //CATS
            if (!get_theme_mod("singlepost_disable_entry_cats") &&  has_category()) {
            ?>
                <div class="row text-center">
                    <div class="col-md-12">
                        <div class="entry-categories">
                            <span class="screen-reader-text"><?php _e('Categories', 'picostrap5-child-base'); ?></span>
                            <div class="entry-categories-inner">
                                <?php the_category(' '); ?>
                            </div><!-- .entry-categories-inner -->
                        </div><!-- .entry-categories -->
                    </div><!-- /col -->
                </div>
            <?php
            }

            ?>



            <?php if (!get_theme_mod("singlepost_disable_date") or !get_theme_mod("singlepost_disable_author")): ?>
                <div class="post-meta" id="single-post-meta">
                    <p class="lead text-secondary">

                        <?php if (!get_theme_mod("singlepost_disable_date")): ?>
                            <!-- <span class="post-date"><?php the_date(); ?> </span> -->
                        <?php endif; ?>

                        <?php if (!get_theme_mod("singlepost_disable_author")): ?>
                            <!-- <span class="text-secondary post-author"> <?php _e('by', 'picostrap5-child-base') ?> <?php the_author(); ?></span> -->
                        <?php endif; ?>
                    </p>
                </div>
            <?php endif; ?>


            <div class="row">
                <!-- <div class="col-md-8 offset-md-2"> -->
                <div class="col-md-12">
                    <?php the_content(); ?>
                    <?php
                    $pdf_box_area = get_field('pdf_box_area');
                    if (!empty($pdf_box_area['box_title'])):
                    ?>
                    <div class="row pt-50 g-4 mb-50">
                            <div class="col-md-5">
                                <div class="pdf-flip-area">
                                    <?php if (!empty($pdf_box_area['box_title'])): ?>
                                        <h6><?php echo $pdf_box_area['box_title'] ?></h6>
                                    <?php endif; ?>
                                    <?php
                                    if (!empty($pdf_box_area['box_list'])) {
                                        foreach ($pdf_box_area['box_list'] as $value) {
                                    ?>
                                            <div class="ecpertise-img-cont t-tropic">
                                                <a hreflang="<?php echo esc_attr($pll); ?>" target="_blank" href="<?php echo $value['link']; ?>"><img src="<?php echo $value['image']; ?>" alt="img"></a>
                                            </div>
                                    <?php
                                        }
                                    }
                                    ?>
                                </div>
                            </div>
                        
                        <div class="col-md-7">
                            <div class="culture-content-2">
                                <?php
                                if (!empty($pdf_box_area['box_list'])) {
                                    foreach ($pdf_box_area['box_list'] as $value) {
                                ?>
                                        <?php if (!empty($value['title'])): ?>
                                            <h6><?php echo $value['title']; ?></h6>
                                        <?php endif; ?>
                                <?php
                                    }
                                }
                                ?>

                            </div>
                        </div>
                    </div>
                    <?php endif; ?>

                    <?php

                    if (get_theme_mod("enable_sharing_buttons")) picostrap5 - child - base();

                    edit_post_link(__('Edit this post', 'picostrap5-child-base'), '<p class="text-end">', '</p>');

                    // If comments are open or we have at least one comment, load up the comment template.
                    if (!get_theme_mod("singlepost_disable_comments")) if (comments_open() || get_comments_number()) {
                        comments_template();
                    }
                    ?>


                </div><!-- /col -->
            </div>

            <?php
            $manual_sectors_list = get_field('manual_sectors_list');
            $manual_sectors_list_data = get_field('manual_sectors_list_data');
            ?>
            <div class="row <?php echo get_field('sector_sub_sector') || ($manual_sectors_list && $manual_sectors_list_data) ? '' : 'd-none'; ?>">
                <div class="col-sm-12">
                    <div class="experience-box ">
                        <h5 class="title"><?php echo __('Relevant sectors', 'picostrap5-child-base'); ?> </h5>
                        <?php
                        // echo get_field('sector_sub_sector');

                        if ($manual_sectors_list) {
                        ?>
                            <ul>
                                <?php
                                foreach ($manual_sectors_list_data as $_sector) {
                                    if ($_sector['url']) {
                                ?>
                                        <li><a hreflang="<?php echo esc_attr($pll); ?>" href="<?php echo $_sector['url']; ?>"><?php echo $_sector['title']; ?></a></li>
                                    <?php
                                    } else {
                                    ?>
                                        <li><?php echo $_sector['title']; ?></li>
                                <?php
                                    }
                                }
                                ?>
                            </ul>
                            <?php
                        } else {

                            $sectors = get_field('sector_sub_sector');
                            $_sectors = $sectors = wp_list_pluck($sectors, 'ID');

                            $arg = array(
                                'post_type' => 'sector',
                                'posts_per_page' => '-1',
                                'post_status' => 'publish',
                                'post_parent' => '0',
                                'post__in' => $sectors,
                            );
                            $s = get_posts($arg);
                            if (empty($s)) {
                                unset($arg['post_parent']);
                                $s = get_posts($arg);
                            }

                            if ($s) {
                                foreach ($s as $sector) {
                            ?>
                                    <h6 class="subtitle"><?php echo $sector->post_title; ?></h6>
                                    <?php
                                    $arg = array(
                                        'post_type' => 'sector',
                                        'posts_per_page' => '-1',
                                        'post_status' => 'publish',
                                        'post_parent' => $sector->ID,
                                        'post__in' => $_sectors,
                                    );
                                    $sec = get_posts($arg);
                                    if ($sec) {
                                        echo '<ul >';
                                        foreach ($sec as $_sector) {
                                    ?>
                                            <li><?php echo $_sector->post_title; ?></li>
                                <?php
                                        }
                                        echo '</ul>';
                                    }
                                }
                            } else {
                                ?>
                                <h6 class="subtitle"><?php echo __('No sectors listed.', 'picostrap5-child-base'); ?></h6>
                        <?php
                            }
                        }
                        ?>
                    </div>
                </div>

                <div class="row">
                    <?php
                    /*if(get_field('people')){
                    ?>
                        <div class="col-sm-12 mt-5">
                            <h3 class="pt-3 mb-50 bt-black border-top border-dark"><?php echo __('People', 'picostrap5-child-base'); ?></h3>
                        </div>
                        <div class="row">
                            <div class="col-md-8 offset-md-2">
                                <div class="row">
                                    <?php 
                                        if(get_field('people')){
                                            global $post;
                                            foreach (get_field('people') as $post) {
                                                setup_postdata($post);
                                                ?>
                                                    <div class="col-sm-3">
                                                        <div class="team-item style-two style-two">
                                                            <div class="team-image">
                                                                <a href="<?php echo get_the_permalink(); ?>">
                                                                    <img src="<?php echo get_field('person_photo_1') ? get_field('person_photo_1')['sizes']['medium_large'] : get_stylesheet_directory_uri() . '/assets/images/team/team.jpg'; ?>" alt="<?php echo get_the_title(); ?>">
                                                                </a>
                                                            </div>
                                                            <div class="team-content">
                                                                <h6><a href="<?php echo get_the_permalink(); ?>"><?php echo get_the_title(); ?></a></h6>
                                                                <span><?php echo $roles; ?> </span>
                                                                <span><?php echo $locations; ?></span>
                                                            </div>
                                                        </div>
                                                    </div>
                                                <?php 
                                            }
                                            wp_reset_postdata();
                                        }
                                    ?>
                                </div>
                            </div>
                        </div>
                    <?php 
                }*/
                    ?>
                </div>
            </div>

    <?php
    endwhile;
else :
    _e('Sorry, no posts matched your criteria.', 'picostrap5-child-base');
endif;
    ?>




    <?php get_footer();
