<?php

// Exit if accessed directly.
defined('ABSPATH') || exit;

get_header();
$pll = pll_current_language();



if (have_posts()) :
    while (have_posts()) : the_post();

        if (get_the_post_thumbnail_url()) {
?>
            <div class="d-flex container-fluid banner-section position-relative" style="background:url(<?php echo get_the_post_thumbnail_url(); ?>)  center / cover no-repeat;">
                <div class="inner-overlay"></div>
                <div class="container-one position-relative">

                </div>
            </div>
            <div class="banner-single-post-title title-bg-green">
                <div class="container-one">
                    <h1 class="display-44"><?php the_title(); ?></h1>
                    <div class="banner-single-post-meta">
                        <span class="post-date title-tag"><strong><?php echo __('UPC News', 'picostrap5-child-base'); ?></strong> </span>
                        <span class="post-date"><?php the_date('d F Y'); ?> </span>
                    </div>
                    
                </div>
                
            </div>
            <div class="bulletin-people-wrap <?php echo empty(get_field('people')) ? 'd-none' : '' ?>">
                <div class="container-one">
                    <div class="row">
                        <div class="col-md-12">
                            <div class="people-list-bulletin">
                                <div class="row g-4 row-cols-lg-6 row-cols-md-4 row-cols-sm-3 row-cols-2">
                                    <?php
                                    if ($people = get_field('people')) {
                                        foreach ($people as $person) :


                                            $roles = get_the_terms($person->ID, 'people_role');
                                            $roles = join(', ', wp_list_pluck($roles, 'name'));

                                            $reverse_person_photo_places = get_field('reverse_person_photo_places', $person->ID);
                                            $image_url = get_field('person_photo_1', $person->ID) ? get_field('person_photo_1', $person->ID)['sizes']['medium_large'] : get_stylesheet_directory_uri() . '/assets/images/team/team.jpg';
                                            if ($reverse_person_photo_places) {
                                                $image_url = get_field('person_photo_2', $person->ID) ? get_field('person_photo_2', $person->ID)['sizes']['medium_large'] : get_stylesheet_directory_uri() . '/assets/images/team/team.jpg';
                                            }

                                    ?>

                                            <div class="col">
                                                <div class="banner-auther">
                                                    <div class="banner-auther-image">
                                                        <img src="<?php echo $image_url; ?>" alt="<?php echo get_the_title($person); ?>"> 
                                                    </div>
                                                    <div class="banner-auther-meta">
                                                        <div class="row">
                                                            <div class="col-sm-12">
                                                                <div class="author-name-section">
                                                                    <!-- <p><? //php echo __('Author:', 'picostrap5-child-base'); 
                                                                            ?></p> -->
                                                                    <h6><a hreflang="<?php echo esc_attr($pll); ?>" href="<?php echo get_the_permalink($person); ?>"><?php echo get_the_title($person); ?></a></h6>
                                                                    <span><?php echo $roles; ?> </span>
                                                                    <span class="border-bottom-el"></span>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                    <?php
                                        endforeach;
                                    }
                                    ?>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        <?php } else {
        ?><div class="d-flex container-fluid" style="height:30vh;"></div>
        <?php } ?>

        <!-- <div class="container p-5 bg-light" style="margin-top:-100px"> -->
        <div class="container-one bulletins <?php echo $people ? 'people-meta-exists' : 'people-meta-not-exists'; ?> content-wrapper">
            <?php
            /*if($people = get_field('people')){
                    $person = $people[0];

                    $roles = get_the_terms( $person->ID, 'people_role' );
                    $roles = join(', ', wp_list_pluck($roles, 'name'));

                    ?>
                        <div class="row">
                            <div class="col-sm-12">
                                <div class="author-name-section">
                                    <p class="author-name-label"><?php echo __('Author:', 'picostrap5-child-base'); ?></p>
                                    <h6><a href="<?php echo get_the_permalink($person); ?>"><?php echo get_the_title($person); ?></a></h6>
                                    <span class="author-name-label"><?php echo $roles; ?> </span>
                                    <span class="border-bottom-el"></span>
                                </div>
                            </div>
                        </div>
                    <?php 
                }*/
            ?>

            <?php
            //CATS
            if (!get_theme_mod("singlepost_disable_entry_cats") &&  has_category()) {
            ?>
                <div class="row text-center">
                    <div class="col-md-12">
                        <div class="entry-categories">
                            <span class="screen-reader-text"><?php _e('Categories', 'picostrap5-child-base'); ?></span>
                            <div class="entry-categories-inner">
                                <?php the_category(' '); ?>
                            </div><!-- .entry-categories-inner -->
                        </div><!-- .entry-categories -->
                    </div><!-- /col -->
                </div>
            <?php
            }

            ?>

            <?php if (!get_theme_mod("singlepost_disable_date") or !get_theme_mod("singlepost_disable_author")): ?>
                <div class="post-meta" id="single-post-meta">
                    <p class="lead text-secondary">

                        <?php if (!get_theme_mod("singlepost_disable_date")): ?>
                            <!-- <span class="post-date"><?php the_date(); ?> </span> -->
                        <?php endif; ?>

                        <?php if (!get_theme_mod("singlepost_disable_author")): ?>
                            <!-- <span class="text-secondary post-author"> <?php // _e( 'by', 'picostrap5-child-base' ) 
                                                                            ?> <?php // the_author(); 
                                                                                ?></span> -->
                        <?php endif; ?>
                    </p>
                </div>
            <?php endif; ?>

            <div class="row">
                <!-- <div class="col-md-8 offset-md-2"> -->
                <div class="col-md-12">
                <style>
                    .social-share-list{
                        list-style: none;
                        padding: 0;
                        margin: 0;
                        margin-bottom: 15px;
                    }
                    .social-share-list li a {
                        color: #000;
                        font-size: 18px;
                        margin-right: 10px;
                        color: #000 !important;
                    }
                    
                </style>
                <ul class="social-share-list d-flex align-items-center">
                    <li><a hreflang="<?php echo esc_attr($pll); ?>" href="https://www.linkedin.com/shareArticle?mini=true&url=<?php the_permalink(); ?>&title=<?php the_title(); ?>&summary=&source=" target="_blank"><i class="fab fa-linkedin-in"></i></a></li>
                    <li><a hreflang="<?php echo esc_attr($pll); ?>" href="mailto:?subject=<?php the_title(); ?>&body=<?php the_permalink(); ?>" target="_blank"><i class="fas fa-envelope"></i></a></li>
                    <li><a hreflang="<?php echo esc_attr($pll); ?>" href="javascript:window.print()"><i class="fas fa-print"></i></a></li>
                </ul>
                    <?php

                    the_content();


                    if (get_theme_mod("enable_sharing_buttons")) picostrap5 - child - base_the_sharing_buttons();

                    edit_post_link(__('Edit this post', 'picostrap5-child-base'), '<p class="text-end">', '</p>');

                    // If comments are open or we have at least one comment, load up the comment template.
                    if (!get_theme_mod("singlepost_disable_comments")) if (comments_open() || get_comments_number()) {
                        comments_template();
                    }

                    ?>

                </div><!-- /col -->
            </div>

            <div class="row">
                <div class="col-sm-12">
                    <div class="experience-box <?php echo get_field('sector_sub_sector') || (get_field('manual_sectors_list') && get_field('manual_sectors_list_data')) ? '' : 'd-none'; ?>">
                        <h5 class="title"><?php echo __('Relevant sectors', 'picostrap5-child-base'); ?> </h5>
                        <?php
                        if ($manual_sectors_list) : ?>
                            <ul>
                                <?php foreach ($manual_sectors_list_data as $_sector) : ?>
                                    <li>
                                        <?php if (!empty($_sector['url'])) : ?>
                                            <a href="<?php echo esc_url($_sector['url']); ?>"><?php echo esc_html($_sector['title']); ?></a>
                                        <?php else : ?>
                                            <?php echo esc_html($_sector['title']); ?>
                                        <?php endif; ?>
                                    </li>
                                <?php endforeach; ?>
                            </ul>
                            <?php else :
                            $sectors = get_field('sector_sub_sector');
                            $_sectors = wp_list_pluck($sectors, 'ID');

                            $args = [
                                'post_type'      => 'sector',
                                'posts_per_page' => -1,
                                'post_status'    => 'publish',
                                'post_parent'    => 0,
                                'post__in'       => $_sectors,
                                'orderby'        => 'title',
                                'order'          => 'ASC',
                            ];

                            $main_sectors = get_posts($args);

                            if (empty($main_sectors)) {
                                unset($args['post_parent']);
                                $main_sectors = get_posts($args);
                            }

                            if ($main_sectors) :
                                foreach ($main_sectors as $sector) :
                                    $has_parent = $sector->post_parent != 0;
                                    $has_page_parent = wp_get_post_parent_id(get_the_ID());
                            ?>
                                    <h6 class="subtitle">
                                        <?php if (!$has_page_parent) : ?>
                                            <a href="<?php echo get_permalink($sector); ?>"><?php echo esc_html($sector->post_title); ?></a>
                                        <?php else : ?>
                                            <?php echo esc_html($sector->post_title); ?>
                                        <?php endif; ?>
                                    </h6>
                                    <?php
                                    $sub_args = [
                                        'post_type'      => 'sector',
                                        'posts_per_page' => -1,
                                        'post_status'    => 'publish',
                                        'post_parent'    => $sector->ID,
                                        'post__in'       => $_sectors,
                                        'orderby'        => 'title',
                                        'order'          => 'ASC',
                                    ];

                                    $sub_sectors = get_posts($sub_args);

                                    if ($sub_sectors) :
                                        echo '<ul>';
                                        foreach ($sub_sectors as $sub) : ?>
                                            <li><?php echo esc_html($sub->post_title); ?></li>
                                <?php endforeach;
                                        echo '</ul>';
                                    endif;
                                endforeach;
                            else : ?>
                                <h6 class="subtitle"><?php echo esc_html__('No sectors listed.', 'picostrap5-child-base'); ?></h6>
                        <?php endif;
                        endif;
                        ?>
                    </div>
                </div>

            </div>

    <?php
    endwhile;
else :
    _e('Sorry, no posts matched your criteria.', 'picostrap5-child-base');
endif;
    ?>




    <?php get_footer();
