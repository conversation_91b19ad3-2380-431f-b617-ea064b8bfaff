<?php
/*
Template Name: Events Listing with Filter
*/

// Exit if accessed directly.
defined( 'ABSPATH' ) || exit;
$pll = pll_current_language();
get_header();

?>
    <!-- ========== banner-section start============= -->

    <div class="banner-section d-flex flex-column align-items-staer justify-content-center position-relative"
        style="background-image: url('<?php echo get_the_post_thumbnail_url(get_the_ID(), 'full'); ?>')">
        <div class="inner-overlay"></div>
        <!-- style="background-image:linear-gradient(to bottom, rgba(255,255,255,0.05) 0%, rgba(0,0,0,0.15) 50%), radial-gradient(at top center, rgba(255,255,255,0.20) 50%, rgba(0,0,0,0.40) 190%), url('<?php echo get_the_post_thumbnail_url(get_the_ID(), 'full'); ?>')"> -->
        <!-- style="background-image:linear-gradient(to bottom, rgba(255,255,255,0.05) 0%, rgba(0,0,0,0.15) 100%), radial-gradient(at top center, rgba(255,255,255,0.40) 0%, rgba(0,0,0,0.40) 120%), url('<?php echo get_the_post_thumbnail_url(get_the_ID(), 'full'); ?>')"> -->
        <div class="container text-center">
            <div class="banner-content">
                <h1 ><?php echo get_field('banner_title') ? get_field('banner_title') : get_the_title(); ?></h1>
            </div>
        </div>
    </div>

    <!-- ========== banner-section end============= -->

    <!-- ========== people details section start ============= -->

    <div class="people-section pt-60 pb-60">
        <div class="container-one">
            <div class="row">
                <div class="col-lg-6">
                    <div class="section-title-one style-yellow">
                        <h2 ><?php echo get_field('people_title'); ?></h2>
                        <div >
                            <p><?php echo get_field('people_description'); ?></p>
                        </div>
                    </div>
                </div>
                <div class="col-lg-6">
                    <div class="right-box">
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- ========== people details section end ============= -->

    <!-- <div class="filter-search-area pt-100 pb-100">        
        <div class="container-one" >
            <?php // echo do_shortcode('[tribe_community_events]'); ?>
        </div>
    </div> -->


    <!-- ========== team section start ============= -->

    <div class="team-section pt-100">
        <div class="container-one">
            <div class="section-title-two">
                <h4 ><?php echo __('Events', 'picostrap5-child-base'); ?></h4>
            </div>
            <div class="row" id="people-filter-search-list">
                <?php 
                    $events = tribe_get_events( [
                       'posts_per_page' => -1,
                       'orderby' => 'meta_value',
                       'meta_key' => '_EventStartDate',
                       'order' => 'DESC',
                    ] );

                    if(!empty($events)){
                        foreach ($events as $value) {
                            ?>
                                <div class="col-sm-12 mb-3" >
                                    <a hreflang="<?php echo esc_attr($pll); ?>" href="<?php echo get_the_permalink($value); ?>">
                                    <div class="sector-item-two style-blue">
                                        <h6 class="title"><?php echo get_the_title($value); ?></h6>
                                        <p><?php echo get_the_excerpt($value); ?></p>
                                        <span class="date"><?php echo tribe_get_start_date($value, $display_time = false, $date_format = 'd F Y'); ?></span>
                                        <div class="arrow-btn">
                                            <img src="<?php echo get_stylesheet_directory_uri(); ?>/assets/images/icons/arrow-white.svg" alt="arrow">
                                        </div>
                                    </div>
                                    </a>
                                </div>
                            <?php
                        }
                    }
                ?>
            </div>
        </div>
    </div>

    <!-- ========== team section end ============= -->

    <!-- ========== expertise-section start======== -->

    <div class="expertise-section-1">
        <div class="container-one">
            <div class="row justiy-content-start">
                <div class="col-lg-7">
                    <div class="section-title-one style-blue">
                        <h2 ><?php echo get_field('expertise_title'); ?></h2>
                        <p class="mb-45" ><?php echo get_field('expertise_description'); ?></p>
                        <?php 
                            $expertise_button = get_field('expertise_button');
                            if($expertise_button && isset($expertise_button['url'])){
                                ?>
                                    <a hreflang="<?php echo esc_attr($pll); ?>" href="<?php echo $expertise_button['url']; ?>" class="eg-btn btn--primary-blue btn--lg2"><?php echo $expertise_button['title']; ?> <i
                                            class="bi bi-arrow-right"></i></a>
                                <?php 
                            }
                        ?>
                    </div>
                </div>
                <div class="col-lg-5">
                    <div class="right-box-blue">

                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- ========== expertise-section end======== -->

    <div class="footer-top-design-one">
        <div class="box"></div>
    </div>


    <script type="text/javascript">
        jQuery(function($){
            var ajax_var = null;

            $(document).on('keyup', '#people-filter-search-form [name="name"]', function(e){
                $(this).closest('form').trigger('submit');
            });
            $(document).on('change', '#people-filter-search-form select[name="role"]', function(e){
                $(this).closest('form').trigger('submit');
            });
            $(document).on('change', '#people-filter-search-form select[name="location"]', function(e){
                $(this).closest('form').trigger('submit');
            });
            $(document).on('change', '#people-filter-search-form select[name="sector"]', function(e){
                $(this).closest('form').trigger('submit');
            });

            $(document).on('submit', '#people-filter-search-form', function(e){
                e.preventDefault();

                var this_form = $(this);
                var this_data = this_form.serialize();

                ajax_var = $.ajax({
                    beforeSend : function()    {          
                        if(ajax_var != null) {
                            ajax_var.abort();
                            ajax_var = null;
                        }
                    },
                    url: '<?php echo admin_url("admin-ajax.php"); ?>',
                    method: 'POST',
                    dataType: 'JSON',
                    data: this_data,
                    success: function(r){
                        // $(document).find('#people-filter-search-list').html(r.html);
                        var data = r.data;
                        let output = '';
                        data.forEach(function (item) {
                            output += `
                                <div class="col">
                                    <div class="team-item style-two style-two">
                                        <div class="team-image">
                                            <a href="${item.permalink}">
                                                <img src="${item.image}" alt="${item.title}">
                                            </a>
                                        </div>
                                        <div class="team-content">
                                            <h6><a href="${item.permalink}">${item.title}</a></h6>
                                            <span>${item.roles} </span>
                                            <span>${item.locations}</span>
                                        </div>
                                    </div>
                                </div>
                            `
                        });
                        document.getElementById('people-filter-search-list').innerHTML = output;
                    }
                });
            });
        });
    </script>

<?php 

get_footer();


