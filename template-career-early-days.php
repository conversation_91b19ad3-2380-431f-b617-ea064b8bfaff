<?php 
/*
    Template Name: Career Early Days Page
*/

// Exit if accessed directly.
defined( 'ABSPATH' ) || exit;
$pll = pll_current_language();
get_header();

?>



<!-- <div class="logo-section">
    <div class="container-one">
        <div class="row justify-content-start">
            <div class="col-lg-6">
                <div class="logo-area">
                    <img src="<?php //echo get_stylesheet_directory_uri(); ?>/assets/images/logo/header-logo.svg" alt="image">
                </div>
            </div>
        </div>
    </div>
</div> -->

    <!-- ========== header end============= -->

    <!-- ========== software-section start============= -->
    <h1 style="opacity:0; display: none;">hidden</h1>
    <div class="content-section pt-240">
        <div class="container-one">
            <div class="row">
                <div class="col-lg-6">
                    <div class="section-title-one style-blue title-pb-150">
                        <h2 ><?php echo get_field('page_title'); ?></h2>
                    </div>
                    <div class="eligibility-card style-blue">
                        <div >
                            <?php echo get_field('blue_box');?>
                            
                        </div>
                        
                    </div>
                </div>
                <div class="col-lg-6">
                    <div class="content" >
                        <?php echo get_field('content');?>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="tesitmonial-section">
        <div class="container-fluid">
            <div class="row">
                <div class="col-lg-6">
                    <div class="testimonial-image2 d-md-none d-lg-block" >
                        <img src="<?php echo get_field('blue_box_image');?>" alt="image">
                    </div>
                </div>
                <div class="col-lg-6">
                    <div class="testimonial-card">
                        <div >
                            <?php echo get_field('red_box');?>
                            
                        </div>
                        
                    </div>
                </div>
            </div>
        </div>
    </div>

    <?php $state_group = get_field('state_group'); ?>
    <?php $apply_now = get_field('apply_now'); ?>
    <div class="openday-section">
        <div class="container-fluid">
            <div class="row">
                <div class="col-lg-6">
                    <div class="attendents d-none" >
                        <h2><?php echo $state_group['number']; ?></h2>
                        <span><?php echo $state_group['content']; ?></span>
                    </div>
                </div>
                <div class="col-lg-6">
                    <div class="testimonial-image2 pt-30 d-lg-none" >
                        <img src="<?php echo get_field('blue_box_image');?>" alt="image">
                    </div>
                    <!-- <div class="box-right"></div> -->
                    <div class="openday-apply" >
                        <div class="apply-card style-yellow mt-0">
                            <h5><?php echo $apply_now['title']; ?></h5>
                            <div class="h-line"></div>
                            <a hreflang="<?php echo esc_attr($pll); ?>" href="<?php echo $apply_now['button']['url']; ?>" class="eg-btn btn--primary-black btn--lg"><?php echo $apply_now['button']['title']; ?></a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- ========== counter_section start======== -->
    <?php 
        $state_section= get_field('state_section');
        if(!empty($state_section['state_list'])) :
    ?>
    <div class="counter-section pt-100 pb-100">
        <div class="container-one">
        <!-- <div class="container-fluid"> -->
            <div class="row counter-row">
                <?php foreach($state_section['state_list'] as $value) : ?>
                <div class="col-lg-3 col-md-3 col-6 counter-item" >
                    <div class="counter-single style-green">
                        <h3 class="odometer" data-odometer-final="<?php echo $value['count_number']; ?>">01</h3>
                        <p><?php echo $value['content']; ?></p>
                    </div>
                </div>
                <?php endforeach; ?>
            </div>
        </div>
    </div>
    <?php endif; ?>
    <!-- ========== counter_section start======== -->


    <!-- ========== software-section end============= -->



<?php get_footer();