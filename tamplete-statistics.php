<?php
/**
 * Template Name: Statistics Page
 */
get_header();

    // Check if page is password protected
    if (post_password_required()) {
        ?>
        <div class="" style="padding: 240px 20px 20px 20px;"></div>
        <div class="container-one">
            <div class="row">
                <div class="col-lg-9">
                    <?php
                    echo get_the_password_form();
                    ?>
                </div>
            </div>
        </div>
        <?php
        
        get_footer();
        exit; // stop loading the rest of the page
    }

$statistics_monthly_data = get_field('statistics_monthly_data');
?>

<style>
    /* Your styles here (as before) */
    .statistic-section {
        padding: 60px 0;
        font-family: 'Courier New', Courier, monospace;
        background-color: #fff;
    }

    .statistic-section h2 {
        color: #000000;
        font-size: 24px;
        font-weight: 600;
    }

    .statistic-section ul {
        padding-left: 0;
        list-style: none;
    }

    .statistic-section ul li {
        margin-bottom: 8px;
        font-size: 16px;
    }

    .statistic-section ul li a {
        text-decoration: none;
        color: blue;
    }

    .statistic-section ul li a:hover {
        text-decoration: underline;
    }
</style>

<div class="banner-section d-flex flex-column align-items-staer justify-content-center position-relative"
    style="background-image:url('<?php echo get_the_post_thumbnail_url(get_the_ID(), 'full'); ?>')">
    <div class="inner-overlay"></div>
    <div class="container-one position-relative"></div>
</div>

<div class="banner-content banner-single-post-title title-bg-yellow">
    <div class="container-one">
        <h1 class="title-position-design text-white">
            <?php echo get_field('banner_title') ? esc_html(get_field('banner_title')) : get_the_title(); ?>
        </h1>
    </div>
</div>

<div class="statistic-section">
    <div class="container-one">
        <?php if (!empty($statistics_monthly_data)) : ?>
            <?php foreach ($statistics_monthly_data as $data) : ?>
                <div class="row mb-5">
                    <div class="col-lg-3 col-md-12">
                        <h2><?php echo esc_html($data['month_year']); ?></h2>
                    </div>
                    <div class="col-lg-9 col-md-12">
                        <ul>
                            <?php
                            if (!empty($data['upload_media'])) :
                                foreach ($data['upload_media'] as $media) :
                                    if (!empty($media['single_file'])) :
                                        $upload_url = esc_url($media['single_file']);
                                        $file_url = esc_url($media['file_url']);
                                        $file_name = basename($upload_url);
                            ?>
                                        <li>
                                            <a href="<?php echo $upload_url; ?>" target="_blank">
                                                <?php echo esc_html($file_name); ?>
                                            </a>
                                        </li>
                            <?php
                                    endif;
                                endforeach;
                            endif;
                            ?>
                        </ul>
                    </div>
                </div>
            <?php endforeach; ?>
        <?php else : ?>
            <p>No statistics available.</p>
        <?php endif; ?>
    </div>
</div>

<?php get_footer(); ?>
