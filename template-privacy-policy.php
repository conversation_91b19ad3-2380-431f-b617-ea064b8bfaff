<?php
/*
    Template Name: Privacy Policy
*/
// Exit if accessed directly.
defined( 'ABSPATH' ) || exit;
$pll = pll_current_language();
get_header();




if ( have_posts() ) : 
    while ( have_posts() ) : the_post();
    
    if (get_the_post_thumbnail_url()){ 
        ?><div class="d-flex container-fluid current-vacancies-banner" style="background:url(<?php echo get_the_post_thumbnail_url(); ?>)  center / cover no-repeat;"></div>
    <?php } else {
        ?><div class="d-flex container-fluid ht-cus-privary-policy"></div>
    <?php } ?>
    
    <!-- <div class="container p-5 bg-light" style="margin-top:-100px"> -->
    <div class="container-one" style="margin-top:-100px">

                <?php 
                //CATS
                if (!get_theme_mod("singlepost_disable_entry_cats") &&  has_category() ) {
                        ?>
                        <div class="row text-center">
                            <div class="col-md-12">
                                <div class="entry-categories">
                                    <span class="screen-reader-text"><?php _e( 'Categories', 'picostrap5-child-base' ); ?></span>
                                    <div class="entry-categories-inner">
                                        <?php the_category( ' ' ); ?>
                                    </div><!-- .entry-categories-inner -->
                                </div><!-- .entry-categories -->
                            </div><!-- /col -->
                        </div>
                        <?php
                }
                
                ?>

                <h1 class="display-4"><?php the_title(); ?></h1>
                
                <?php if (!get_theme_mod("singlepost_disable_date") OR !get_theme_mod("singlepost_disable_author")  ): ?>
                    <div class="post-meta" id="single-post-meta">
                        <p class="lead text-secondary">
                            
                            <?php if (!get_theme_mod("singlepost_disable_date") ): ?>
                                <!-- <span class="post-date"><?php the_date(); ?> </span> -->
                            <?php endif; ?>

                            <?php if (!get_theme_mod("singlepost_disable_author") ): ?>
                                <!-- <span class="text-secondary post-author"> <?php _e( 'by', 'picostrap5-child-base' ) ?> <?php the_author(); ?></span> -->
                            <?php endif; ?>
                        </p>
                    </div> 
                <?php endif; ?>


        <div class="row">
            <!-- <div class="col-md-8 offset-md-2"> -->
            <div class="col-md-12">
                <?php 
                
                the_content();
                
                if( get_theme_mod("enable_sharing_buttons")) picostrap5-child-base_the_sharing_buttons();
                
                edit_post_link( __( 'Edit this post', 'picostrap5-child-base' ), '<p class="text-end">', '</p>' );
                
                // If comments are open or we have at least one comment, load up the comment template.
                if (!get_theme_mod("singlepost_disable_comments")) if ( comments_open() || get_comments_number() ) {
                    comments_template();
                }
                
                ?>

            </div><!-- /col -->
        </div>
    </div>

    <?php 
    $refunds_policy = get_field('refunds_policy');
    ?>
    <?php if(!empty($refunds_policy)) :?>
    <div class="refunds-policy">
        <div class="container-one">
            <div class="row">
                <div class="col-md-12">
                <?php echo $refunds_policy;?>
                </div>
            </div>
        </div>
    </div>
    <?php endif; ?>
<?php
    endwhile;
 else :
     _e( 'Sorry, no posts matched your criteria.', 'picostrap5-child-base' );
 endif;
 ?>
<?php get_footer();
