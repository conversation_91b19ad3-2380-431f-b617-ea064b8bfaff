<?php 
/*
	Template Name: Search Page
*/
get_header();
?>
<div class="py-6 bg-light">
    <div class="container text-center">
        <h1 class="display-4"><?php the_title(); ?></h1>
        
  </div>
</div>

<div class="filter-search-area pt-100 pb-100">        
    <div class="container-one" >
        <form class="filter-search-form search-inpage-filter" id="search-page-filter-search-form" method="post" action="">
            <!-- <div class="row row-cols-lg-3 row-cols-md-3 row-cols-sm-2 row-cols-2 justify-content-start g-3"> -->
            <div class="row">
                <div class="col-md-5">
                    <div class="form-inner">
                        <input type="text" name="keyword" placeholder="<?php echo __('Search', 'picostrap5-child-base'); ?>">
                    </div>
                </div>
                <div class="col-md-5">
                    <div class="form-inner">
                        <select name="type">
                            <option value="all"><?php echo __('All', 'picostrap5-child-base'); ?></option>
                            <option value="people"><?php echo __('People', 'picostrap5-child-base'); ?></option>
                            <option value="expertise"><?php echo __('Expertise', 'picostrap5-child-base'); ?></option>
                            <option value="articles"><?php echo __('Articles', 'picostrap5-child-base'); ?></option>
                        </select>
                    </div>
                </div>
                <div class="col-md-2">
                    <div class="form-inner">
                        <input type="submit" value="">
                        <i class="bi bi-search"></i>
                        <input type="hidden" name="action" value="search_page_filter_search_action">
                    </div>
                </div>
            </div>
        </form>
    </div>
</div>

<div class="team-section pt-100">
    <div class="container-one">
        <div id="search-filter-search-list">

        </div>
    </div>
</div>


<script type="text/javascript">
    jQuery(function($){
        var main_search_ajax = null;
        var ajax_loader_html = '<div class="main-search-ajax-loader text-black"><i class="fa fa-spinner fa-spin"></i></div>';

        $(document).on('keyup', '#search-page-filter-search-form input[name="keyword"]', function(e){
            e.preventDefault();
            $(this).closest('form').trigger('submit');
        });
        $(document).on('change', '#search-page-filter-search-form select[name="type"]', function(e){
            e.preventDefault();
            $(this).closest('form').trigger('submit');
        });

        $(document).on('submit', '#search-page-filter-search-form', function(e){
            e.preventDefault();

            var this_form = $(this);
            var this_data = this_form.serialize();
            var keyword = $.trim(this_form.find('input[name="keyword"]').val());

            main_search_ajax = $.ajax({
                beforeSend : function()    {
                    if(main_search_ajax != null) {
                        main_search_ajax.abort();
                        main_search_ajax = null;
                    }

                    $('#search-filter-search-list').empty().html( ajax_loader_html );
                },
                url: '<?php echo admin_url("admin-ajax.php"); ?>', // AJAX handler
                data: this_data,
                type: 'POST',
                success: function (data) {
                    if(keyword){
                        $('#search-filter-search-list').empty().html( data );
                    }
                    else{
                        $('#search-filter-search-list').empty();
                    }
                }
            });

        });
    });

</script>


<?php get_footer();




// file ends here