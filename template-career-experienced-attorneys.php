<?php 
/*
    Template Name: Career Experienced Attorneys Page
*/

// Exit if accessed directly.
defined( 'ABSPATH' ) || exit;
$pll = pll_current_language();
get_header();

?>



<!-- <div class="logo-section">
    <div class="container-one">
        <div class="row justify-content-start">
            <div class="col-lg-6">
                <div class="logo-area">
                    <img src="<?php //echo get_stylesheet_directory_uri(  );?>/assets/images/logo/header-logo.svg" alt="image">
                </div>
            </div>
        </div>
    </div>
</div> -->

<!-- ========== header end============= -->

<!-- ========== banner-section start============= -->

<div class="banner-section d-flex flex-column align-items-staer justify-content-center position-relative"
    style="background-image:url('<?php echo get_the_post_thumbnail_url(get_the_ID(), 'full'); ?>')">
    <div class="inner-overlay"></div>
    <!-- style="background-image:linear-gradient(to bottom, rgba(255,255,255,0.05) 0%, rgba(0,0,0,0.15) 50%), radial-gradient(at top center, rgba(255,255,255,0.20) 50%, rgba(0,0,0,0.40) 190%), url('<?php echo get_the_post_thumbnail_url(get_the_ID(), 'full'); ?>')"> -->
    <!-- style="background-image:linear-gradient(to bottom, rgba(255,255,255,0.05) 0%, rgba(0,0,0,0.15) 100%), radial-gradient(at top center, rgba(255,255,255,0.40) 0%, rgba(0,0,0,0.40) 120%), url('<?php echo get_the_post_thumbnail_url(get_the_ID(), 'full'); ?>')"> -->
    <div class="container-one text-center">
        <div class="banner-content">
            <h1 ><?php echo get_field('page_title') ? get_field('page_title') : get_the_title(); ?></h1>
        </div>
    </div>
</div>

<!-- ========== banner-section end============= -->

<!-- ========== software-section start============= -->
<?php 

?>
<div class="content-section pt-150 new-section-top-index">
    <div class="container-one">
        <div class="row">
            <div class="col-lg-6 pe-lg-5">
                <div class="section-title-one style-blue title-pb-150 d-none">
                    <h2 ><?php echo get_field('page_title'); ?></h2>
                </div>
                <?php 
                    $show_image_content_instead = get_field('show_image_content_instead');
                    if($show_image_content_instead){
                        ?>
                            <div class="business-parnter-single style-blue">
                                <div class="image-wrap" >
                                    <img src="<?php echo get_field('person_image')['url']; ?>" alt="image">
                                </div>
                                <div class="content" >
                                    <?php echo get_field('person_content'); ?>
                                </div>
                            </div>
                        <?php 
                    }
                    else{
                        $select_people = get_field('select_people');
                            if(!empty($select_people)):
                                $roles = get_the_terms( $select_people->ID, 'people_role' );
                                $roles = join(', ', wp_list_pluck($roles, 'name'));

                                $locations = get_the_terms( $select_people->ID, 'location' );
                                $locations = join(', ', wp_list_pluck($locations, 'name'));
                        ?>
                        <div class="business-parnter-single style-blue">
                            <div class="image-wrap" >
                                <img src="<?php echo get_field('person_photo_1', $select_people->ID) ? get_field('person_photo_1', $select_people->ID)['sizes']['medium_large'] : get_stylesheet_directory_uri() . '/assets/images/team/team.jpg'; ?>" alt="<?php echo get_the_title($select_people->ID); ?>">
                            </div>
                            <div class="content" >
                                <h6><?php echo get_the_title($select_people->ID); ?></h6>
                                <span><?php echo $roles;?></span>
                                <span><?php echo $locations;?></span>
                            </div>
                        </div>
                        <?php endif;
                    }

                ?>

                <!-- ========== recent-highlightsection start ============= -->

                <div class="recent-highlight pt-100 <?php echo get_field('quotes') || get_field('recent_highlights') ? '' : 'd-none'; ?>">
                    <div class="container-one">
                        <div class="row">
                            <div class="col-lg-12">
                                <div class="recent-highlight-quotes">
                                    <div class="box-with-border-inner"></div>
                                    <div class="box-inner-content">
                                        <?php echo get_field('quotes'); ?>
                                        <?php 
                                            $quote_person_image = get_field('quote_person_image');
                                            $person_image = get_stylesheet_directory_uri() . '/assets/images/team/team.jpg';
                                            if($quote_person_image){
                                                $roles = get_the_terms( $quote_person_image->ID, 'people_role' );
                                                $roles = join(', ', wp_list_pluck($roles, 'name'));

                                                $person_image = get_field('person_photo_1', $quote_person_image->ID) ? get_field('person_photo_1', $quote_person_image->ID)['sizes']['medium_large'] : get_stylesheet_directory_uri() . '/assets/images/team/team.jpg';
                                                ?>
                                                    <div class="team-content quote-inner">
                                                        <h6><a hreflang="<?php echo esc_attr($pll); ?>" href="<?php echo get_the_permalink($quote_person_image); ?>"><?php echo get_the_title($quote_person_image); ?></a></h6>
                                                        <span><?php echo strtoupper($roles); ?></span>
                                                    </div>
                                                <?php 
                                            }

                                            if(strpos($person_image, 'team/team.jpg') !== false){
                                                $quote_person_image_custom = get_field('quote_person_image_custom');
                                                if($quote_person_image_custom){
                                                    $person_image = $quote_person_image_custom['url'];
                                                }
                                            }
                                        ?>
                                    </div>
                                    <div class="box-inner-image">
                                        <img src="<?php echo $person_image; ?>" alt="image">
                                    </div>
                                </div>
                                <!-- <div class="section-title-one style-yellow">
                                    <div class="box-with-border p-3" >
                                        <?php // echo get_field('quotes'); ?>
                                    </div>
                                </div> -->

                                <div class="recent-highlight-inner <?php echo get_field('recent_highlights') ? '' : 'd-none'; ?>">
                                    <h2 ><?php echo __('Recent highlights', 'picostrap5-child-base'); ?><span class="border-bar"></span></h2>
                                    <div>
                                        
                                    <?php 
                                        echo get_field('recent_highlights');
                                    ?>
                                    </div>
                                </div>
                            </div>
                            <!-- <div class="col-lg-6">
                                <div >
                                    
                                    
                                </div>
                            </div> -->
                        </div>
                    </div>
                </div>

                <!-- ========== recent-highlight section end ============= -->
                <div class="attorny-left-image" >
                    <img src="<?php echo get_field('image'); ?> " alt="image">
                </div>

            </div>
            <div class="col-lg-6">
                <div class="content-italic pb-100" >
                    <?php echo get_field('content'); ?> 
                </div>
            </div>
        </div>
    </div>
</div>

<!-- ========== software-section end============= -->

<?php 
    $skills_card = get_field('skills_card');
?>
<!-- ========== facilities-section start============= -->

<div class="facilities-section">
    <div class="container-one">
        <div class="row align-items-center">
            <div class="col-lg-6">
                <div class="expertise-section-1">
                    <div class="container-one">
                        <div class="row justiy-content-start">
                            <div class="col-lg-12">
                                <div class="section-title-one style-blue">
                                    <h2 ><?php echo get_field('expertise_title'); ?></h2>
                                    <!-- <p class="mb-45" ><?php echo get_field('expertise_description'); ?></p> -->
                                </div>
                                <div >
                                    <?php echo get_field('expertise_description'); ?>
                                    <?php 
                                        $expertise_button = get_field('expertise_button');
                                        if($expertise_button && isset($expertise_button['url'])){
                                            ?>
                                                <a hreflang="<?php echo esc_attr($pll); ?>" href="<?php echo $expertise_button['url']; ?>" class="eg-btn btn--primary-blue btn--lg2"><?php echo $expertise_button['title']; ?> <i
                                                        class="bi bi-arrow-right"></i></a>
                                            <?php 
                                        }
                                    ?>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-lg-6" >
                <?php echo get_field('facilities_content'); ?> 

                <div class="recent-highlight-quotes">
                    <div class="box-with-border-inner"></div>
                    <div class="box-inner-content">
                        <?php echo $skills_card['quotes']; ?>
                        <?php 
                            $quote_person_image = $skills_card['quote_person_image'];
                            $person_image = get_stylesheet_directory_uri() . '/assets/images/team/team.jpg';
                            if($quote_person_image){
                                $roles = get_the_terms( $quote_person_image->ID, 'people_role' );
                                $roles = join(', ', wp_list_pluck($roles, 'name'));

                                $person_image = get_field('person_photo_1', $quote_person_image->ID) ? get_field('person_photo_1', $quote_person_image->ID)['sizes']['medium_large'] : get_stylesheet_directory_uri() . '/assets/images/team/team.jpg';
                                ?>
                                    <div class="team-content quote-inner">
                                        <h6><a hreflang="<?php echo esc_attr($pll); ?>" href="<?php echo get_the_permalink($quote_person_image); ?>"><?php echo get_the_title($quote_person_image); ?></a></h6>
                                        <span><?php echo strtoupper($roles); ?></span>
                                    </div>
                                <?php 
                            }
                        ?>
                    </div>
                    <div class="box-inner-image">
                        <img src="<?php echo $person_image; ?>" alt="image">
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- ========== facilities-section start============= -->

<!-- ========== search section start ============= -->

<div class="search-section style-blue mt-100 mb-100 d-none" >
    <div class="container">
        <form method="get" action="" id="home-page-banner-search-form">
            <div class="search-block">
                <button type="search"><i class="bi bi-search"></i></button>
                <input type="text" placeholder="<?php echo __('SEARCH OUR TEAM', 'picostrap5-child-base'); ?>" name="keyword">
            </div>
            <div class="search-block bg-black p-0">
                <div class="home-page-banner-search-list" id="home-page-banner-search-list"></div>
            </div>
        </form>
    </div>
</div>

<!-- ========== search section start ============= -->

<div class="skill-section pt-100 <?php echo empty($skills_card['skills_list']) ? 'd-none' : ''; ?>">
    <div class="skill-box-design"></div>
    <div class="container-one">
        <div class="section-title-one style-blue">
            <h2 ><?php echo $skills_card['title']; ?></h2>
        </div>
        <div class="row g-lg-4 g-2">
            <?php foreach($skills_card['skills_list'] as $value) : ?>
            <div class="col-lg-4 col-6" >
                <div class="skill-card">
                    <div class="content">
                        <h5><?php echo $value['title']; ?> </h5>
                        <p><?php echo $value['content']; ?></p>
                    </div>
                    <div class="icon">
                        <?php echo $value['image_svg_code']; ?>
                    </div>
                </div>
            </div>
            <?php endforeach; ?>
        </div>
    </div>
</div>

<?php 
    $people_quote = get_field('people_quote');
?>
<div class="testimonial-section pr-container mb-100 <?php echo $people_quote ? '' : 'd-none'; ?>">
    <div class="testi-top-image" >
        <!-- <img src="<?php echo $skills_card['image']['url']; ?>" alt="image"> -->
        
    </div>
    <div class="container-fluid mt-100">
        <?php 
            foreach($people_quote as $value) :
        ?>
        <div style="background-color: <?php echo $value['background_color']; ?>" class="big-card testimonial-single style-yellow mb-90">
            <div class="content" >
                <div class="title">
                    <h4><?php echo $value['title']; ?></h4>
                </div>
                <div style="border-color: <?php echo $value['border_color']; ?>" class="body">
                    <?php echo $value['content']; ?>
                </div>
            </div>
            <?php 
                $thum_img = get_field('person_photo_1', $value['select_people']->ID);
                $role =  get_the_terms( $value->ID, 'people_role' );
                $location =  get_the_terms( $value->ID, 'location' );
            ?>
            <?php if(!get_field('hide_person_photo')): ?>
                <div class="author" >
                    <?php if(!empty($thum_img)) :?>
                        <div class="author-img">
                            <img src="<?php echo $thum_img['url']; ?>" alt="image">
                        </div>
                        <?php else: ?>
                        <?php 
                        $classes = array('bg-primary-red', 'bg-primary-green', 'bg-primary-yellow', 'bg-primary-blue');
                        shuffle($classes);
                        $c = array_rand($classes, 1);
                        ?>
                        <div class="no-person-image-box <?php echo $classes[$c]; ?>"></div>
                    <?php endif; ?>
                    <h6><?php echo get_the_title($value['select_people']->ID);?></h6>
                    <span>
                        <?php 
                            if(!empty($role)){
                                echo strtoupper($role[0]->name);
                            }
                        ?>
                    </span>
                    <span>
                        <?php 
                            if(!empty($location)){
                                echo strtoupper($location[0]->name);
                            }
                        ?>
                    </span>
                    <span><?php echo strtoupper($value['people_text']); ?></span>
                </div>
            <?php endif; ?>
        </div>
        <?php endforeach; ?>
       
    </div>
</div>


<!-- ========== expertise-section start======== -->

   

    <!-- ========== expertise-section end======== -->




<?php get_footer();