<?php

// Exit if accessed directly.
defined('ABSPATH') || exit;


$pll = pll_current_language();


$trans['people'] = 'People';
$trans['expertise'] = 'Expertise';
$trans['insights'] = 'Insights';
$trans['phone'] = '+44 (0)20 7430 7500';
$trans['email'] = '<EMAIL>';

if ($pll == 'es') {
  $trans['people'] = 'Equipo';
  $trans['expertise'] = 'Experiencia';
  $trans['insights'] = 'Actualidad';
  $trans['phone'] = '+34 (0)919 269 970';
  $trans['email'] = '<EMAIL>';
} elseif ($pll == 'de') {
  $trans['phone'] = '+49 (0)69 506 08 733';
  $trans['email'] = '<EMAIL>';
}
?>

<?php
$custom_logo_id = get_theme_mod('custom_logo');
$logo_url = wp_get_attachment_image_src($custom_logo_id, 'full');


// Retrive slug 
$current_url = $_SERVER["REQUEST_URI"];
$url_parts = explode('/', $current_url);
$first_slug = '';
if (!empty($url_parts[1])) {
  $first_slug = $url_parts[1];
}
$home_url = home_url('/');
if ($first_slug == 'de') {
  $home_url = site_url() . '/' . 'ger';
}

?>

<!doctype html>
<html <?php language_attributes(); ?>>

<head>
  <!-- Required meta tags -->
  <meta charset="<?php bloginfo('charset'); ?>">
  <meta name="viewport" content="width=device-width, initial-scale=1">
  <!-- Google Tag Manager -->
  <script>
    (function(w, d, s, l, i) {
      w[l] = w[l] || [];
      w[l].push({
        'gtm.start': new Date().getTime(),
        event: 'gtm.js'
      });
      var f = d.getElementsByTagName(s)[0],
        j = d.createElement(s),
        dl = l != 'dataLayer' ? '&l=' + l : '';
      j.async = true;
      j.src =
        'https://www.googletagmanager.com/gtm.js?id=' + i + dl;
      f.parentNode.insertBefore(j, f);
    })(window, document, 'script', 'dataLayer', 'GTM-P3ZDSJTT');
  </script>
  <!-- End Google Tag Manager -->
  <!-- wp_head begin -->
  <?php wp_head(); ?>
  <!-- wp_head end -->
</head>

<body <?php body_class(); ?>>
  <?php wp_body_open(); ?>

  <!-- Google Tag Manager (noscript) -->
  <noscript><iframe src="https://www.googletagmanager.com/ns.html?id=GTM-P3ZDSJTT"
      height="0" width="0" style="display:none;visibility:hidden"></iframe></noscript>
  <!-- End Google Tag Manager (noscript) -->
  <!-- =============== search-area start =============== -->

  <div class="mobile-search">
    <div class="container-one">
      <form id="searchForm" method="get" action="<?php echo site_url('/'); ?>">
        <div class="row d-flex justify-content-center">
          <div class="col-11 col-sm-11 col-md-11">
            <label><?php echo __('What are you looking for?', 'picostrap5-child-base'); ?></label>
            <input type="text" id="main_search" placeholder="<?php echo __('Search', 'picostrap5-child-base'); ?>">
            <button type="reset" class="d-none">Reset</button>
          </div>
          <div class="col-1 d-flex justify-content-end align-items-center gap-2">
            <div class="search-cross-btn search-btnn" id="searchButton">
              <i class='bx bx-search-alt-2'></i>
            </div>
            <div class="search-cross-btn search-close-btn">
              <i class="bi bi-x"></i>
            </div>
          </div>
          <div class="col-md-12">
            <div class="search-option-filters">
              <ul>
                <li>
                  <label>
                    <input type="radio" name="type" value="all" checked> <span><?php echo __('All', 'picostrap5-child-base'); ?></span>
                  </label>
                </li>
                <li>
                  <label>
                    <input type="radio" name="type" value="people"> <span><?php echo __($trans['people'], 'picostrap5-child-base'); ?></span>
                  </label>
                </li>
                <li>
                  <label>
                    <input type="radio" name="type" value="expertise"> <span><?php echo __($trans['expertise'], 'picostrap5-child-base'); ?></span>
                  </label>
                </li>
                <li>
                  <label>
                    <input type="radio" name="type" value="articles"> <span><?php echo __($trans['insights'], 'picostrap5-child-base'); ?></span>
                  </label>
                </li>
              </ul>
            </div>
          </div>
          <div class="col-md-12">
            <div id="filtered-data">

            </div>
          </div>
        </div>
      </form>
    </div>
  </div>

  <!-- =============== search-area end  =============== -->


  <!-- ========== header============= -->

  <header class="style-1">
    <!-- <div class="container-fluid position-relative  d-flex justify-content-between  align-items-center"> -->

    <div class="container" style="max-width: 1700px;">
      <div class="position-relative  d-flex justify-content-between  align-items-center" style="padding: 20px 0;">
        <div class="logo-area">
          <!-- <a href="index.html"><img src="assets/images/logo/header-logo.svg" alt="image"></a> -->
          <?php if (!empty($logo_url[0])) : ?>
            <a href="<?php echo esc_url($home_url); ?>" rel="home">
              <img src="<?php echo esc_url($logo_url[0]); ?>" alt="<?php bloginfo('name'); ?>">
            </a>
          <?php endif ?>
          <div class="breadcrumps-wrap">

            <?php
            $upc_link = '';
            $upc_text = '';
            $sector_link = '';
            $sector_text = '';
            $service_link = '';
            $service_text = '';

            $news_link = '';
            $bulletin_link = '';
            $bulletin_text = '';
            if ($pll == 'es') {
              $upc_link = "/es/tpu/";
              $upc_text = "TPU";

              $sector_link = "/expertise/#sectors";
              $sector_text = "Sectores";

              $service_link = "/es/expertise/#services";
              $service_text = "Servicios";

              $news_link = '/es/noticia/';
              $bulletin_link = '/es/bulletins2/';
              $bulletin_text = 'Boletines';
            } elseif ($pll == 'de') {
              $upc_link = "/de/upc/";
              $upc_text = "UPC";

              $sector_link = "/de/expertise/#sectors";
              $sector_text = "Sector";

              $service_link = "/de/expertise/#services";
              $service_text = "Service";

              $news_link = '/all-news/';

              $bulletin_link = '/de/bulletins3/';
              $bulletin_text = 'Bulletins';
            } else {
              $upc_link = "/upc/";
              $upc_text = "UPC";

              $sector_link = "/expertise/#sectors";
              $sector_text = "Sector";

              $service_link = "/expertise/#services";
              $service_text = "Service";

              $news_link = '/all-news/';

              $bulletin_link = '/bulletins/';
              $bulletin_text = 'Bulletins';
            }
            if (is_single() && 'upc_news' == get_post_type()) {
            ?>
              <span typeof="v:Breadcrumb"><a rel="v:url" property="v:title" title="<?php echo $upc_text; ?>" href="<?php echo $upc_link; ?>"><?php echo $upc_text; ?></a></span> »
              <span typeof="v:Breadcrumb"><span property="v:title"><?php the_title(); ?></span></span>
            <?php
            } elseif (
              get_the_ID() === 3048 ||
              get_the_ID() === 636 ||
              get_the_ID() === 639 ||
              get_the_ID() === 651 ||
              get_the_ID() === 645 ||
              get_the_ID() === 654 ||
              get_the_ID() === 1233 ||
              get_the_ID() === 9022 ||
              get_the_ID() === 9076 ||
              get_the_ID() === 9080 ||
              get_the_ID() === 9094 ||
              get_the_ID() === 9078 ||
              get_the_ID() === 8268 ||
              get_the_ID() === 8250 ||
              get_the_ID() === 10028 ||
              get_the_ID() === 8270 ||
              get_the_ID() === 8260
            ) {
            ?>
              <span typeof="v:Breadcrumb"><a rel="v:url" property="v:title" title="Careers" href="/careers/">Careers</a></span> »
              <span typeof="v:Breadcrumb"><span property="v:title"><?php the_title(); ?></span></span>
            <?php
            } elseif (is_single() && 'people' == get_post_type()) {
            ?>
              <span typeof="v:Breadcrumb"><a rel="v:url" property="v:title" title="People" href="/people/">People</a></span> »
              <span typeof="v:Breadcrumb"><span property="v:title"><?php the_title(); ?></span></span>
            <?php
            } elseif (is_single() && 'sector' == get_post_type()) {
            ?>
              <span typeof="v:Breadcrumb"><a rel="v:url" property="v:title" title="<?php echo $sector_text; ?>" href="<?php echo $sector_link; ?>"><?php echo $sector_text; ?></a></span> »
              <span typeof="v:Breadcrumb"><span property="v:title"><?php the_title(); ?></span></span>
            <?php
            } elseif (is_single() && 'service' == get_post_type()) {
            ?>
              <span typeof="v:Breadcrumb"><a rel="v:url" property="v:title" title="<?php echo $service_text; ?>" href="<?php echo $service_link; ?>"><?php echo $service_text; ?></a></span> »
              <span typeof="v:Breadcrumb"><span property="v:title"><?php the_title(); ?></span></span>
            <?php
            } elseif (is_single() && 'news' == get_post_type()) {
            ?>
              <span typeof="v:Breadcrumb"><a rel="v:url" property="v:title" title="News" href="<?php echo $news_link; ?>">News</a></span> »
              <span typeof="v:Breadcrumb"><span property="v:title"><?php the_title(); ?></span></span>
            <?php
            } elseif (is_single() && 'bulletin' == get_post_type()) {
            ?>
              <span typeof="v:Breadcrumb"><a rel="v:url" property="v:title" title="<?php echo $bulletin_text; ?>" href="<?php echo $bulletin_link; ?>"><?php echo $bulletin_text; ?></a></span> »
              <span typeof="v:Breadcrumb"><span property="v:title"><?php the_title(); ?></span></span>
            <?php
            } elseif (is_single() && 'ip_basics' == get_post_type()) {
            ?>
              <span typeof="v:Breadcrumb"><a rel="v:url" property="v:title" title="IP Basics" href="#">IP Basics</a></span> »
              <span typeof="v:Breadcrumb"><span property="v:title"><?php the_title(); ?></span></span>
            <?php
            } elseif (is_single() && 'patent_essential' == get_post_type()) {
            ?>
              <span typeof="v:Breadcrumb"><a rel="v:url" property="v:title" title="Patent Essential" href="#">Patent Essential</a></span> »
              <span typeof="v:Breadcrumb"><span property="v:title"><?php the_title(); ?></span></span>
            <?php
            } else {
              if (function_exists('yoast_breadcrumb')) {
                yoast_breadcrumb('<p id="breadcrumbs">', '</p>');
                if ($first_slug) {
                  echo "/" . $first_slug;
                }
              }
            }
            ?>
          </div>
          <?php if (is_single() && 'news' == get_post_type() || is_single() && 'bulletin' == get_post_type()) :  ?>
            <div class="breadcrumb-news-buletins">
              <ul class="banner-breadcrumb">
                <?php if ('news' == get_post_type()) :  ?>
                  <?php if ($pll == 'es') : ?>
                    <li><a href="<?php echo home_url() . '/actualidad/'; ?>">Regresa</a></li>
                  <?php else : ?>
                    <li><a href="<?php echo home_url() . '/insights'; ?>">Back to Insights</a></li>
                  <?php endif ?>
                <?php else : ?>
                  <?php if ($pll == 'es') : ?>
                    <li><a href="<?php echo home_url() . '/bulletins2/'; ?>">Regresa</a></li>
                  <?php else : ?>
                    <li><a href="<?php echo home_url() . '/bulletins'; ?>">Back to Bulletins</a></li>
                  <?php endif ?>
                <?php endif ?>
              </ul>
            </div>
          <?php endif ?>
          <?php  /*
                <?php 
                    $homeTitle = get_field('banner_section', 666);
                    $peopleTitle = get_field('banner_title', 416);
                    $responsibleTitle = get_field('banner_title', 8503);
                   
                ?>
                <?php  if(is_page(666)){ ?>
                  <h4><?php echo $homeTitle['banner_image'][0]['title']; ?></h4>
                <?php } ?>
                <?php  if(is_page(416)){ ?>
                  <h4><?php echo $peopleTitle; ?></h4>
                <?php } ?>
                <?php  if(is_page(8503)){ ?>
                  <h4><?php echo $responsibleTitle; ?></h4>
                <?php } ?> */ ?>
        </div>
        <div class="new-header-right d-flex align-items-center gap-3">
          <div class="header-icons d-lg-flex d-none justify-content-start" style="position: relative; top: unset; right: unset; max-width: 260px;">
            <ul>
              <li>
                <a href="<?php echo get_permalink(pll_get_post(1121/*get_field('locations_page_link', 'option')*/)); ?>">
                  <i class="bi bi-geo-alt"></i>
                </a>
              </li>
              <li><i class="bi bi-search search-btn"></i></li>
              <li><a class="contact-button" href="mailto:<?php echo __($trans['email'], 'picostrap5-child-base'); ?>"><i class="bi bi-envelope"></i></a></li>
              <li><a class="contact-info" href="tel:<?php echo __($trans['phone'], 'picostrap5-child-base'); ?>"><i class="bi bi-telephone"></i> <span class="d-xl-flex d-none"><?php echo __($trans['phone'], 'picostrap5-child-base'); ?></span> </a></li>
            </ul>
            <div class="flag-img">
              <?php
              $url = $_SERVER['REQUEST_URI'];
              $urlExplode = explode('/', $url);

              $pll = pll_current_language();
              if ($urlExplode[1] == 'en') {
              ?>
                <img src="<?php echo get_stylesheet_directory_uri() . '/assets/images/flag/en.png'; ?>" alt="">
              <?php
              } elseif ($urlExplode[1] == 'de') {
              ?>
                <img src="<?php echo get_stylesheet_directory_uri() . '/assets/images/flag/german.png'; ?>" alt="">
              <?php
              } elseif ($urlExplode[1] == 'es') {
              ?>
                <img src="<?php echo get_stylesheet_directory_uri() . '/assets/images/flag/es.png'; ?>" alt="">
              <?php
              } elseif ($urlExplode[1] == 'jp') {
              ?>
                <img src="<?php echo get_stylesheet_directory_uri() . '/assets/images/flag/jp.png'; ?>" alt="">
              <?php
              } elseif ($urlExplode[1] == 'kr') {
              ?>
                <img src="<?php echo get_stylesheet_directory_uri() . '/assets/images/flag/kr.png'; ?>" alt="">
              <?php
              } elseif ($urlExplode[1] == 'cn') {
              ?>
                <img src="<?php echo get_stylesheet_directory_uri() . '/assets/images/flag/cn.png'; ?>" alt="">
              <?php
              } else {
              ?>
                <img src="<?php echo get_stylesheet_directory_uri() . '/assets/images/flag/en.png'; ?>" alt="">
              <?php
              }
              ?>
            </div>
          </div>
          <style>
            .dropdown-header-area {
              position: relative;
            }

            .dropdown-header-area .mobile-menu-btn i {
              font-size: 30px;
              /* color: rgb(227, 6, 19) !important; */
              cursor: pointer;
            }

            .dropdown-header-area .dropdown-item {
              padding: 20px;
              background-color: #464646;
              width: 260px;
              position: absolute;
              top: 50px;
              right: 29px;
              transform: scaleY(0);
              transform-origin: top;
              opacity: 0;
              transition: 0.5s;
            }

            .dropdown-header-area .dropdown-item.active {
              transform: scaleY(1);
              opacity: 1;
            }

            .dropdown-header-area .dropdown-item .main-nav>ul {
              padding-right: 0 !important;
              width: 100%;
            }

            .dropdown-header-area .dropdown-item .main-nav>ul>li {
              display: block;
              padding: 0;
            }

            .dropdown-header-area .dropdown-item .main-nav>ul>li:first-child a {
              padding-top: 0;
            }

            .dropdown-header-area .dropdown-item .main-nav>ul>li a {
              padding: 10px 0;
              text-align: start;
              color: #fff;
              border-bottom: 1px solid #8b8888;
            }

            .dropdown-header-area .dropdown-item .main-nav>ul>li a::after {
              content: "";
              position: absolute;
              bottom: -1px;
              left: 0;
              background: linear-gradient(90deg, #626262, #fff, #626262);
              width: 100%;
              height: 1px;
              transform: scaleX(0);
              transform-origin: left;
              transition: 0.4s ease-in;
              z-index: 1;
            }

            .dropdown-header-area .dropdown-item .main-nav>ul>li a:hover::after {
              transform: scale(1);
            }

            .dropdown-header-area .dropdown-item .main-nav>ul>li a.active::after {
              transform: scale(1);
            }

            .dropdown-header-area .dropdown-item .main-nav>ul>li a:hover {
              color: #fff;
            }

            .dropdown-header-area .dropdown-item .main-nav>ul>li a.active {
              color: #fff !important;
              position: relative;
              display: block !important;
            }

            .dropdown-header-area .dropdown-item .main-nav>ul>li a.active::after {
              transform: scale(1);
            }

            .dropdown-header-area .dropdown-item ul.language-list {
              padding-right: 0;
              padding-top: 20px;
            }

            .dropdown-header-area .dropdown-item ul.language-list>li a {
              color: #fff;
              padding: 0;
            }

            .dropdown-header-area .dropdown-item ul.language-list>li::after {
              background-color: #fff;
              top: 5px;
              right: -2px;
              width: 1px;
              height: 10px;
            }

            .dropdown-header-area .dropdown-item .main-nav ul li.menu-item-has-children {
              position: relative;
            }

            .dropdown-header-area .dropdown-item .main-nav>ul li.menu-item-has-children>i {
              display: block;
              opacity: 1;
              color: #fff;
              top: 7px;
              right: -9px;
            }

            .dropdown-header-area .dropdown-item .main-nav>ul>li ul.sub-menu>li i {
              position: absolute;
              top: 8px;
              right: -9px;
              display: block;
              color: rgb(255 255 255);
              font-size: 12px;
            }

            .dropdown-header-area .dropdown-item .main-nav>ul li.menu-item-has-children>i::before {
              content: "＋";
            }

            header.style-1 .main-nav>ul>li ul.sub-menu>li i::before {
              content: "＋";
            }

            .dropdown-header-area .dropdown-item .main-nav>ul li.menu-item-has-children>i.active::before {
              content: "ー";
            }

            header.style-1 .main-nav>ul>li ul.sub-menu>li i.active::before {
              content: "ー";
            }

            .dropdown-header-area .dropdown-item .main-nav>ul>li ul.sub-menu {
              position: static;
              left: 0;
              right: 0;
              top: auto;
              margin: 0;
              min-width: unset;
              max-width: 210px;
              width: 210px;
              border-radius: 0px;
              padding: 0;
              opacity: 1;
              visibility: visible;
              background: transparent;
              text-align: left;
              transition: transform 0.35s ease-out 0s;
              transform: translateY(0);
              z-index: 999;
              margin-left: 10px;
              display: none;
            }

            .dropdown-header-area .dropdown-item .main-nav>ul>li ul.sub-menu>li .sub-menu {
              position: static;
              left: 10px;
              right: 0;
              top: auto;
              margin: 0;
              max-width: 200px;
              width: 200px;
              border-radius: 0px;
              padding: 0;
              opacity: 1;
              visibility: visible;
              background: transparent;
              text-align: left;
              transition: transform 0.35s ease-out 0s;
              transform: translateY(0);
              z-index: 999;
              margin-left: 10px;
              display: none;
            }

            .dropdown-header-area .dropdown-item .main-nav>ul>li ul.sub-menu>li a {
              border-bottom: 1px solid #8b8888;
              padding-left: 0;
              line-height: 1.4;
              white-space: normal;
            }

            .dropdown-header-area .dropdown-item .main-nav>ul>li ul.sub-menu>li ul.sub-menu>li a {
              border-bottom: unset;
              padding-left: 0;
              line-height: 1.4;
              white-space: normal;
              padding: 2px 0;
              font-size: 9px;
            }

            .dropdown-header-area .dropdown-item .main-nav>ul>li ul.sub-menu>li ul.sub-menu>li a::after {
              display: none !important;
            }

            .dropdown-header-area .dropdown-item .main-nav>ul>li ul.sub-menu>li a:hover {
              color: #fff;
            }

            .dropdown-header-area .dropdown-item .main-nav>ul>li ul.sub-menu>li a::after {
              content: "";
              position: absolute;
              bottom: -1px;
              left: 0;
              background: linear-gradient(90deg, #626262, #fff, #626262);
              width: 100%;
              height: 1px;
              transform: scaleX(0);
              transform-origin: left;
              transition: 0.4s ease-in;
              z-index: 1;
            }

            .dropdown-header-area .dropdown-item .main-nav>ul>li ul.sub-menu>li a:hover::after {
              transform: scale(1);
            }

            @media only screen and (max-width: 991px) {

              .dropdown-header-area .dropdown-item .main-nav {
                position: relative;
                top: unset;
                left: unset;
                width: 100%;
                padding: 0 !important;
                z-index: 99999;
                height: 100%;
                overflow: auto;
                background: transparent;
                transform: unset;
                transition: transform 0.6s;
                box-shadow: unset;
                padding: 0;
              }

              .dropdown-header-area .dropdown-item .main-nav ul.menu-list {
                padding: 0;
              }

              header.style-1 .main-nav ul li ul.sub-menu>li a {
                color: #fff;
              }

              header.style-1 .main-nav>ul>li i {
                color: #fff;
              }

              header.style-1 .header-icons ul li::after {
                background-color: #fff;
              }

              .dropdown-header-area .dropdown-item .main-nav>ul>li ul.sub-menu>li i {
                display: none;
              }

              .dropdown-header-area .dropdown-item .main-nav>ul>li ul.sub-menu>li.menu-item-has-children i {
                display: block;
              }

              .dropdown-header-area {
                position: initial;
              }

              .dropdown-header-area .dropdown-item {
                top: 88px;
                right: -2.1rem;
                width: calc(100% + 3.6rem);
              }
              .dropdown-header-area .dropdown-item .main-nav>ul>li ul.sub-menu {
                  max-width: calc(100% - 10px);
                  width: calc(100% - 10px);
              }
              .dropdown-header-area .dropdown-item .main-nav>ul>li ul.sub-menu>li .sub-menu {
                  max-width: calc(100% - 10px);
                  width: calc(100% - 10px);
              }
               .dropdown-header-area .dropdown-item .main-nav>ul>li ul.sub-menu>li.menu-item-has-children ul li i {
                display: none;
              }

            }
          </style>
          <div class="dropdown-header-area">
            <div class="mobile-menu-btn">
              <i class="bi bi-list text-dark"></i>
            </div>
            <div class="dropdown-item">
              <div class="main-nav text-end d-lg-flex d-block justify-content-start justify-content-start align-items-center">
                <!-- mobile-nav -->

                <?php
                if (is_page(11087)) {
                  wp_nav_menu(array(
                    'theme_location' => 'chinese_main_menu',
                    'container' => false,
                    'menu_class' => '',
                    'before' => '<i class="dropdown-icon"> </i>',
                    'fallback_cb' => '__return_false',
                    'items_wrap' => '<ul id="%1$s" class="menu-list %2$s">%3$s</ul>',
                    'walker' => new bootstrap_5_wp_nav_menu_walker_extended()
                  ));
                } elseif (is_page(1255)) {
                  wp_nav_menu(array(
                    'theme_location' => 'japanese_main_menu',
                    'container' => false,
                    'menu_class' => '',
                    'after' => '<i class="dropdown-icon"> </i>',
                    'fallback_cb' => '__return_false',
                    'items_wrap' => '<ul id="%1$s" class="menu-list %2$s">%3$s</ul>',
                    'walker' => new bootstrap_5_wp_nav_menu_walker_extended()
                  ));
                } elseif (is_page(1259)) {
                  wp_nav_menu(array(
                    'theme_location' => 'korean_main_menu',
                    'container' => false,
                    'menu_class' => '',
                    'after' => '<i class="dropdown-icon"> </i>',
                    'fallback_cb' => '__return_false',
                    'items_wrap' => '<ul id="%1$s" class="menu-list %2$s">%3$s</ul>',
                    'walker' => new bootstrap_5_wp_nav_menu_walker_extended()

                  ));
                } elseif (is_page(14797)) {
                  wp_nav_menu(array(
                    'theme_location' => 'german_main_menu',
                    'container' => false,
                    'menu_class' => '',
                    'fallback_cb' => '__return_false',
                    'after' => '<i class="dropdown-icon"> </i>',
                    'items_wrap' => '<ul id="%1$s" class="menu-list %2$s">%3$s</ul>',
                    'walker' => new bootstrap_5_wp_nav_menu_walker_extended()
                  ));
                } else {
                  wp_nav_menu(array(
                    'theme_location' => 'primary',
                    'container' => false,
                    'menu_class' => '',
                    'after' => '<i class="dropdown-icon"> </i>',
                    'fallback_cb' => '__return_false',
                    'items_wrap' => '<ul id="%1$s" class="menu-list %2$s">%3$s</ul>',
                    'walker' => new bootstrap_5_wp_nav_menu_walker_extended()
                  ));
                }
                ?>
                <!-- <ul class="menu-list">
                    <li class="menu-item-has-children">
                        <a href="people.html" class="drop-down">People</a><i
                            class='bi bi-chevron-down dropdown-icon'></i>
                            <ul class="sub-menu">
                                <li><a href="people.html">People One</a></li>
                                <li><a href="people-two.html">People Two</a></li>
                            </ul>
                    </li>
                    <li class="menu-item-has-children">
                        <a href="expertise-realstate.html" class="active">Experise</a><i class='bi bi-chevron-down dropdown-icon'></i>
                        <ul class="sub-menu">
                            <li><a href="expertise-one.html">Expertise One</a></li>
                            <li><a href="expertise-two.html">Expertise Two</a></li>
                            <li><a href="expertise-three.html">Expertise Three</a></li>
                            <li><a href="expertise-four.html">Expertise Four</a></li>
                            <li><a href="expertise-five.html">Expertise Five</a></li>
                        </ul>
                    </li>
                    <li class="menu-item-has-children">
                        <a href="insights-one.html">Insights</a><i class='bi bi-chevron-down dropdown-icon'></i>
                    </li>
                    <li><a href="upc.html">UPC</a></li>
                    <li class="menu-item-has-children">
                        <a href="career.html" class="drop-down">Careers</a><i
                            class='bi bi-chevron-down dropdown-icon'></i>
                            <ul class="sub-menu">
                                <li><a href="career.html">Career One</a></li>
                                <li><a href="career-two.html">Career Two</a></li>
                                <li><a href="career-three.html">Career Three</a></li>
                                <li><a href="career-four.html">Career Four</a></li>
                                <li><a href="career-five.html">Career Five</a></li>
                            </ul>
                    </li>
                    <li class="menu-item-has-children">
                        <a href="about.html">About</a>
                    </li>
                    <li><a href="responisble-business.html">RESPONSIBLE BUSINESS</a></li>
                </ul> -->
              </div>

              <ul class="language-list" style="padding-right: 25px;">
                <?php

                // add_filter( 'acf/validate_post_id', 'skip_acf_polylang_options', 10, 2 );
                // remove_filter( 'acf/validate_post_id', 'skip_acf_polylang_options' );
                $german_homepage_link = get_field('german_homepage_link', 'option');
                $japanese_homepage_link = get_field('japanese_homepage_link', 'option');
                $korean_homepage_link = get_field('korean_homepage_link', 'option');
                $chinese_homepage_link = get_field('korean_homepage_link', 'option');

                $german_homepage_link = get_post('14797');
                $japanese_homepage_link = get_post('1255');
                $korean_homepage_link = get_post('1259');
                $chinese_homepage_link = get_post('11087');

                global $wp_query;


                $pll_the_languages = pll_the_languages(array('raw' => 1));
                if ($pll_the_languages) {
                  foreach ($pll_the_languages as $l) {

                    $url = $l['url'];
                    $slug = $l['slug'];
                    if ($wp_query->post->ID == $japanese_homepage_link->ID || $wp_query->post->ID == $german_homepage_link->ID || $wp_query->post->ID == $korean_homepage_link->ID || $wp_query->post->ID == $chinese_homepage_link->ID) {

                      if (strtolower($slug) == 'en') {
                        // $url = home_url('/');
                        $url = site_url();
                      } else if (strtolower($slug) == 'es') {
                        $url = home_url('/es/');
                      } else if (strtolower($slug) == 'de') {
                        $url = home_url('/ger/');
                      }
                    }


                    if (in_array($first_slug, ['jp', 'kr', 'cn']) && $slug == 'en' && $l['current_lang']) {
                      $l['current_lang'] = false;
                    }

                    if (($l['current_lang'] && $first_slug == $slug) || ($l['current_lang'] && empty($first_slug)) || ($slug == 'de' && $first_slug == 'ger') || ($l['current_lang'] && $slug == 'en')) {
                      continue;
                    }
                    if (is_singular('people')) {
                      $url = str_replace("/es/people/", "/es/equipo/", $url);
                    }
                    // if(is_singular( 'bulletin' )){
                    //   $url = str_replace("/es/Boletín/","/bulletin", $url); 
                    //   // if($url === "https://boult2.ovstaging.com/es/" || $url === "https://boult2.ovstaging.com/"){
                    //   //   $url  = get_permalink($wp_query->post->ID);
                    //   // }
                    // }
                    // if(is_singular( 'news' )){
                    //   // if($url === "https://boult2.ovstaging.com/es/" || $url === "https://boult2.ovstaging.com/"){
                    //   //   $url  = get_permalink($wp_query->post->ID);
                    //   // }
                    // }
                    //echo  $url;
                ?>
                    <li class="<?php echo $slug; ?>">
                      <a href="<?php echo $url; ?>"><?php echo $slug; ?></a>
                    </li>
                <?php
                  }
                }
                ?>
                <?php /* 
                <li>
                    <a href="https://uat.boult.com.cn/en/">CN</a>
                </li> */ ?>
                <?php
                // if(is_home() || is_front_page() || $wp_query->post->ID == $japanese_homepage_link->ID || $wp_query->post->ID == $korean_homepage_link->ID){
                if (true) {
                  if ($german_homepage_link && $wp_query->post->ID != $german_homepage_link->ID || ($slug == 'de' && $first_slug == 'ger')) {
                ?>
                    <?php if ($slug != $first_slug) :  ?>
                      <li class="<?php echo is_singular('people') && $first_slug == 'de' ? 'd-none' : '' ?>">
                        <a href="<?php echo get_permalink($german_homepage_link); ?>">DE</a>
                      </li>
                    <?php endif ?>
                  <?php
                  }
                  if ($japanese_homepage_link && $wp_query->post->ID != $japanese_homepage_link->ID) {
                  ?>
                    <li>
                      <a href="<?php echo get_permalink($japanese_homepage_link); ?>">JP</a>
                    </li>
                  <?php
                  }
                  if ($korean_homepage_link && $wp_query->post->ID != $korean_homepage_link->ID) {
                  ?>
                    <li>
                      <a href="<?php echo get_permalink($korean_homepage_link); ?>">KR</a>
                    </li>
                  <?php
                  }
                  if ($chinese_homepage_link && $wp_query->post->ID != $chinese_homepage_link->ID) {
                  ?>
                    <li>
                      <a href="<?php echo get_permalink($chinese_homepage_link); ?>">CN</a>
                    </li>
                <?php
                  }
                }
                ?>
              </ul>
            </div>
          </div>
        </div>
      </div>
    </div>
    <style>
      @media (max-width: 1199px) {
        header.style-1 .header-icons {
          max-width: 140px !important;
        }
      }

      .contact-bar {
        display: inline-flex;
        align-items: center;
        gap: 15px;
      }

      .contact-info {
        /* border: 1px solid #ddd; */
        display: flex;
        align-items: center;
      }

      /* .contact-info span {
        display: block;
        font-size: 13px;
        font-weight: 600;
        color: #000;
        line-height: 1;
      } */

      .contact-info {
        text-decoration: none;
        font-size: 12px;
        font-weight: 600;
        line-height: 1;
        color: #000;
        display: flex;
        align-items: center;
        gap: 2px;
      }

      .contact-info i {
        color: #000;
        margin-right: 5px;
        transform: rotate(8deg);
      }
    </style>

  </header>
  <script>
    jQuery('.mobile-menu-btn').on("click", function() {
      jQuery('.dropdown-item').toggleClass('active');
    });

    jQuery('.dropdown-icon').on('click', function() {
      jQuery(this)
        .toggleClass('active') // Toggle 'active' class on the clicked icon
        .next('ul.sub-menu') // Target the immediate next submenu
        .slideToggle(); // Show or hide that submenu with animation

      jQuery(this)
        .parent() // Go to the parent of the clicked icon
        .siblings() // Get all sibling menu items
        .children('ul.sub-menu') // Find their submenus
        .slideUp(); // Hide them

      jQuery(this)
        .parent() // Again go to the parent
        .siblings() // Siblings
        .children('.active') // Any other icons with 'active' class
        .removeClass('active'); // Remove 'active' class from them
    });
  </script>
  <!-- ========== header end============= -->

  <?php /*if(function_exists('lc_custom_header')) lc_custom_header(); else {
      
      //STANDARD NAV
      
      if (get_theme_mod("enable_topbar") ) : ?>
        <!-- ******************* The Topbar Area ******************* -->
        <div id="wrapper-topbar" class="py-2 <?php echo get_theme_mod('topbar_bg_color_choice','bg-light') ?> <?php echo get_theme_mod('topbar_text_color_choice','text-dark') ?>">
          <div class="container">
            <div class="row">
              <div id="topbar-content" class="col-md-12 text-center small"> <?php echo do_shortcode(get_theme_mod('topbar_content')) ?>	</div>
            </div>
          </div>
        </div>
        <?php endif; ?>
        

        <!-- ******************* The Navbar Area ******************* -->
        <div id="wrapper-navbar" itemscope itemtype="http://schema.org/WebSite">

          <a class="skip-link visually-hidden-focusable" href="#theme-main"><?php esc_html_e( 'Skip to content', 'picostrap5-child-base' ); ?></a>

          
          <nav class="navbar <?php echo get_theme_mod('picostrap_header_navbar_expand','navbar-expand-lg'); ?> <?php echo get_theme_mod('picostrap_header_navbar_position')." ". get_theme_mod('picostrap_header_navbar_color_scheme','navbar-dark').' '. get_theme_mod('picostrap_header_navbar_color_choice','bg-dark'); ?>" aria-label="Main Navigation" >
            <div class="container">
              <div id="logo-tagline-wrap">
                  <!-- Your site title as branding in the menu -->
                  <?php if ( ! has_custom_logo() ) { ?>

                    <?php if ( is_front_page() && is_home() ) : ?>

                      <div class="navbar-brand mb-0 h3"><a rel="home" href="<?php echo esc_url( home_url( '/' ) ); ?>" title="<?php echo esc_attr( get_bloginfo( 'name', 'display' ) ); ?>" itemprop="url"><?php bloginfo( 'name' ); ?></a></div>

                    <?php else : ?>

                      <a class="navbar-brand mb-0 h3" rel="home" href="<?php echo esc_url( home_url( '/' ) ); ?>" title="<?php echo esc_attr( get_bloginfo( 'name', 'display' ) ); ?>" itemprop="url"><?php bloginfo( 'name' ); ?></a>

                    <?php endif; ?>


                  <?php } else {
                    the_custom_logo();
                  } ?><!-- end custom logo -->

                
                  <?php if (!get_theme_mod('header_disable_tagline')): ?>
                    <small id="top-description" class="text-muted d-none d-md-block mt-n2">
                      <?php bloginfo("description") ?>
                    </small>
                  <?php endif ?>
              
              
                  </div> <!-- /logo-tagline-wrap -->



              <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNavDropdown" aria-controls="navbarsExample05" aria-expanded="false" aria-label="Toggle navigation">
                <span class="navbar-toggler-icon"></span>
              </button>

              <div class="collapse navbar-collapse" id="navbarNavDropdown">
                <?php 
                  wp_nav_menu(array(
                    'theme_location' => 'primary',
                    'container' => false,
                    'menu_class' => '',
                    'fallback_cb' => '__return_false',
                    'items_wrap' => '<ul id="%1$s" class="navbar-nav me-auto mb-2 mb-md-0 %2$s">%3$s</ul>',
                    'walker' => new bootstrap_5_wp_nav_menu_walker()
                ));
                ?>
                
                <?php if (get_theme_mod('enable_search_form')): ?>
                  <form action="<?php echo bloginfo('url') ?>" method="get" id="header-search-form">
                    <input class="form-control" type="text" placeholder="Search" aria-label="Search" name="s" value="<?php the_search_query(); ?>">
                  </form> 
                <?php endif ?>
                <div class="search-form-icon">
                  <a href="javascript:;" class="search-btn"><i class="fa fa-search"></i></a>
                </div>

              </div> <!-- .collapse -->
            </div> <!-- .container -->
          </nav> <!-- .site-navigation -->
          <?php

          //AS A TEST / DEMO for a mock-up megamenu
          //include("nav-static-mega.php");
          ?>
        </div><!-- #wrapper-navbar end -->
        <!-- ******************* Search Area Start ******************* -->
          <div class="mobile-search">
              <div class="container-one">
                  <form method="get" action="<?php echo home_url('/'); ?>">
                    <div class="row d-flex justify-content-center">
                        <div class="col-md-11">
                            <label>What are you looking for?</label>
                            <input id="main_search" type="text" placeholder="Search">
                        </div>
                        <div class="col-1 d-flex justify-content-end align-items-center gap-2">
                            <div class="search-cross-btn">
                                <i class="bx bx-search-alt-2 fa fa-search"></i>
                            </div>
                            <div class="search-cross-btn">
                                <i class="bi bi-x fa fa-times"></i>
                            </div>
                        </div>
                        <div class="col-md-12">
                          <div id="filtered-data">

                          </div> 
                        </div>

                    </div>
                  </form>
              </div>
          </div>
          <!-- ******************* Search Area End ******************* -->
      
    <?php 
    } // END ELSE CASE */
  ?>
  <style>
    .banner-breadcrumb {
      margin-top: 10px;
      padding: 0;
      list-style: none;
      display: none;
    }

    .banner-breadcrumb a {
      color: rgb(227, 6, 19);
    }

    .cky-box-bottom-left {
      display: none;
    }
  </style>
  <main id='theme-main'>