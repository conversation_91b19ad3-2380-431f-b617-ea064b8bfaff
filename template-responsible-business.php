<?php 
/*
    Template Name: Responsible Business Page
*/

// Exit if accessed directly.
defined( 'ABSPATH' ) || exit;
$pll = pll_current_language();
get_header();

?>

<!-- ========== banner-section start============= -->

<div class="banner-section d-flex flex-column align-items-staer justify-content-center position-relative"
    style="background-image: url('<?php echo get_the_post_thumbnail_url(get_the_ID(), 'full'); ?>')">
    <div class="inner-overlay"></div>
    <!-- style="background-image:linear-gradient(to bottom, rgba(255,255,255,0.05) 0%, rgba(0,0,0,0.15) 50%), radial-gradient(at top center, rgba(255,255,255,0.20) 50%, rgba(0,0,0,0.40) 190%), url('<?php echo get_the_post_thumbnail_url(get_the_ID(), 'full'); ?>')"> -->
    <!-- style="background-image:linear-gradient(to bottom, rgba(255,255,255,0.05) 0%, rgba(0,0,0,0.15) 100%), radial-gradient(at top center, rgba(255,255,255,0.40) 0%, rgba(0,0,0,0.40) 120%), url('<?php echo get_the_post_thumbnail_url(get_the_ID(), 'full'); ?>')"> -->
    <div class="container text-center">
        <div class="banner-content style-dark">
            <h2><?php echo get_field('banner_title');?></h2>
        </div>
    </div>
</div>

<!-- ========== banner-section end============= -->

<!-- ========== page-content-section start============= -->
<?php 
    $description = get_field('description');
?>
<div class="content-section pl-container pt-100 mb-70">
    <div class="container-fluid">
        <div class="row">
            <div class="col-lg-5">
                <div class="section-title-one style-yellow">
                    <h1 ><?php echo $description['title']; ?></h1>
                </div>
                <div >
                    
                <?php echo $description['content']; ?>
                </div>

            </div>
            <div class="col-lg-7">
                <div class="box-design-15"> </div>
            </div>
        </div>
    </div>
</div>
<?php 
    $responsible_list = get_field('responsible_list');
    if(!empty( $responsible_list)) :
?>
<div class="business-card-section">
    
    <div class="container-one">
        <div class="business-card-box  m-hide">
            <div class="business-card-box-content"><?php echo get_field('download_box_content') ?></div>
        </div>
        <div class="row">
            <div class="col-lg-4">

            </div>
            <div class="col-lg-8">
                <div class="row g-lg-4 g-3">
                    <?php 
                        foreach($responsible_list as $value){
                            ?>
                            <div class="col-md-6 col-sm-12 col--6" >
                                <div class="service-item">
                                    <div class="image">
                                        <a hreflang="<?php echo esc_attr($pll); ?>" href="<?php echo $value['link']['url']; ?>"><img src="<?php echo $value['image']; ?>" alt="image"></a>
                                    </div>
                                    <div class="content">
                                        <h6><a hreflang="<?php echo esc_attr($pll); ?>" href="<?php echo $value['link']['url']; ?>"><?php echo $value['title']; ?></a></h6>
                                        <p><?php echo $value['content']; ?></p>
                                        <a hreflang="<?php echo esc_attr($pll); ?>" href="<?php echo $value['link']['url']; ?>"><!-- <i class='bx bx-plus'></i> --><?php echo $value['link']['title']; ?></a>
                                    </div>
                                </div>
                            </div>
                            <?php 
                        }
                    ?>
                    
                </div>
            </div>
        </div>
    </div>
</div>

<div class="business-card-section m-show" style="display: none;">
    <div class="container-one">
        <div class="row">
    <div class="business-card-box">
        <div class="business-card-box-content"><?php echo get_field('download_box_content') ?></div>
    </div>
</div></div>
</div>
<?php endif; ?>
<!-- ========== page-content-section start============= -->

<!-- ========== un-section start============= -->
<?php 
    $priorities = get_field('priorities', get_the_ID());
?>
<div class="un-section pt-100">
    <div class="container-one">
        <div class="row">
            <div class="col-lg-5">
                <div class="section-title-one style-yellow">
                    <h2 ><?php echo $priorities['title']; ?></h2>
                </div>
                <div >
                    
                <?php echo $priorities['content']; ?>
                </div>
            </div>
        </div>
        <div class="un-logo-wrap">
            <?php   
                foreach($priorities['gallery_image'] as $image) :
            ?>
            <div class="logo-wrap" >
                <img src="<?php echo $image['url']; ?>" alt="image">
            </div>
            <?php endforeach; ?>
        </div>
    </div>
</div>

<!-- ========== un-section end============= -->

    <!-- ========== search section start ============= -->

    <div class="search-section style-green with-box mt-100 d-none">
        <div class="container-one" >
            <form method="get" action="" id="home-page-banner-search-form">
                <div class="search-block">
                    <button type="search"><i class="bi bi-search"></i></button>
                    <input type="text" placeholder="<?php echo __('SEARCH OUR TEAM', 'picostrap5-child-base'); ?>" name="keyword">
                </div>
                <div class="search-block bg-black p-0">
                    <div class="home-page-banner-search-list" id="home-page-banner-search-list"></div>
                </div>
            </form>
        </div>
    </div>

    <!-- ========== search section start ============= -->


<?php get_footer();