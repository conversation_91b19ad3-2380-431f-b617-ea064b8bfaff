<?php

// Exit if accessed directly.
defined( 'ABSPATH' ) || exit;

get_header();

$pll = pll_current_language();


if ( have_posts() ) : 
    while ( have_posts() ) : the_post();
    
    if (get_the_post_thumbnail_url()){ 
        ?>
        <div class="d-flex container-fluid banner-section" style="background:url(<?php echo get_the_post_thumbnail_url(); ?>)  center / cover no-repeat;">
            <div class="container-one position-relative">
                
            </div>
        </div>
        <div class="banner-single-post-title title-bg-yellow">
            <div class="container-one">
                <h1 class="display-44"><?php the_title(); ?></h1>

                <?php
                        if ($pll == 'es') {
                        ?>
                            <div class="breadcrumps-wrap">
                                <span typeof="v:Breadcrumb"><a rel="v:url" property="v:title" hreflang="<?php echo esc_attr($pll); ?>" title="Servicios" href="/es/expertise/#services">Servicios</a></span> -
                                <span typeof="v:Breadcrumb"><a rel="v:url" property="v:title" hreflang="<?php echo esc_attr($pll); ?>" title="Patentes" href="/es/service/patents/">Patentes</a></span> -
                                <span typeof="v:Breadcrumb"><a rel="v:url" property="v:title" hreflang="<?php echo esc_attr($pll); ?>" title="Introducción a las Patentes" href="/es/patent-essentials/">Introducción a las Patentes</a></span> -
                                <span typeof="v:Breadcrumb"><span property="v:title"><?php the_title(); ?></span></span>
                            </div>
                        <?php
                        } else {
                        ?>
                           <div class="breadcrumps-wrap">
                                <span typeof="v:Breadcrumb"><a rel="v:url" property="v:title" hreflang="<?php echo esc_attr($pll); ?>" title="Services" href="/expertise/#services">Services</a></span> -
                                <span typeof="v:Breadcrumb"><a rel="v:url" property="v:title" hreflang="<?php echo esc_attr($pll); ?>" title="Patents" href="/service/patents/">Patents</a></span> -
                                <span typeof="v:Breadcrumb"><a rel="v:url" property="v:title" hreflang="<?php echo esc_attr($pll); ?>" title="Patent essentials" href="/patent-essentials/">Patent essentials</a></span> -
                                <span typeof="v:Breadcrumb"><span property="v:title"><?php the_title(); ?></span></span>
                            </div>
                        <?php
                        }
                        ?>
                <!-- <div class="banner-single-post-meta"> -->
                    <!-- <span class="post-date title-tag"><strong><?php echo __('Patent essential', 'picostrap5-child-base'); ?></strong> </span> -->
                    <!-- <span class="post-date"><?php the_date('d F Y'); ?> </span> -->
                <!-- </div> -->
                <?php 
                    if($people = get_field('people')){
                        $person = $people[0];
                        $person_2 = isset($people[1]) ? $people[1] : false;

                        $roles = get_the_terms( $person->ID, 'people_role' );
                        $roles = join(', ', wp_list_pluck($roles, 'name'));

                        $reverse_person_photo_places = get_field('reverse_person_photo_places', $person->ID);
                        $image_url = get_field('person_photo_1', $person->ID) ? get_field('person_photo_1', $person->ID)['sizes']['medium_large'] : get_stylesheet_directory_uri() . '/assets/images/team/team.jpg';
                        if($reverse_person_photo_places){
                            $image_url = get_field('person_photo_2', $person->ID) ? get_field('person_photo_2', $person->ID)['sizes']['medium_large'] : get_stylesheet_directory_uri() . '/assets/images/team/team.jpg';
                        }

                        ?>
                            <div class="banner-auther person-2">
                                <div class="banner-author-list">
                                    <div class="banner-auther-image">
                                        <img src="<?php echo $image_url; ?>" alt="<?php echo get_the_title($person); ?>">
                                    </div>
                                    <div class="banner-auther-meta">
                                        <div class="row">
                                            <div class="col-sm-12">
                                                <div class="author-name-section">
                                                    <p><?php echo __('Author:', 'picostrap5-child-base'); ?></p>
                                                    <h6><a hreflang="<?php echo esc_attr($pll); ?>" href="<?php echo get_the_permalink($person); ?>"><?php echo get_the_title($person); ?></a></h6>
                                                    <span><?php echo $roles; ?> </span>
                                                    <span class="border-bottom-el"></span>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                
                                <?php 
                                    if($person_2){

                                        $roles = get_the_terms( $person_2->ID, 'people_role' );
                                        $roles = join(', ', wp_list_pluck($roles, 'name'));

                                        $reverse_person_photo_places = get_field('reverse_person_photo_places', $person_2->ID);
                                        $image_url = get_field('person_photo_1', $person_2->ID) ? get_field('person_photo_1', $person_2->ID)['sizes']['medium_large'] : get_stylesheet_directory_uri() . '/assets/images/team/team.jpg';
                                        if($reverse_person_photo_places){
                                            $image_url = get_field('person_photo_2', $person_2->ID) ? get_field('person_photo_2', $person_2->ID)['sizes']['medium_large'] : get_stylesheet_directory_uri() . '/assets/images/team/team.jpg';
                                        }

                                        ?>
                                        <div class="banner-author-list">
                                            <div class="banner-auther-image">
                                                <img src="<?php echo $image_url; ?>" alt="<?php echo get_the_title($person_2); ?>">
                                            </div>
                                            <div class="banner-auther-meta">
                                                <div class="row">
                                                    <div class="col-sm-12">
                                                        <div class="author-name-section">
                                                            <p><?php echo __('Author:', 'picostrap5-child-base'); ?></p>
                                                            <h6><a hreflang="<?php echo esc_attr($pll); ?>" href="<?php echo get_the_permalink($person_2); ?>"><?php echo get_the_title($person_2); ?></a></h6>
                                                            <span><?php echo $roles; ?> </span>
                                                            <span class="border-bottom-el"></span>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <?php 
                                    }
                                ?>
                            </div>
                        <?php 
                    }
                ?>
            </div>
            
        </div>
    <?php } else {
        ?><div class="d-flex container-fluid" style="height:30vh;"></div>
    <?php } ?>
    
    <!-- <div class="container p-5 bg-light" style="margin-top:-100px"> -->
    <div class="container-one <?php echo $people ? 'people-meta-exists' : 'people-meta-not-exists'; ?> content-wrapper">
        <?php 
            /*if($people = get_field('people')){
                $person = $people[0];

                $roles = get_the_terms( $person->ID, 'people_role' );
                $roles = join(', ', wp_list_pluck($roles, 'name'));

                ?>
                    <div class="row">
                        <div class="col-sm-12">
                            <div class="author-name-section">
                                <p><?php echo __('Author:', 'picostrap5-child-base'); ?></p>
                                <h6><a href="<?php echo get_the_permalink($person); ?>"><?php echo get_the_title($person); ?></a></h6>
                                <span><?php echo $roles; ?> </span>
                                <span class="border-bottom-el"></span>
                            </div>
                        </div>
                    </div>
                <?php 
            }*/
        ?>
        
                <?php 
                //CATS
                if (!get_theme_mod("singlepost_disable_entry_cats") &&  has_category() ) {
                        ?>
                        <div class="row text-center">
                            <div class="col-md-12">
                                <div class="entry-categories">
                                    <span class="screen-reader-text"><?php _e( 'Categories', 'picostrap5-child-base' ); ?></span>
                                    <div class="entry-categories-inner">
                                        <?php the_category( ' ' ); ?>
                                    </div><!-- .entry-categories-inner -->
                                </div><!-- .entry-categories -->
                            </div><!-- /col -->
                        </div>
                        <?php
                }
                
                ?>

                
                <?php if (!get_theme_mod("singlepost_disable_date") OR !get_theme_mod("singlepost_disable_author")  ): ?>
                    <div class="post-meta" id="single-post-meta">
                        <p class="lead text-secondary">
                            
                            <?php if (!get_theme_mod("singlepost_disable_date") ): ?>
                                <!-- <span class="post-date"><?php the_date(); ?> </span> -->
                            <?php endif; ?>

                            <?php if (!get_theme_mod("singlepost_disable_author") ): ?>
                                <!-- <span class="text-secondary post-author"> <?php _e( 'by', 'picostrap5-child-base' ) ?> <?php the_author(); ?></span> -->
                            <?php endif; ?>
                        </p>
                    </div> 
                <?php endif; ?>


        <div class="row">
            <!-- <div class="col-md-8 offset-md-2"> -->
            <div class="col-md-12">
                <?php 
                
                the_content();

                ?>

            </div><!-- /col -->
        </div>


        <!-- ========== toolkit section start ============= -->

        <?php 
            $args = array(
                'post_type' => 'patent_essential',
                'posts_per_page' => '-1',
                'order' => 'DESC',
                'orderby' => 'date',
                'post__not_in' => array(get_the_ID()),
            );

            $q = new WP_Query($args);

            if($q->have_posts()){
                ?>
                    <div class="event-section position-relative pb-100 pt-50">
                        <div class="box-design-ten"></div>
                        <div class="container-onee">
                            <div class="section-title-one style-yellow">
                                <h2 ><?php echo __('Other patent essentials', 'picostrap5-child-base'); ?></h2>
                            </div>
                            <div class="row justify-content-lg-start justify-content-center g-sm-4 g-2">
                                <?php 
                                    while($q->have_posts()){
                                        $q->the_post();
                                        ?>
                                            <div class="col-lg-3 col-md-6 col-6" >
                                                <a hreflang="<?php echo esc_attr($pll); ?>" href="<?php echo get_the_permalink(); ?>">
                                                <div class="sector-item-two style-yellow">
                                                    <h6 class="title"><?php echo get_the_title(); ?></h6>
                                                    <!-- <p><?php // echo get_the_excerpt(); ?></p> -->
                                                    <!-- <span class="date">28 MARCH 2023</span> -->
                                                    <div class="arrow-btn">
                                                        <img src="<?php echo get_stylesheet_directory_uri(); ?>/assets/images/icons/arrow-white.svg" alt="arrow">
                                                    </div>
                                                </div>
                                                </a>
                                            </div>
                                        <?php 
                                    }
                                ?>
                            </div>
                        </div>
                    </div>
                <?php 
            }
        ?>


        <!-- ========== toolkit section start ============= -->

        <?php 
            $manual_sectors_list = get_field('manual_sectors_list');
            $manual_sectors_list_data = get_field('manual_sectors_list_data');
        ?>
        <div class="row d-none">
            <div class="col-sm-12">
                <div class="experience-box <?php echo get_field('sector_sub_sector') || ($manual_sectors_list && $manual_sectors_list_data) ? '' : 'd-none'; ?>">
                    <h5 class="title" ><?php echo __('Relevant sectors', 'picostrap5-child-base'); ?> </h5>
                    <?php 
                        // echo get_field('sector_sub_sector');

                        /*if($manual_sectors_list){
                            ?>
                                <ul >
                                    <?php 
                                        foreach ($manual_sectors_list_data as $_sector) {
                                            if($_sector['url']){
                                                ?>
                                                    <li><a href="<?php echo $_sector['url']; ?>"><?php echo $_sector['title']; ?></a></li>
                                                <?php 
                                            }
                                            else{
                                                ?>
                                                    <li><?php echo $_sector['title']; ?></li>
                                                <?php 
                                            }
                                        }
                                    ?>
                                </ul>
                            <?php 
                        }
                        else{
                            $sectors = get_field('sector_sub_sector');
                            $_sectors = $sectors = wp_list_pluck($sectors, 'ID');

                            $arg = array(
                                'post_type' => 'sector',
                                'posts_per_page' => '-1',
                                'post_status' => 'publish',
                                'post_parent' => '0',
                                'post__in' => $sectors,
                            );
                            $s = get_posts($arg);
                            if(empty($s)){
                                unset($arg['post_parent']);
                                $s = get_posts($arg);
                            }

                            if($s){
                                foreach ($s as $sector) {
                                    ?>
                                        <h6  class="subtitle"><?php echo $sector->post_title; ?></h6>
                                    <?php 
                                    $arg = array(
                                        'post_type' => 'sector',
                                        'posts_per_page' => '-1',
                                        'post_status' => 'publish',
                                        'post_parent' => $sector->ID,
                                        'post__in' => $_sectors,
                                    );
                                    $sec = get_posts($arg);
                                    if($sec){
                                        echo '<ul >';
                                        foreach ($sec as $_sector) {
                                            ?>
                                                <li><?php echo $_sector->post_title; ?></li>
                                            <?php 
                                        }
                                        echo '</ul>';
                                    }
                                }
                            }
                            else{
                                ?>
                                    <h6  class="subtitle"><?php echo __('No sectors listed.', 'picostrap5-child-base'); ?></h6>
                                <?php 
                            }
                        }*/

                    ?>
            </div>
        </div>

        <div class="row d-none">
            <?php 
                /*if(get_field('people')){
                    ?>
                        <div class="col-sm-12 mt-5">
                            <h3 class="pt-3 mb-50 bt-black border-top border-dark"><?php echo __('People', 'picostrap5-child-base'); ?></h3>
                        </div>
                        <div class="row">
                            <div class="col-md-8 offset-md-2">
                                <div class="row">
                                    <?php 
                                        if(get_field('people')){
                                            global $post;
                                            foreach (get_field('people') as $post) {
                                                setup_postdata($post);
                                                ?>
                                                    <div class="col-sm-3">
                                                        <div class="team-item style-two style-two">
                                                            <div class="team-image">
                                                                <a href="<?php echo get_the_permalink(); ?>">
                                                                    <img src="<?php echo get_field('person_photo_1') ? get_field('person_photo_1')['sizes']['medium_large'] : get_stylesheet_directory_uri() . '/assets/images/team/team.jpg'; ?>" alt="<?php echo get_the_title(); ?>">
                                                                </a>
                                                            </div>
                                                            <div class="team-content">
                                                                <h6><a href="<?php echo get_the_permalink(); ?>"><?php echo get_the_title(); ?></a></h6>
                                                                <span><?php echo $roles; ?> </span>
                                                                <span><?php echo $locations; ?></span>
                                                            </div>
                                                        </div>
                                                    </div>
                                                <?php 
                                            }
                                            wp_reset_postdata();
                                        }
                                    ?>
                                </div>
                            </div>
                        </div>
                    <?php 
                }*/
            ?>
        </div>
    </div>

<?php
    endwhile;
 else :
     _e( 'Sorry, no posts matched your criteria.', 'picostrap5-child-base' );
 endif;
 ?>


 

<?php get_footer();
