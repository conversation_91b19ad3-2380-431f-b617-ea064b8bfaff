<?php 
/*
    Template Name: Career Trainee Placement Page
*/

// Exit if accessed directly.
defined( 'ABSPATH' ) || exit;
$pll = pll_current_language();
get_header();

?>



<!-- <div class="logo-section">
    <div class="container-one">
        <div class="row justify-content-start">
            <div class="col-lg-6">
                <div class="logo-area">
                    <img src="<?php //echo get_stylesheet_directory_uri(); ?>/assets/images/logo/header-logo.svg" alt="image">
                </div>
            </div>
        </div>
    </div>
</div> -->

<!-- ========== header end============= -->
<h1 style="opacity:0; display: none;">hidden</h1>
<!-- ========== software-section start============= -->
<?php 
$content_info = get_field('content_info');
$eligibility_card = get_field('eligibility_card');
?>
<div class="content-section pt-240 pb-100">
    <div class="container-one">
        <div class="row">
            <div class="col-lg-6 pe-lg-5">
                <div class="section-title-one style-green title-pb-150">
                    <h2 ><?php echo get_field('page_title');?></h2>
                </div>
                <div class="eligibility-card style-green">
                    <div class="title">
                        <h6 ><?php echo $eligibility_card['title']; ?></h6>
                    </div>
                    <div >
                        
                    <?php echo $eligibility_card['content']; ?>
                    </div>
                </div>
                <div class="trainee-placement-image pb-100" >
                    <img src="<?php echo $eligibility_card['image']; ?>" alt="image">
                </div>

                <div class="pt-100"></div>

                <div class="attorny-left-image pt-100 d-none d-lg-block" >
                    <img src="<?php echo get_field('attorny_image')['url']; ?> " alt="image">
                </div>
            </div>
            <div class="col-lg-6">
                <div class="content pb-100">
                    <h6 ><?php echo $content_info['title']; ?></h6>
                    <div >
                        
                    <?php echo $content_info['content']; ?>    
                    </div>
                </div>
                <div class="apply-area mb-5" >
                    <div class="apply-card style-yellow">
                        <h5><?php echo get_field('apply_now_title');?></h5>
                        <div class="h-line"></div>
                        <?php $apply_now_button = get_field('apply_now_button'); ?>
                        <a hreflang="<?php echo esc_attr($pll); ?>" href="<?php echo $apply_now_button['url']; ?>" class="eg-btn btn--primary-black btn--lg"><?php echo $apply_now_button['title']; ?></a>
                    </div>
                    <div class="attorny-left-image2 d-lg-none">
                        <img src="<?php echo get_field('attorny_image')['url']; ?> " alt="image">
                    </div>
                </div>

                <div class="recent-highlight-quotes">
                    <div class="box-with-border-inner"></div>
                    <div class="box-inner-content">
                        <?php echo get_field('quotes'); ?>
                        <?php 
                            $quote_person_image = get_field('quote_person_image');
                            $person_image = get_stylesheet_directory_uri() . '/assets/images/team/team.jpg';
                            if($quote_person_image){
                                $roles = get_the_terms( $quote_person_image->ID, 'people_role' );
                                $roles = join(', ', wp_list_pluck($roles, 'name'));

                                $person_image = get_field('person_photo_1', $quote_person_image->ID) ? get_field('person_photo_1', $quote_person_image->ID)['sizes']['medium_large'] : get_stylesheet_directory_uri() . '/assets/images/team/team.jpg';
                                ?>
                                    <div class="team-content quote-inner">
                                        <h6><a hreflang="<?php echo esc_attr($pll); ?>" href="<?php echo get_the_permalink($quote_person_image); ?>"><?php echo get_the_title($quote_person_image); ?></a></h6>
                                        <span><?php echo strtoupper($roles); ?></span>
                                    </div>
                                <?php 
                            }
                        ?>
                    </div>
                    <div class="box-inner-image">
                        <img src="<?php echo $person_image; ?>" alt="image">
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- ========== software-section end============= -->

<!-- ========== facilities-section start============= -->

<div class="facilities-section">
    <div class="container-one">
        <!-- <div class="row align-items-end"> -->
        <div class="row align-items-center">
            <div class="col-lg-6 pe-lg-5">
                <div class="attorny-left-image d-none" >
                    <img src="<?php echo get_field('attorny_image')['url']; ?> " alt="image">
                </div>
            </div>
            <div class="col-lg-6" >
                <div id="application-tips">
                    <?php echo get_field('facilities_content'); ?> 
                </div>
            </div>
        </div>
    </div>
</div>

<!-- ========== facilities-section start============= -->

<!-- ========== search section start ============= -->

<div class="search-section style-green mb-100">
    <div class="container">
        <div class="search-block" >
            <button type="search"><i class="bi bi-search"></i></button>
            <input type="text" placeholder="<?php echo __('SEARCH OUR TEAM', 'picostrap5-child-base'); ?>">
        </div>
    </div>
</div>

<!-- ========== search section start ============= -->




<?php get_footer();