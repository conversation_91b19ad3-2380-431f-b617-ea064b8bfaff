<?php
/*
    Template Name: Current Vacancy
*/
// Exit if accessed directly.
defined('ABSPATH') || exit;
$pll = pll_current_language();
get_header();




if (have_posts()) :
    while (have_posts()) : the_post();

        if (get_the_post_thumbnail_url()) {
?><div class="d-flex container-fluid current-vacancies-banner" style="background:url(<?php echo get_the_post_thumbnail_url(); ?>)  center / cover no-repeat;"></div>
        <?php } else {
        ?><div class="d-flex container-fluid ht-cus-privary-policy"></div>
        <?php } ?>

        <!-- <div class="container p-5 bg-light" style="margin-top:-100px"> -->


        <?php
        $args = array(
            'post_type' => 'vacancy',
            'posts_per_page' => '-1',
            // 'post_parent' => '0',
            'post_status' => 'publish',
            // 'orderby' => array('title' => 'ASC', 'menu_order' => 'ASC'),
            'orderby'        => 'date',    // Order by post date
            'order'          => 'DESC',    // Latest post first
        );

        $q = new WP_Query($args);
        $add_external_vacancy = get_field('add_external_vacancy');

        $has_vacancies = $q->have_posts() || !empty($add_external_vacancy);
        ?>

        <div class="vacency-list-section <?php echo !$has_vacancies ? 'd-none' : ''; ?>">
            <div class="container-one">
                <div class="row g-4">
                    <?php
                    if ($q->have_posts()) {
                        while ($q->have_posts()) {
                            $q->the_post();
                    ?>
                            <div class="col-lg-4 col-sm-6">
                                <div class="job-post-name">
                                    <a hreflang="<?php echo esc_attr($pll); ?>" href="<?php echo get_the_permalink(); ?>"><?php echo get_the_title(); ?>
                                        <img src="<?php echo get_stylesheet_directory_uri(); ?>/assets/images/icons/arrow-white.svg" alt="arrow" />
                                    </a>
                                </div>
                            </div>
                        <?php
                        }
                        wp_reset_postdata();
                    }

                    if (!empty($add_external_vacancy)) {
                        foreach ($add_external_vacancy as $value) {
                        ?>
                            <div class="col-lg-4 col-sm-6">
                                <div class="job-post-name">
                                    <a hreflang="<?php echo esc_attr($pll); ?>" target="_blank" href="<?php echo $value['link']; ?>"><?php echo $value['title']; ?>
                                        <img src="<?php echo get_stylesheet_directory_uri(); ?>/assets/images/icons/arrow-white.svg" alt="arrow" />
                                    </a>
                                </div>
                            </div>
                    <?php
                        }
                    }
                    ?>
                </div>
            </div>
        </div>


        <div class="sign-up-area pt-30">
            <div class="container-one">
                <div class="row">
                    <div class="col-lg-4 col-md-6 col-sm-8">
                        <div class="sign-up-box">
                            <strong style="font-weight: 700;">Keep in touch</strong>
                            <br>
                        <strong  style="font-weight: 600;"><a hreflang="<?php echo esc_attr($pll); ?>" href="https://communications.boult.com/5/5/landing-pages/keep-in-touch-form---website.asp" target="_blank" rel="noopener">Sign up</a> to be notified of our open day, vacation scheme and work experience opportunities.</strong>
                        </div>
                    </div>
                </div>
            </div>
        </div>
                    <style>
                        .sign-up-box{
                            border: 1px solid var(--border);
                            padding: 20px;
                        }
                    </style>

        <div class="vacency-driscription pt-40">
            <div class="container-one">
                <h1 class="display-4"><?php the_title(); ?></h1>
                <div class="row">
                    <div class="col-md-12">
                        <?php
                        the_content();
                        ?>

                    </div>
                </div>
            </div>
        </div>
<?php
    endwhile;
else :
    _e('Sorry, no posts matched your criteria.', 'picostrap5-child-base');
endif;
?>
<?php get_footer();
