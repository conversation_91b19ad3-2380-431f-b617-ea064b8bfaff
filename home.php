<?php
/*
    Template Name: Home Page
*/

// Exit if accessed directly.
defined('ABSPATH') || exit;

get_header();
$pll = pll_current_language();
$trans['readmore'] = 'READ MORE';
if ($pll == 'es') {
    $trans['readmore'] = 'LEER MÁS';
} elseif ($pll == 'de') {
    $trans['readmore'] = 'MEHR LESEN';
}
?>
<style>
     .banner-slider-content{
        min-height: 100vh;
    }
    .bh-slider-cursor {
        display: inline-block;
        width: 10px;
        height: 10px;
        margin-left: -50px;
        margin-top: -105px;
        border-radius: 50%;
        background-color: red;
        left: 0;
        top: 0;
        opacity: 0;
        transition: opacity 0.5s ease-out, width 0.5s ease-out, height 0.5s ease-out;
        text-align: center;
        font-size: 6px;

    }

    .bh-slider-block-wrap {
        width: 100%;
        position: relative;
        overflow: hidden;
    }

    .bh-slider-block-wrap:hover .bh-slider-cursor {
        width: 100px;
        height: 100px;
        line-height: 100px;
        font-size: 30px;
        opacity: 1;
        z-index: 22222;
    }

    .bh-cursor {
        position: fixed;
    }

    .box-color-yellow {
        background-color: var(--primary-yellow) !important;
    }

    .box-color-red {
        background-color: var(--primary-red) !important;
    }

    .box-color-green {
        background-color: var(--primary-green) !important;
    }

    .box-color-blue {
        background-color: var(--primary-blue) !important;
    }

    .banner-slider-content h1 {
        margin: 0;
    }
</style>

<!-- ========== banner-section start ============= -->
<?php
$banner_section = get_field('banner_section');
?>
<div class="home-banner-section position-relative">
    <div class="container-fluid px-0">
        <?php
        $page_class = (is_page(8263)) ? 'sshome-banner-slider' : 'main-home-banner-slider';
        $page_class2 = (is_page(8263)) ? 'd-none' : 'd-flex';
        ?>

        <div class="swiper <?php echo $page_class; ?>">
            <div class="swiper-wrapper">
                <style>
                    .banner-btn {
                        padding: 13px 20px;
                        text-align: center;
                        display: inline-flex;
                        justify-content: center;
                        font-size: 16px;
                        background-color: #000;
                    }

                    .banner-btn.btn--lg2 i {
                        position: relative;
                        right: unset;
                        top: unset;
                        transform: unset;
                        color: #fff;
                        font-size: 16px;
                        display: inline-block;
                        transition: .4s;
                        padding-left: 7px;
                    }

                    .banner-logo {
                        width: 260px;
                        height: auto;
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        margin: 20px auto 0;
                    }
                </style>

                <?php
                if (!empty($banner_section['banner_image'])) {
                    foreach ($banner_section['banner_image'] as $value) {
                        if (!empty($value['title'])) {
                ?>
                            <div class="swiper-slide" style="background-image: linear-gradient(rgba(0, 0, 0, 0.15), rgba(0, 0, 0, 0.15) ), url('<?php echo $value['image']; ?>'); background-size: cover; background-position: center center;" data-mobile-bg="<?php echo esc_url($image_to_show); ?>">
                                <div class="banner-slider-content style-dark" style="background: unset;">
                                    <div class="container-one">
                                        <div class="col-lg-12 text-center">
                                            <h1 style="color: #fff;"><?php echo $value['title']; ?></h1>
                                            <?php if (!empty($value['read_more_link'])) : ?>
                                                <a href="<?php echo $value['read_more_link']; ?>" class="eg-btn btn--primary-green btn--lg2 mt-3 banner-btn">
                                                    <?php echo __($trans['readmore'], 'picostrap5-child-base'); ?>
                                                </a>
                                            <?php endif; ?>
                                            <?php if (!empty($value['upload_logo'])) : ?>
                                                <img class="banner-logo" src="<?php echo $value['upload_logo']; ?>" alt="">
                                            <?php endif; ?>
                                        </div>
                                    </div>
                                </div>
                            </div>
                <?php
                        }
                    }
                }
                ?>
            </div>
            <div class="silder-btn-group <?php echo $page_class2; ?>">
                <div class="slider-btn prev"><i class="bi bi-chevron-left"></i></div>
                <div class="slider-btn push-btn"><i class="bi bi-pause"></i></div>
                <div class="slider-btn next"><i class="bi bi-chevron-right"></i></div>
            </div>
        </div>

    </div>
    <!-- <div class = "banner-pagination d-flex justify-content-end align-items-start"></div> -->
    <style>
        .home-banner-section {
            position: relative;
        }

        .mouse_scroll {
            display: block;
            width: 24px;
            height: 50px;
            position: absolute;
            bottom: 30px;
            left: 50%;
            transform: translateX(-50%);
            cursor: pointer;
            z-index: 999;

        }

        .m_scroll_arrows {
            display: block;
            width: 5px;
            height: 5px;
            -ms-transform: rotate(45deg);
            -webkit-transform: rotate(45deg);
            transform: rotate(45deg);
            border-right: 2px solid white;
            border-bottom: 2px solid white;
            margin: 0 0 3px 4px;
            width: 16px;
            height: 16px
        }

        .unu {
            margin-top: 1px
        }

        .unu,
        .doi,
        .trei {
            -webkit-animation: mouse-scroll 1s infinite;
            -moz-animation: mouse-scroll 1s infinite;
            animation: mouse-scroll 1s infinite
        }

        .unu {
            -webkit-animation-delay: .1s;
            -moz-animation-delay: .1s;
            -webkit-animation-direction: alternate;
            animation-direction: alternate;
            animation-delay: alternate
        }

        .doi {
            -webkit-animation-delay: .2s;
            -moz-animation-delay: .2s;
            -webkit-animation-direction: alternate;
            animation-delay: .2s;
            animation-direction: alternate;
            margin-top: -6px
        }

        .trei {
            -webkit-animation-delay: .3s;
            -moz-animation-delay: .3s;
            -webkit-animation-direction: alternate;
            animation-delay: .3s;
            animation-direction: alternate;
            margin-top: -6px
        }

        .mouse {
            height: 42px;
            width: 24px;
            border-radius: 14px;
            transform: none;
            border: 2px solid white;
            top: 170px
        }

        .wheel {
            height: 5px;
            width: 2px;
            display: block;
            margin: 5px auto;
            background: white;
            position: relative;
            height: 4px;
            width: 4px;
            border: 2px solid #fff;
            -webkit-border-radius: 8px;
            border-radius: 8px
        }

        .wheel {
            -webkit-animation: mouse-wheel 0.6s linear infinite;
            -moz-animation: mouse-wheel 0.6s linear infinite;
            animation: mouse-wheel 0.6s linear infinite
        }

        @keyframes mouse-wheel {
            0% {
                top: 1px
            }

            25% {
                top: 2px
            }

            50% {
                top: 3px
            }

            75% {
                top: 2px
            }

            100% {
                top: 1px
            }
        }

        @keyframes mouse-scroll {
            0% {
                opacity: 0
            }

            50% {
                opacity: .5
            }

            100% {
                opacity: 1
            }
        }
    </style>
    <div class="mouse_scroll">
        <div> <span class="m_scroll_arrows unu"></span> <span class="m_scroll_arrows doi"></span></div>
    </div>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            function scrollToElement(element) {
                const offset = element.getBoundingClientRect().top + window.pageYOffset - 100;
                window.scrollTo({
                    top: offset,
                    behavior: 'smooth'
                });
            }

            const trigger = document.querySelector('.mouse_scroll');
            const target = document.querySelector('#content-section');

            if (trigger && target) {
                trigger.addEventListener('click', function(event) {
                    event.preventDefault();

                    // 🔽 Scroll to the element
                    scrollToElement(target);
                });
            }
        });
    </script>
</div>
<style>
    .home-banner-section .silder-btn-group {
        position: absolute;
        right: 0;
        bottom: 0;
        background-color: rgb(177, 179, 177);
        padding: 20px;
        display: flex;
        align-items: center;
        gap: 20px;
        z-index: 999;

    }

    .home-banner-section .silder-btn-group i {
        color: #000;
        font-size: 22px;
    }

    .home-banner-section .silder-btn-group .slider-btn {
        height: 35px;
        width: 35px;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        border: 1px solid #000;
        border-radius: 50%;
    }
     @media (max-width: 767px) {
        .home-banner-section .silder-btn-group {
            gap: 10px;
            padding: 12px;
        }
        .home-banner-section .silder-btn-group i {
            font-size: 18px;
        }
        .home-banner-section .silder-btn-group .slider-btn {
            height: 25px;
            width: 25px;
        }
    }
</style>

<!-- ========== banner-section end ============= -->

<div class="search-area d-none">
    <!--    <div class = "mobile-box"></div> -->
    <form method="get" action="" id="home-page-banner-search-form">
        <div class="search-block">
            <button type="search"><i class="bi bi-search"></i></button>
            <input type="text" placeholder="<?php echo __('SEARCH', 'picostrap5-child-base'); ?>" name="keyword">
        </div>
        <div class="search-block bg-black p-0">
            <div class="home-page-banner-search-list" id="home-page-banner-search-list"></div>
        </div>
    </form>
</div>

<!-- ========== page-content-section start ============= -->
<?php
$expertise_section = get_field('expertise_section');
?>
<div class="content-section pl-container pt-100" id="content-section">
    <div class="container-fluid">
        <div class="row justify-content-start">
            <div class="col-md-12 col-lg-6 col-xl-5">
                <div class="section-title-one style-yellow">
                    <h2><?php echo $expertise_section['title']; ?></h2>
                </div>
                <div>
                    <?php echo $expertise_section['content']; ?>
                    <a href="<?php echo isset($expertise_section['link']) && isset($expertise_section['link']['url']) ? $expertise_section['link']['url'] : ''; ?>" class="eg-btn btn--primary-yellow btn--lg2 mt-3"><?php echo isset($expertise_section['link']) && isset($expertise_section['link']['title']) ? $expertise_section['link']['title'] : ''; ?> <i class="bi bi-arrow-right"></i></a>
                </div>
            </div>
            <div class="col-md-12 col-lg-6 col-xl-7">
                <div class="expertise-section pt-100">
                    <div class="expertise-wrapper">
                        <div class="expertise-image">
                            <img src="<?php echo $expertise_section['quote_image']; ?>" alt="image">
                        </div>
                        <div class="quote-box2">
                            <p><?php echo $expertise_section['quote_content']; ?></p>
                            <span><?php echo $expertise_section['quote_title']; ?></span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- ========== expertise-section start ============= -->


<?php
$careers_section = get_field('careers_section');
?>
<div class="content-section pl-container mt-100">
    <div class="container-fluid">
        <div class="row justify-content-start">
            <div class="col-lg-5">
                <div class="section-title-one style-blue">
                    <h2><?php echo $careers_section['title']; ?></h2>
                </div>
                <div>
                    <?php echo $careers_section['content']; ?>
                    <a href="<?php echo isset($careers_section['link']) && isset($careers_section['link']['url']) ? $careers_section['link']['url'] : ''; ?>" class="eg-btn btn--primary-blue btn--lg2 mt-3"><?php echo isset($careers_section['link']) && isset($careers_section['link']['title']) ? $careers_section['link']['title'] : ''; ?><i class="bi bi-arrow-right"></i></a>
                </div>
            </div>
            <div class="col-lg-7">
                <div class="career-green-box">
                    <div class="border-box"></div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- ========== expertise-section start ============= -->

<!-- ========== insights section start ============= -->
<style>
    .insight-item-new .image {
        position: relative;
    }

    .insight-item-new .image h6 {
        position: absolute;
        left: 0;
        top: 0;
        margin-bottom: 0;
    }

    .insight-item-new .image h6 a {
        font-size: 13px;
        font-weight: 600;
        color: #ffffff;
        display: block;
        padding: 5px 12px;
        border-radius: 0 100px 100px 0;
        background: var(--primary-blue);
    }
   
</style>
<?php $auto_list_of_insights = get_field('auto_list_of_insights'); ?>
<?php $auto_list_of_insights = $auto_list_of_insights                           === NULL || $auto_list_of_insights ? true : false; ?>
<?php $insights              = get_field('insights'); ?>
<div class="insights-section position-relative bh-slider-block-wrap mt-70">
    <span class="bh-cursor bh-slider-cursor arrow-left"><i class="bi bi-arrow-left"></i></span>
    <div class="container-one">
        <div class="row">
            <div class="col-md-12">
                <div class="section-title-one style-yellow">
                    <h2><?php echo __('Insights', 'picostrap5-child-base'); ?></h2>
                </div>
            </div>
        </div>

        <div class="row justify-content-centerr mb-40 g-lg-4 g-3">
            <?php
            if ($auto_list_of_insights) {
                $args = array(
                    'post_type'      => array('bulletin', 'news', 'newsletter', 'tribe_events', 'toolkit', 'upc_news'),
                    'posts_per_page' => 4,
                    'orderby'        => 'modified',
                    'orderby'        => 'date',
                    'order'          => 'DESC',
                    'post_status'    => 'publish',
                );

                $query = new WP_Query($args);

                if ($query->have_posts()) {
                    while ($query->have_posts()) {
                        $query->the_post();
                        // Get the current post type
                        $post_type = get_post_type();
                        $permalink = '';

                        if ($pll == 'es') {
                            if ($post_type == 'bulletin') {
                                $permalink = str_replace("/bulletin/", "/es/Bolet%C3%ADn/", get_permalink());
                            } elseif ($post_type == 'news') {
                                $permalink = str_replace("/news/", "/es/noticias/", get_permalink());
                            } else {
                                $permalink = get_permalink();
                            }
                        } elseif ($pll == 'de') {

                            if ($post_type == 'bulletin') {
                                $permalink = str_replace("/bulletin/", "/bulletin/", get_permalink());
                            } elseif ($post_type == 'news') {
                                $permalink = str_replace("/news/", "/news/", get_permalink());
                            } else {
                                $permalink = get_permalink();
                            }
                        } else {
                            $permalink = get_permalink();
                        }

                         $post_type_obj = get_post_type_object($post_type);
                        $singular_name = $post_type_obj ? $post_type_obj->labels->singular_name : '';

                        if ($post_type === 'bulletin') {
                            $singular_name = 'IP updates';
                        }
                        $excerpt = get_the_excerpt($post);

                        $excerpt = substr($excerpt, 0, 200);
            ?>
                        <div class="col-lg-3 col-md-6 col-12 <?php if (!empty(get_field(get_the_ID(), 'banner_video_url'))) {
                                                                    echo 'video-sticky';
                                                                } ?>">
                          <div class="insight-item insight-item-new style-red">
                                <?php  // if(!empty(get_the_post_thumbnail_url())) :
                                ?>
                                <div class="image">
                                    <a href="<?php echo $permalink; ?>"><img src="<?php echo has_post_thumbnail() ? get_the_post_thumbnail_url($b) : home_url('/wp-content/uploads/2023/06/iStock-97970805-scaled.jpg'); ?>" alt="image"></a>
                                    <h6><a class="insight-title" href="<?php echo $permalink; ?>"><?php echo __($singular_name, 'picostrap5-child-base'); ?></a></h6>
                                </div>
                                <?php  // endif; 
                                ?>
                                <div class="content">
                                   
                                    <h6><a class="insight-title" href="<?php echo $permalink; ?>"><?php the_title(); ?></a></h6>
                                    <p><?php echo $excerpt; ?></p>
                                </div>
                            </div>
                        </div>
                    <?php
                    }
                }
                wp_reset_postdata();
            } else {
                if ($insights) {
                    foreach ($insights as $value) {
                        // $postType = get_post_type_object(get_post_type($value));
                    ?>
                        <div class="col-lg-3 col-md-6 col-6">
                            <div class="insight-item style-red">
                                <?php  // if(!empty(get_the_post_thumbnail_url())) :
                                ?>
                                <div class="image">
                                    <a href="<?php echo get_the_permalink($value); ?>"><img src="<?php echo has_post_thumbnail($value->ID) ? get_the_post_thumbnail_url($value, 'full') : home_url('/wp-content/uploads/2023/06/iStock-97970805-scaled.jpg'); ?>" alt="image"></a>
                                </div>
                                <?php  // endif; 
                                ?>
                                <div class="content">
                                    <h6><a href="<?php echo get_the_permalink($value); ?>"><?php  // echo __($postType->labels->singular_name, 'picostrap5-child-base'); // __('Bulletin', 'picostrap5-child-base'); 
                                                                                            ?></a></h6>
                                    <p><?php echo get_the_title($value); ?></p>
                                    <a href="<?php echo get_the_permalink($value); ?>"><?php echo __('MORE', 'picostrap5-child-base'); ?></a>
                                </div>
                            </div>
                        </div>
            <?php
                    }
                }
            }
            ?>

        </div>
        <?php
        $pll        = pll_current_language();
        $insightUrl = '';
        if ($pll == 'es') {
            $insightUrl = home_url('/es/actualidad/');
        } else {
            $insightUrl = home_url('/insights/');
        }
        ?>
        <a target="_blank" href="<?php echo  home_url('/insights/'); ?>" class="eg-btn btn--primary-yellow btn--lg2"><?php echo __('INSIGHTS', 'picostrap5-child-base'); ?><i class="bi bi-arrow-right"></i></a>
    </div>
</div>

<!--
<div class = "box-sectoin mt-70">
<div class = "container-one">
<div class = "row">
<div class = "col-lg-6">

            </div>
            <div class = "col-lg-6    ">
            <div class = "insight-box-area" >
            <div class = "red-box"></div>
                </div>
            </div>
        </div>
    </div>
</div> -->
<?php
$trending_section = get_field('trending_section');
?>
<div class="trending-section pl-container pt-100">
    <div class="container-fluid">
        <div class="row justify-content-start">
            <div class="col-lg-5">
                <div class="section-title-one style-green">
                    <h2><?php echo $trending_section['title']; ?></h2>
                </div>
                <?php echo $trending_section['content']; ?>
                <a href="<?php echo isset($trending_section['link']) && isset($trending_section['link']['url']) ? $trending_section['link']['url'] : ''; ?>" class="eg-btn btn--primary-green btn--lg2 mt-3"> <?php echo isset($trending_section['link']) && isset($trending_section['link']['title']) ? $trending_section['link']['title'] : ''; ?><i class="bi bi-arrow-right"></i></a>
            </div>
            <div class="col-lg-5">
                <div class="trending-image">
                    <img src=" <?php echo $trending_section['image']; ?>" alt="image">
                </div>
            </div>
        </div>
    </div>
</div>

<!--  ========== insights section end ============= -->
<div class="footer-top-design-eight">
    <div class="box"></div>
</div>

<?php get_footer();
