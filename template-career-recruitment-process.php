<?php 
/*
    Template Name: Career Recruitment Process Page
*/

// Exit if accessed directly.
defined( 'ABSPATH' ) || exit;
$pll = pll_current_language();
get_header();

?>



<!-- <div class="logo-section">
    <div class="container-one">
        <div class="row justify-content-start">
            <div class="col-lg-6">
                <div class="logo-area">
                    <img src="<?php //echo get_stylesheet_directory_uri(  );?>/assets/images/logo/header-logo.svg" alt="image">
                </div>
            </div>
        </div>
    </div>
</div> -->

<!-- ========== header end============= -->
<h1 style="opacity:0; display: none;">hidden</h1>
<!-- ========== software-section start============= -->
<?php 
 $page_description = get_field('page_description');
 $we_offer_section = get_field('we_offer_section');
?>
<div class="content-section pt-240">
    <div class="container-one">
        <div class="row">
            <div class="col-lg-6 pe-lg-5">
                <div class="section-title-one style-green title-pb-150">
                    <h2 ><?php echo get_field('page_title');?></h2>
                </div>
                <div class="recruitment-image" >
                    <img src="<?php echo $page_description['image']; ?>" alt="image">
                </div>

                <div class="recent-highlight-quotes mt-100 <?php echo get_field('quotes') ? '' : 'd-none'; ?>" style="margin-top: 250px;">
                    <div class="box-with-border-inner"></div>
                    <div class="box-inner-content">
                        <?php echo get_field('quotes'); ?>
                        <?php 
                            $quote_person_image = get_field('quote_person_image');
                            $person_image = get_stylesheet_directory_uri() . '/assets/images/team/team.jpg';
                            if($quote_person_image){
                                $roles = get_the_terms( $quote_person_image->ID, 'people_role' );
                                $roles = join(', ', wp_list_pluck($roles, 'name'));

                                $person_image = get_field('person_photo_1', $quote_person_image->ID) ? get_field('person_photo_1', $quote_person_image->ID)['sizes']['medium_large'] : get_stylesheet_directory_uri() . '/assets/images/team/team.jpg';
                                ?>
                                    <div class="team-content quote-inner">
                                        <h6><a hreflang="<?php echo esc_attr($pll); ?>" href="<?php echo get_the_permalink($quote_person_image); ?>"><?php echo get_the_title($quote_person_image); ?></a></h6>
                                        <span><?php echo strtoupper($roles); ?></span>
                                    </div>
                                <?php 
                            }
                        ?>
                    </div>
                    <div class="box-inner-image">
                        <img src="<?php echo $person_image; ?>" alt="image">
                    </div>

                </div>

                <div class="recent-highlight-quotes mt-100 <?php echo get_field('quotes_2') ? '' : 'd-none'; ?>" style="margin-top: 250px;">
                    <div class="box-with-border-inner"></div>
                    <div class="box-inner-content">
                        <?php echo get_field('quotes_2'); ?>
                        <?php 
                            $quote_person_image = get_field('quote_person_image_2');
                            $person_image = get_stylesheet_directory_uri() . '/assets/images/team/team.jpg';
                            if($quote_person_image){
                                $roles = get_the_terms( $quote_person_image->ID, 'people_role' );
                                $roles = join(', ', wp_list_pluck($roles, 'name'));

                                $person_image = get_field('person_photo_1', $quote_person_image->ID) ? get_field('person_photo_1', $quote_person_image->ID)['sizes']['medium_large'] : get_stylesheet_directory_uri() . '/assets/images/team/team.jpg';
                                ?>
                                    <div class="team-content quote-inner">
                                        <h6><a hreflang="<?php echo esc_attr($pll); ?>" href="<?php echo get_the_permalink($quote_person_image); ?>"><?php echo get_the_title($quote_person_image); ?></a></h6>
                                        <span><?php echo strtoupper($roles); ?></span>
                                    </div>
                                <?php 
                            }
                            else if($quote_person_image_custom_2 = get_field('quote_person_image_custom_2')){
                                $person_image = $quote_person_image_custom_2['url'];
                            }
                        ?>
                    </div>
                    <div class="box-inner-image">
                        <img src="<?php echo $person_image; ?>" alt="image">
                    </div>
                </div>

                <div class="offer-card <?php echo empty($we_offer_section['content']) ? 'd-none' : ''; ?>">
                    <div class="title" >
                        <h4><?php echo $we_offer_section['title']; ?></h4>
                    </div>
                    <div >
                        
                    <?php echo $we_offer_section['content']; ?>
                    </div>
                </div>
                <?php if( !empty( get_field('download_box_content') ) ) : ?>
                    <div class="business-card-box-content community">
                        <?php echo get_field('download_box_content') ?>
                    </div>
                <?php endif ?>
                <?php 
                    if(get_the_ID() == '1230'){
                        ?>
                            <div class="recruitment-process-quotes left-qoutes mt-50" >
                                <?php
                                    foreach(get_field('case_studies') as $case_study){
                                        ?>
                                            <div class="recruitment-process-quotes-single mb-30">
                                                <div class="section-title-one style-pink">
                                                    <h2><?php echo $case_study['title']; ?></h2>
                                                </div>
                                                <div class="quote-content">
                                                    <?php echo $case_study['content']; ?>
                                                </div>
                                                <div class="section-title-one style-pink">
                                                    <h2></h2>
                                                </div>
                                            </div>
                                        <?php 
                                    }
                                ?>
                            </div>
                        <?php 
                    }
                ?>

            </div>
            <div class="col-lg-6">
                <div class="content" >
                    <?php echo $page_description['content']; ?>
                </div>
                <div class="faq-area <?php echo get_field('faq_list') ? '' : 'd-none'; ?>">
                    <div class="section-title-four">
                        <h4 ><?php echo get_field('faq_list_title'); ?></h4>
                    </div>
                    <div class="faq-wrap">
                        <?php 
                            if(!empty($faq_list = get_field('faq_list'))){
                                $i = 1; 
                                foreach($faq_list as $value){
                                    ?>
                                    <div class="faq-item" >
                                        <h5 id="heading<?php echo $i; ?>" class="accordion-button <?php echo $i==1 ? 'collapsed' : 'collapsed'; ?>" data-bs-toggle="collapse"
                                            data-bs-target="#collapse<?php echo $i; ?>" aria-controls="collapse<?php echo $i; ?>">
                                            <?php echo $value['title']; ?>
                                        </h5>
                                        <div id="collapse<?php echo $i; ?>" class="accordion-collapse collapse <?php echo $i==1 ? '' : ''; ?>" aria-labelledby="heading<?php echo $i; ?>">
                                            <div class="faq-body">
                                                <?php echo $value['content']; ?>
                                            </div>
                                        </div>
                                    </div>
                                    <?php 
                                    $i++;
                                }
                            }
                        ?>
                    </div>
                </div>
                <?php 
                    if(get_the_ID() != '1580' || get_the_ID() != '1230'){
                        ?>
                            <div class="recruitment-process-quotes right-qoutes mt-50" >
                                <?php
                                    foreach(get_field('case_studies') as $case_study){
                                        ?>
                                            <div class="recruitment-process-quotes-single mb-30">
                                                <div class="section-title-one style-pink">
                                                    <h2><?php echo $case_study['title']; ?></h2>
                                                </div>
                                                <div class="quote-content">
                                                    <?php echo $case_study['content']; ?>
                                                </div>
                                                <div class="section-title-one style-pink">
                                                    <h2></h2>
                                                </div>
                                            </div>
                                        <?php 
                                    }
                                ?>
                            </div>
                        <?php
                    }
                ?>

                <div class="recent-highlight-quotes mt-50 <?php echo get_field('quotes_3') ? '' : 'd-none'; ?>" >
                    <div class="box-with-border-inner"></div>
                    <div class="box-inner-content">
                        <?php echo get_field('quotes_3'); ?>
                        <?php 
                            $quote_person_image = get_field('quote_person_image_3');
                            $person_image = get_stylesheet_directory_uri() . '/assets/images/team/team.jpg';
                            if($quote_person_image){
                                $roles = get_the_terms( $quote_person_image->ID, 'people_role' );
                                $roles = join(', ', wp_list_pluck($roles, 'name'));

                                $person_image = get_field('person_photo_1', $quote_person_image->ID) ? get_field('person_photo_1', $quote_person_image->ID)['sizes']['medium_large'] : get_stylesheet_directory_uri() . '/assets/images/team/team.jpg';
                                ?>
                                    <div class="team-content quote-inner">
                                        <h6><a hreflang="<?php echo esc_attr($pll); ?>" href="<?php echo get_the_permalink($quote_person_image); ?>"><?php echo get_the_title($quote_person_image); ?></a></h6>
                                        <span><?php echo strtoupper($roles); ?></span>
                                    </div>
                                <?php 
                            }
                            else if($quote_person_image_custom_3 = get_field('quote_person_image_custom_3')){
                                $person_image = $quote_person_image_custom_3['url'];
                            }
                        ?>
                    </div>
                    <div class="box-inner-image">
                        <img src="<?php echo $person_image; ?>" alt="image">
                    </div>
                </div>

            </div>
        </div>
    </div>
</div>

<!-- ========== software-section end============= -->

<!-- ========== tips-section start============= -->
<?php 
    $people_talks = get_field('people_talk');
    if(!empty($people_talks)){
        foreach ($people_talks as $key => $people_talk) {
            ?>
                <div class="tips-section">
                    <div class="container-fluid">
                        <div class="row">
                            <div class="col-lg-4 d-lg-flex d-none">
                                <div class="tip-left-box">
                                </div>
                            </div>
                            <div class="col-lg-8">
                                <div class="big-card testimonial-single style-blue mb-90 align-items-end">
                                    <?php 
                                        $people = $people_talk['select_people'];
                        
                                        if(!empty($people)):
                                            $roles = get_the_terms( $people->ID, 'people_role' );
                                            $roles = join(', ', wp_list_pluck($roles, 'name'));
                    
                                            $locations = get_the_terms( $people->ID, 'location' );
                                            $locations = join(', ', wp_list_pluck($locations, 'name'));
                                    ?>
                                    <div class="author" >
                                        <img src="<?php echo get_field('person_photo_1', $people->ID) ? get_field('person_photo_1', $people->ID)['sizes']['medium_large'] : get_stylesheet_directory_uri() . '/assets/images/team/team.jpg'; ?>" alt="<?php echo get_the_title($people->ID); ?>">
                                        <h6><?php echo get_the_title($people->ID); ?></h6>
                                        <span><?php echo $roles;?></span>
                                        <span><?php echo $locations;?></span>
                                    </div>
                                    <?php endif; ?>
                                    <div class="content">
                                        <div class="title" >
                                            <h4><?php echo $people_talk['title']; ?></h4>
                                        </div>
                                        <div class="body" >
                                            <?php 
                                                echo $people_talk['content']; 
                                            ?>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            <?php 
        }
    }
?>

<!-- ========== tips-section start============= -->
<?php 
    $apply_card = get_field('apply_card');
    if (!empty($apply_card) && !empty($apply_card[0]['button'])) :
?>
<div class="apply-card-section">
    <div class="container-one">
        <div class="row g-lg-4 g-2">
            <?php foreach($apply_card as $value) : ?>
            <div class="col-lg-4 col-md-6 col-12" >
                <div style="background: <?php echo $value['bg_color']; ?>" class="apply-card mt-0">
                    <h5><?php echo $value['title']; ?></h5>
                    <div class="h-line"></div>
                    <a hreflang="<?php echo esc_attr($pll); ?>" href="<?php echo isset($value['button']) && isset($value['button']['url']) ? $value['button']['url'] : ''; ?>" class="eg-btn btn--primary-black btn--lg"><?php echo isset($value['button']) && isset($value['button']['url']) ? $value['button']['title'] : ''; ?></a>
                </div>
            </div>
            <?php endforeach; ?>
        </div>
    </div>
</div>
<?php endif; ?>


<!-- ========== toolkit section start ============= -->

<?php 
    $toolkit_items = get_field('toolkit_items');
?>

<div class="event-section position-relative pb-100 <?php echo $toolkit_items ? '' : 'd-none'; ?>">
    <div class="box-design-ten"></div>
    <div class="container-one">
        <div class="section-title-one style-yellow">
            <h2 ><?php echo get_field('toolkit_title') ? get_field('toolkit_title') : 'Toolkits'; ?></h2>
        </div>
        <div class="row justify-content-lg-start justify-content-center g-sm-4 g-2">
            <?php 
                if(!empty($toolkit_items)){
                    foreach($toolkit_items as $t){
                        ?>
                            <div class="col-lg-3 col-md-6 col-12" >
                                <a hreflang="<?php echo esc_attr($pll); ?>" href="<?php echo isset($t['link']['url']) ? $t['link']['url'] : '#'; ?>">
                                <div class="sector-item-two style-yellow">
                                    <h6 class="title"><?php echo $t['title']; ?></h6>
                                    <p><?php echo $t['content']; ?></p>
                                    <!-- <span class="date">28 MARCH 2023</span> -->
                                    <div class="arrow-btn">
                                        <img src="<?php echo get_stylesheet_directory_uri(); ?>/assets/images/icons/arrow-white.svg" alt="">
                                    </div>
                                </div>
                                </a>
                            </div>
                        <?php 
                    }
                }
            ?>
        </div>
    </div>
</div>

<!-- ========== toolkit section start ============= -->




<?php get_footer();