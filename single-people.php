<?php
// Exit if accessed directly.
defined('ABSPATH') || exit;

get_header();

$referer = isset($_GET['referer']) ? $_GET['referer'] : false;
/*$referer_id = false;
$ref = false;
if($referer == 'sector'){
    $referer_id = get_the_ID();
    $ref = get_post($referer_id);
}*/

?>
<style>
    a.downloadvcard {
        font-weight: 800 !important;
        font-size: 14px !important;
        display: block;
        color: #000;
        margin-top: -10px;
        margin-bottom: 15px;
    }

    a.downloadvcard img {
        width: 26px;
        margin-right: 5px;
        margin-top: -3px;
    }

    @media (max-width: 767px) {
        a.downloadvcard {
            margin-left: 0;
            margin-top: 20px;
        }

        .experience-box::before {
            top: -65px;
        }
    }
</style>
<!-- ========== banner-section start============= -->

<?php
$no_qoute = "";
if (empty(get_field('partner_quote')) || empty(get_field('partner_quote'))) {
    $no_qoute = 'no-quote';
}


$pll = pll_current_language();
$german_pages_content = get_field('the_pages_content', 'options');
$gepages = explode(',', $german_pages_content);



$trans['sector-experience'] = 'Sector experience';
$trans['memberships'] = 'Memberships';
$trans['qualifications'] = 'Qualifications';
$trans['recent-experience'] = 'Recent experience';
$trans['contact'] = 'Contact';
$trans['languages'] = 'Languages';


if ($pll == 'de') {
    if (in_array(get_the_ID(), $gepages)) {
        $trans['sector-experience'] = 'Sektoren';
        $trans['memberships'] = 'Mitgliedschaften';
        $trans['qualifications'] = 'Qualifikationen';
        $trans['recent-experience'] = 'Aktuelle Fälle';
        $trans['contact'] = 'Kontakt';
        $trans['languages'] = 'Sprachen';
    }
} elseif ($pll == 'es') {
    $trans['sector-experience'] = 'Sectores';
    $trans['contact'] = 'Datos de contacto';
    $trans['qualifications'] = 'Cualificaciones';
    $trans['memberships'] = 'Asociaciones profesionales';
}
$fullname = get_the_title();
$job_title = get_field('role');
$telephone = get_field('telephone');
$email = get_field('email');
$address = get_field('address');
$linkedin = get_field('linkedin');
//$about = get_field('partner_quote');
$about = 'http://boult2.ovstaging.com';
$companyName = 'Boult';
$roles = get_the_terms(get_the_ID(), 'people_role');
if (!empty($roles)) {
    $roles = join(', ', wp_list_pluck($roles, 'name'));
}
if (get_field('custom_people_role') != '') {
    $roles = get_field('custom_people_role');
}

$locations = get_the_terms(get_the_ID(), 'location');
if (!empty($locations)) {
    $locations = join(', ', wp_list_pluck($locations, 'name'));
}
$fullAddress = '';
if ($locations === 'London') {
    $fullAddress = 'London
Salisbury Square House 
8 Salisbury Square
London
EC4Y 8AP';
} elseif ($locations === 'Frankfurt') {
    $fullAddress = 'Frankfurt
Mindspace Eurotheum 
Neue Mainzer Str. 66-68 
60311 Frankfurt am Main';
} elseif ($locations === 'Munich') {
    $fullAddress = 'Munich
Regus Maximilianstraße 35 A 
80539 Munich';
} elseif ($locations === 'Madrid') {
    $fullAddress = 'Madrid
Boult Wade, S.L.  
Avda. de Europa 26 
Ática 5 Planta 2  
28224 Pozuelo De Alarcón 
Madrid';
} elseif ($locations === 'Cambridge') {
    $fullAddress = 'Cambridge
CPC4 Capital Park 
Cambridge Rd 
Cambridge 
CB21 5XE';
} elseif ($locations === 'Reading') {
    $fullAddress = 'Reading
The Anchorage 
34 Bridge St 
Reading 
RG1 2LU';
}

if (class_exists('OG_VCARD_GENERATOR')) :
    $vcard_link = \OG_VCARD_GENERATOR::vg($fullname, $roles, $telephone, $email, $fullAddress, $linkedin,  $about, $companyName);
endif;

$singlePeopleId = get_the_ID();

$banner_url = get_the_post_thumbnail_url(get_the_ID(), 'full') ? get_the_post_thumbnail_url(get_the_ID(), 'full') : (get_field('person_photo_1') ? get_field('person_photo_1')['url'] : get_field('person_photo_2')['url']);

$reverse_person_photo_places = get_field('reverse_person_photo_places');
if ($reverse_person_photo_places) {
    $banner_url = get_the_post_thumbnail_url(get_the_ID(), 'full') ? get_the_post_thumbnail_url(get_the_ID(), 'full') : (get_field('person_photo_2') ? get_field('person_photo_2')['url'] : get_field('person_photo_1')['url']);
}

?>

<div class="banner-section d-flex flex-column align-items-staer justify-content-center" style="background-image:url('<?php echo $banner_url; ?>')">
</div>

<!-- ========== banner-section end============= -->

<!-- ==========people-info section start ============= -->
<div class="people-info-section pr-container pt-100">
    <div class="container-fluid">
        <div class="row">
            <div class="col-lg-7">
                <div class="about-people-card  <?php echo $no_qoute; ?>">
                    <?php
                    $person_photo_2 = get_field('person_photo_2');
                    if ($person_photo_2 && get_field('cover_profile_photo')) { ?>
                        <div class="mt--50 person-profile-pic-box-phone">
                            <div class="person-profile-img-phone">
                                <img src="<?php echo $person_photo_2['url'] ?>" alt="<?php echo get_the_title(); ?>">
                            </div>
                        </div>
                    <?php } ?>
                    <div class="designation">
                        <h1><?php echo get_the_title(); ?></h1>
                        <h2><?php echo $roles; ?></h2>
                        <h2><?php echo $locations; ?></h2>
                    </div>
                    <div class="box"></div>
                    <h6><?php echo __($trans['contact'], 'picostrap5-child-base'); ?></h6>
                    <ul>
                        <?php
                        $location = wp_get_post_terms(get_the_ID(),);
                        ?>
                        <li><a hreflang="<?php echo esc_attr($pll); ?>" href="tel:<?php echo get_field('telephone'); ?>"><?php echo get_field('telephone'); ?></a></li>
                        <li><a hreflang="<?php echo esc_attr($pll); ?>" href="mailto:<?php echo get_field('email'); ?>"><?php echo get_field('email'); ?></a></li>
                        <li class="social-link <?php echo get_field('linkedin') ? '' : 'd-none'; ?>"><a hreflang="<?php echo esc_attr($pll); ?>" target="_blank" href="<?php echo get_field('linkedin'); ?>"><i class='bx bxl-linkedin'></i></a></li>
                    </ul>
                    <?php if (!empty($vcard_link)) : ?>
                        <a class="downloadvcard" hreflang="<?php echo esc_attr($pll); ?>" href="<?php echo $vcard_link; ?>">
                            <img src="<?php echo get_stylesheet_directory_uri(); ?>/assets/images/icon-download-v-card.svg" alt="v-card"> Download V-card
                        </a>
                    <?php endif; ?>
                    <h6 class="<?php echo get_field('address') ? '' : 'd-none'; ?>"><?php echo __('Address', 'picostrap5-child-base'); ?></h6>
                    <div>
                        <ul>
                            <li><?php echo get_field('address'); ?></li>
                            <li><?php echo get_field('zipcode'); ?></li>
                        </ul>
                    </div>
                    <h6 class="<?php echo get_field('professional_associations_and_memberships') ? '' : 'd-none'; ?>"><?php echo __($trans['memberships'], 'picostrap5-child-base'); ?></h6>
                    <div>
                        <?php echo get_field('professional_associations_and_memberships'); ?>
                    </div>

                    <h6 class="<?php echo get_field('qualifications') ? '' : 'd-none'; ?>"><?php echo __($trans['qualifications'], 'picostrap5-child-base'); ?></h6>
                    <div>
                        <?php echo get_field('qualifications'); ?>
                    </div>



                    <div class="responsive-dv d-none">
                        <?php
                        $person_photo_2 = get_field('person_photo_2');
                        if ($person_photo_2 && get_field('cover_profile_photo')) { ?>
                            <div class="mt--50 person-profile-pic-box">
                                <div class="person-profile-img">
                                    <img src="<?php echo $person_photo_2['url'] ?>" alt="<?php echo get_the_title(); ?>">
                                </div>
                            </div>
                        <?php } ?>
                        <div class="mt--50 person-main-content-box">
                            <?php
                            if (isset($_GET['referer']) && $_GET['referer'] == 'sector' && isset($_GET['ref']) && $_GET['ref'] == 'ai') {
                                $content = get_field('bio_content_2');
                                if ($content) {
                                    echo $content;
                                } else {
                                    the_content();
                                }
                            } else {
                                the_content();
                            }
                            ?>
                        </div>
                        <?php /*
                        <div class="experience-block <?php echo get_field('recent_work') ? 'd-none' : 'd-none'; ?>">
                            <div class="subtitle">
                                <h6><?php echo __('Recent experience', 'picostrap5-child-base'); ?></h6>
                            </div>
                            <div class="experice-text">
                                <?php echo get_field('recent_work'); ?>
                            </div>
                        </div> */ ?>

                        <?php
                        $hide_recent_exp = true;
                        if (isset($_GET['referer']) && $_GET['referer'] == 'sector' && isset($_GET['ref']) && $_GET['ref'] == 'ai') {
                            if (get_field('recent_experience_2')) {
                                $hide_recent_exp = false;
                            }
                        } else {
                            if (get_field('recent_experience_statements')) {
                                $hide_recent_exp = false;
                            }
                        }
                        ?>

                        <div class="experience-block <?php echo $hide_recent_exp ? 'd-none' : ''; ?>">
                            <div class="subtitle">
                                <h6><?php echo __($trans['recent-experience'], 'picostrap5-child-base'); ?></h6>
                            </div>
                            <div class="experice-text">
                                <?php
                                if (isset($_GET['referer']) && $_GET['referer'] == 'sector' && isset($_GET['ref']) && $_GET['ref'] == 'ai') {
                                    echo get_field('recent_experience_2');
                                } else {
                                    echo get_field('recent_experience_statements');
                                }
                                ?>
                            </div>
                        </div>

                        <?php
                        $hide_testimonial = true;
                        if (isset($_GET['referer']) && $_GET['referer'] == 'sector' && isset($_GET['ref']) && $_GET['ref'] == 'ai') {
                            if (get_field('testimonials_and_recognitions_2')) {
                                $hide_testimonial = false;
                            }
                        } else {
                            if (get_field('testimonials_and_recognitions')) {
                                $hide_testimonial = false;
                            }
                        }
                        ?>
                        <div class="experience-block testimonial-block <?php echo $hide_testimonial ? 'd-none' : ''; ?>">
                            <?php if (!empty(get_field('testimonials_and_recognitions'))) : ?>
                                <div class="subtitle">
                                    <h6><?php echo __('Testimonials and recognitions', 'picostrap5-child-base'); ?></h6>
                                </div>
                            <?php endif; ?>
                            <div class="experice-text">
                                <?php
                                if (isset($_GET['referer']) && $_GET['referer'] == 'sector' && isset($_GET['ref']) && $_GET['ref'] == 'ai') {
                                    echo get_field('testimonials_and_recognitions_2');
                                } else {
                                    echo get_field('testimonials_and_recognitions');
                                }
                                ?>
                            </div>
                        </div>

                        <div class="experience-block testimonial-block <?php echo get_field('staff_testimonials') ? 'd-none' : 'd-none'; ?>">
                            <div class="subtitle">
                                <h6><?php // echo __('Testimonials', 'picostrap5-child-base'); 
                                    ?></h6>
                            </div>
                            <?php // echo get_field('staff_testimonials'); 
                            ?>
                            <!-- <div class="testi-single">
                                <p>“Lorem ipsum dolor sit amet, consectetur adipiscing elit.”</p>
                                <span>CHAMBERS UK 2022</span>
                            </div>
                            <div class="testi-single">
                                <p>“Lorem ipsum dolor sit amet, consectetur adipiscing elit. Pellentesque
                                    quis bibendum diam. Vestibulum sem ligula, malesuada sed tortor quis,
                                    commodo viverra urna. ”</p>
                                <span>LEGAL 500 2022</span>
                            </div>
                            <div class="testi-single">
                                <p>“Lorem ipsum dolor sit amet, consectetur adipiscing elit.”</p>
                                <span>CHAMBERS AND PARTNERS 2021</span>
                            </div>
                            <div class="testi-single">
                                <p>“Lorem ipsum dolor sit amet, consectetur adipiscing elit. Pellentesque quis
                                    bibendum diam. Vestibulum sem ligula, malesuada.”</p>
                                <span>LEGAL 500 2021</span>
                            </div>
                            <div class="testi-single">
                                <p>“Pellentesque quis bibendum diam. Vestibulum sem ligula, malesuada
                                    sed tortor quis, commodo viverra urna.”</p>
                                <span>LEGAL 500 2020</span>
                            </div> -->

                        </div>

                        <?php
                        $publications = get_field('publications');
                        $class = '';
                        if (strtoupper(trim(strip_tags($publications))) == 'N/A') {
                            $class = 'd-none';
                        }
                        if (trim(strip_tags($publications)) == '') {
                            $class = 'd-none';
                        }
                        ?>
                        <div class="experience-block publication-block <?php echo $class; ?>">
                            <div class="subtitle">
                                <h6><?php echo __('Publications', 'picostrap5-child-base'); ?></h6>
                            </div>
                            <div class="experice-text publication-text">
                                <!-- <h6>Pellentesque quis bibendum diam. </h6> -->
                                <!-- <p>Vestibulum sem ligula, malesuada sed tortor quis, commodo viverra urna</p> -->
                                <?php echo get_field('publications'); ?>
                            </div>
                        </div>

                        <div class="experience-block publication-block <?php echo get_field('relevant_search_terms') ? 'd-none' : 'd-none'; ?>">
                            <div class="subtitle">
                                <h6><?php echo __('Relevant Search Terms', 'picostrap5-child-base'); ?></h6>
                            </div>
                            <div class="experice-text publication-text">
                                <!-- <h6>Pellentesque quis bibendum diam. </h6> -->
                                <!-- <p>Vestibulum sem ligula, malesuada sed tortor quis, commodo viverra urna</p> -->
                                <?php // echo get_field('relevant_search_terms'); 
                                ?>
                            </div>
                        </div>

                        <div class="publication-block <?php echo get_field('recommended_sitesblogs') ? 'd-none' : 'd-none'; ?>">
                            <div class="subtitle">
                                <h6><?php echo __('Recommended Sites', 'picostrap5-child-base'); ?></h6>
                            </div>
                            <div class="publication-text">
                                <!-- <h6>Pellentesque quis bibendum diam. </h6> -->
                                <!-- <p>Vestibulum sem ligula, malesuada sed tortor quis, commodo viverra urna</p> -->
                                <?php echo get_field('recommended_sitesblogs'); ?>
                            </div>
                        </div>

                        <div class="publication-block <?php echo get_field('suggested_media') ? 'd-none' : 'd-none'; ?>">
                            <div class="subtitle">
                                <h6><?php echo __('Suggested Media', 'picostrap5-child-base'); ?></h6>
                            </div>
                            <div class="publication-text">
                                <!-- <h6>Pellentesque quis bibendum diam. </h6> -->
                                <!-- <p>Vestibulum sem ligula, malesuada sed tortor quis, commodo viverra urna</p> -->
                                <?php echo get_field('suggested_media'); ?>
                            </div>
                        </div>
                        <div class="publication-block <?php echo get_field('recognitions') ? 'd-none' : 'd-none'; ?>">
                            <div class="subtitle">
                                <h6><?php echo __('Recognitions', 'picostrap5-child-base'); ?></h6>
                            </div>
                            <div class="publication-text">
                                <!-- <h6>Pellentesque quis bibendum diam. </h6> -->
                                <!-- <p>Vestibulum sem ligula, malesuada sed tortor quis, commodo viverra urna</p> -->
                                <?php echo get_field('recognitions'); ?>
                            </div>
                        </div>
                        <div class="publication-block <?php echo get_field('additional_info') ? 'd-none' : 'd-none'; ?>">
                            <div class="subtitle">
                                <h6><?php echo __('Additional Info', 'picostrap5-child-base'); ?></h6>
                            </div>
                            <div class="publication-text">
                                <!-- <h6>Pellentesque quis bibendum diam. </h6> -->
                                <!-- <p>Vestibulum sem ligula, malesuada sed tortor quis, commodo viverra urna</p> -->
                                <?php // echo get_field('additional_info'); 
                                ?>
                            </div>
                        </div>
                    </div>


                    <h6 class="<?php echo get_field('language') ? '' : 'd-none'; ?>"><?php echo __($trans['languages'], 'picostrap5-child-base'); ?></h6>
                    <div>
                        <?php echo get_field('language'); ?>
                    </div>


                </div>
                <div class="quat-box-wrap">
                    <?php if (!empty(get_field('partner_quote')) || !empty(get_field('partner_quote'))) : ?>
                        <div class="quote-box">
                            <!-- <p>Lorem ipsum dolor sit amet, consectetuer adipiscing elit, sed diam nonummy
                                nibh euismod tincidunt</p>
                                <span>LEGAL 500 2022</span> -->
                            <?php
                            if ( isset($_GET['referer']) && $_GET['referer'] == 'sector' && isset($_GET['ref']) && $_GET['ref'] == 'ai' ) {
                                echo get_field('partner_quote_2');
                            } else {
                                echo get_field('partner_quote');
                            }
                            ?>
                        </div>
                    <?php endif; ?>
                </div>
                <!-- new -->
                <?php
                // Check if there are any sectors or sub-sectors selected
                $sectors = get_field('sectors');
                $sub_sectors = get_field('sub_sectors');

                // Only render the div if there is content to display
                if (!empty($sectors) || !empty($sub_sectors)) {
                ?>
                    <div class="experience-box <?php echo $no_qoute; ?>">
                        <?php
                        // Display sub-sectors if they exist
                        if (!empty($sub_sectors) && empty($sectors)) {
                            echo $sub_sectors;
                        } else {
                            // If sectors are selected, proceed with fetching and displaying them
                            $_sectors = wp_list_pluck($sectors, 'ID');

                            // Arguments to fetch selected parent sectors
                            $arg = array(
                                'post_type' => 'sector',
                                'posts_per_page' => '-1',
                                'post_status' => 'publish',
                                'post_parent' => '0',
                                'orderby' => 'title',
                                'order' => 'ASC',
                                'post__in' => $_sectors,
                            );
                            $s = get_posts($arg);

                            // If no parent sectors, fetch all matching sectors
                            if (empty($s)) {
                                unset($arg['post_parent']);
                                $s = get_posts($arg);
                            }

                            // Display sectors if they exist
                            if ($s) {
                                echo '<h5 class="title">' . __('Sector Experience', 'picostrap5-child-base') . '</h5>';
                                foreach ($s as $sector) {
                        ?>
                                    <h6 class="subtitle"><a hreflang="<?php echo esc_attr($pll); ?>" href="<?php echo get_permalink($sector->ID); ?>"><?php echo $sector->post_title; ?></a></h6>
                                    <?php
                                    // Arguments to fetch child sectors
                                    $arg = array(
                                        'post_type' => 'sector',
                                        'posts_per_page' => '-1',
                                        'post_status' => 'publish',
                                        'orderby' => 'title',
                                        'order' => 'ASC',
                                        'post_parent' => $sector->ID,
                                        'post__in' => $_sectors,
                                    );
                                    $sec = get_posts($arg);
                                    // Display child sectors if they exist
                                    if ($sec) {
                                        echo '<ul>';
                                        foreach ($sec as $_sector) {
                                    ?>
                                            <li><?php echo $_sector->post_title; ?></li>
                        <?php
                                        }
                                        echo '</ul>';
                                    }
                                }
                            }
                        }
                        ?>
                    </div>
                <?php
                }
                ?>



            </div>

            <div class="col-lg-5 mobile-hdn">
                <?php
                $person_photo_2 = get_field('person_photo_2');
                if ($person_photo_2 && get_field('cover_profile_photo')) { ?>
                    <div class="mt--50 person-profile-pic-box">
                        <div class="person-profile-img">
                            <img src="<?php echo $person_photo_2['url'] ?>" alt="<?php echo get_the_title(); ?>">
                        </div>
                    </div>
                <?php } ?>
                <div class="mt--50 person-main-content-box">
                    <?php
                    if (isset($_GET['referer']) && $_GET['referer'] == 'sector' &&  isset( $_GET['ref'] ) && $_GET['ref'] == 'ai' ) {
                        $content = get_field('bio_content_2');
                        if ($content) {
                            echo $content;
                        } else {
                            the_content();
                        }
                    } else {
                        the_content();
                    }
                    ?>
                </div>
                <?php /* 
                <div class="experience-block <?php echo get_field('recent_work') ? 'd-none' : 'd-none'; ?>">
                    <div class="subtitle">
                        <h6><?php echo __('Recent experience', 'picostrap5-child-base'); ?></h6>
                    </div>
                    <div class="experice-text">
                        <?php echo get_field('recent_work'); ?>
                    </div>
                </div> */ ?>

                <?php
                $hide_recent_exp = true;
                if (isset($_GET['referer']) && $_GET['referer'] == 'sector' && $_GET['ref'] == 'ai') {
                    // $hide_recent_exp = true;
                    $hide_recent_exp = false;
                } elseif (isset($_GET['referer']) && $_GET['referer'] == 'sector') {
                    if (get_field('recent_experience_2')) {
                        $hide_recent_exp = false;
                    }
                } else {
                    if (get_field('recent_experience_statements')) {
                        $hide_recent_exp = false;
                    }
                }
                ?>

                <div class="experience-block <?php echo ($hide_recent_exp || ( $_GET['ref'] == 'ai' && empty(get_field('recent_experience_2') ) || empty(get_field('recent_experience_statements')))) ? 'd-none' : ''; ?> stgd">
                    <div class="subtitle">
                        <h6><?php echo __($trans['recent-experience'], 'picostrap5-child-base'); ?></h6>
                    </div>
                    <div class="experice-text">
                        <?php
                        if ( isset($_GET['referer']) && $_GET['referer'] == 'sector' && isset($_GET['ref']) && $_GET['ref'] == 'ai' ) {
                            echo get_field('recent_experience_2');
                        } else {
                            echo get_field('recent_experience_statements');
                        }
                        ?>
                    </div>
                </div>

                <?php
                $hide_testimonial = true;
                if ( isset($_GET['referer']) && $_GET['referer'] == 'sector' && isset($_GET['ref']) && $_GET['ref'] == 'ai' ) {
                    if (get_field('testimonials_and_recognitions_2')) {
                        $hide_testimonial = false;
                    }
                } else {
                    if (get_field('testimonials_and_recognitions')) {
                        $hide_testimonial = false;
                    }
                }
                ?>
                <div class="experience-block testimonial-block <?php echo $hide_testimonial ? 'd-none' : ''; ?>">
                    <?php if (!empty(get_field('testimonials_and_recognitions'))) : ?>
                        <div class="subtitle">
                            <h6><?php echo __('Testimonials and recognitions', 'picostrap5-child-base'); ?></h6>
                        </div>
                    <?php endif; ?>
                    <div class="experice-text">
                        <?php
                        if ( isset($_GET['referer']) && $_GET['referer'] == 'sector' && isset($_GET['ref']) && $_GET['ref'] == 'ai' ) {
                            echo get_field('testimonials_and_recognitions_2');
                        } else {
                            echo get_field('testimonials_and_recognitions');
                        }
                        ?>
                    </div>
                </div>

                <div class="experience-block testimonial-block <?php echo get_field('staff_testimonials') ? 'd-none' : 'd-none'; ?>">
                    <div class="subtitle">
                        <h6><?php // echo __('Testimonials', 'picostrap5-child-base'); 
                            ?></h6>
                    </div>
                    <?php // echo get_field('staff_testimonials'); 
                    ?>
                    <!-- <div class="testi-single">
                                <p>“Lorem ipsum dolor sit amet, consectetur adipiscing elit.”</p>
                                <span>CHAMBERS UK 2022</span>
                            </div>
                            <div class="testi-single">
                                <p>“Lorem ipsum dolor sit amet, consectetur adipiscing elit. Pellentesque
                                    quis bibendum diam. Vestibulum sem ligula, malesuada sed tortor quis,
                                    commodo viverra urna. ”</p>
                                <span>LEGAL 500 2022</span>
                            </div>
                            <div class="testi-single">
                                <p>“Lorem ipsum dolor sit amet, consectetur adipiscing elit.”</p>
                                <span>CHAMBERS AND PARTNERS 2021</span>
                            </div>
                            <div class="testi-single">
                                <p>“Lorem ipsum dolor sit amet, consectetur adipiscing elit. Pellentesque quis
                                    bibendum diam. Vestibulum sem ligula, malesuada.”</p>
                                <span>LEGAL 500 2021</span>
                            </div>
                            <div class="testi-single">
                                <p>“Pellentesque quis bibendum diam. Vestibulum sem ligula, malesuada
                                    sed tortor quis, commodo viverra urna.”</p>
                                <span>LEGAL 500 2020</span>
                            </div> -->

                </div>

                <?php
                $publications = get_field('publications');
                $class = '';
                if (strtoupper(trim(strip_tags($publications))) == 'N/A') {
                    $class = 'd-none';
                }
                if (trim(strip_tags($publications)) == '') {
                    $class = 'd-none';
                }
                ?>
                <div class="experience-block publication-block <?php echo $class; ?>">
                    <div class="subtitle">
                        <h6><?php echo __('Publications', 'picostrap5-child-base'); ?></h6>
                    </div>
                    <div class="experice-text publication-text">
                        <!-- <h6>Pellentesque quis bibendum diam. </h6> -->
                        <!-- <p>Vestibulum sem ligula, malesuada sed tortor quis, commodo viverra urna</p> -->
                        <?php echo get_field('publications'); ?>
                    </div>
                </div>

                <div class="experience-block publication-block <?php echo get_field('relevant_search_terms') ? 'd-none' : 'd-none'; ?>">
                    <div class="subtitle">
                        <h6><?php echo __('Relevant Search Terms', 'picostrap5-child-base'); ?></h6>
                    </div>
                    <div class="experice-text publication-text">
                        <!-- <h6>Pellentesque quis bibendum diam. </h6> -->
                        <!-- <p>Vestibulum sem ligula, malesuada sed tortor quis, commodo viverra urna</p> -->
                        <?php // echo get_field('relevant_search_terms'); 
                        ?>
                    </div>
                </div>

                <div class="publication-block <?php echo get_field('recommended_sitesblogs') ? 'd-none' : 'd-none'; ?>">
                    <div class="subtitle">
                        <h6><?php echo __('Recommended Sites', 'picostrap5-child-base'); ?></h6>
                    </div>
                    <div class="publication-text">
                        <!-- <h6>Pellentesque quis bibendum diam. </h6> -->
                        <!-- <p>Vestibulum sem ligula, malesuada sed tortor quis, commodo viverra urna</p> -->
                        <?php echo get_field('recommended_sitesblogs'); ?>
                    </div>
                </div>

                <div class="publication-block <?php echo get_field('suggested_media') ? 'd-none' : 'd-none'; ?>">
                    <div class="subtitle">
                        <h6><?php echo __('Suggested Media', 'picostrap5-child-base'); ?></h6>
                    </div>
                    <div class="publication-text">
                        <!-- <h6>Pellentesque quis bibendum diam. </h6> -->
                        <!-- <p>Vestibulum sem ligula, malesuada sed tortor quis, commodo viverra urna</p> -->
                        <?php echo get_field('suggested_media'); ?>
                    </div>
                </div>
                <div class="publication-block <?php echo get_field('recognitions') ? 'd-none' : 'd-none'; ?>">
                    <div class="subtitle">
                        <h6><?php echo __('Recognitions', 'picostrap5-child-base'); ?></h6>
                    </div>
                    <div class="publication-text">
                        <!-- <h6>Pellentesque quis bibendum diam. </h6> -->
                        <!-- <p>Vestibulum sem ligula, malesuada sed tortor quis, commodo viverra urna</p> -->
                        <?php echo get_field('recognitions'); ?>
                    </div>
                </div>
                <div class="publication-block <?php echo get_field('additional_info') ? 'd-none' : 'd-none'; ?>">
                    <div class="subtitle">
                        <h6><?php echo __('Additional Info', 'picostrap5-child-base'); ?></h6>
                    </div>
                    <div class="publication-text">
                        <!-- <h6>Pellentesque quis bibendum diam. </h6> -->
                        <!-- <p>Vestibulum sem ligula, malesuada sed tortor quis, commodo viverra urna</p> -->
                        <?php // echo get_field('additional_info'); 
                        ?>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- ========== people-info section start ============= -->

<!-- ========== insights section start ============= -->
<?php $auto_list_of_insights = get_field('auto_list_of_insights'); ?>
<?php $auto_list_of_insights = $auto_list_of_insights === NULL || $auto_list_of_insights ? true : false; ?>
<?php $insights = get_field('insights'); ?>
<?php
$args_trending = array(
    'post_type' => array('trending_topics'),
    'posts_per_page' => 12,
    'orderby' => 'modified',
    'orderby' => 'date',
    'order' => 'DESC',
    'post_status' => 'publish',

    'meta_query' => array(
        array(
            'key' => 'people',
            'value' => '"' . get_the_ID() . '"',
            'compare' => 'LIKE',
        ),
    ),
);

$query_trendin = new WP_Query($args_trending);



$args = array(
    'post_type' => array('bulletin', 'news', 'upc_news'),
    'posts_per_page' => 12,
    'orderby' => 'modified',
    'orderby' => 'date',
    'order' => 'DESC',
    'post_status' => 'publish',

    'meta_query' => array(
        array(
            'key' => 'people',
            'value' => '"' . get_the_ID() . '"',
            'compare' => 'LIKE',
        ),
    ),
);

$query = new WP_Query($args);
?>

<div class="insights-section pt-100 position-relative <?php echo $insights || $query->have_posts() ? '' : 'd-none'; ?>">
    <div class="container-one">
        <div class="section-title-one style-yellow">
            <h2><?php echo __('Insights', 'picostrap5-child-base'); ?></h2>
        </div>
        <div class="row justify-content-centerr mb-40 g-lg-4 g-3">
            <?php
            if ($query_trendin->have_posts()) {
                while ($query_trendin->have_posts()) {
                    $query_trendin->the_post();
                    $post_type = get_post_type();

                    $permalink = '';
                    if ($pll == 'es') {
                        if ($post_type == 'bulletin') {
                            $permalink = str_replace("/es/Bolet%C3%ADn/", "/bulletin/", get_permalink());
                        } elseif ($post_type == 'news') {
                            $permalink = str_replace("/es/noticias/", "/news/", get_permalink());
                        } elseif ($post_type == 'upc_news') {
                            $permalink = str_replace("/es/", "/", get_permalink());
                        }
                    } elseif ($pll == 'de') {
                        if ($post_type == 'bulletin') {
                            $permalink = str_replace("/de/bulletin/", "/bulletin/", get_permalink());
                        } elseif ($post_type == 'news') {
                            $permalink = str_replace("/de/news/", "/news/", get_permalink());
                        } elseif ($post_type == 'upc_news') {
                            $permalink = str_replace("/de/", "/", get_permalink());
                        }
                    } else {
                        $permalink = get_permalink();
                    }

                    $postType = get_post_type_object(get_post_type($post));

            ?>
                    <div class="col-lg-3 col-md-6 col-12">
                        <div class="insight-item style-yellow">
                            <?php // if(!empty(get_the_post_thumbnail_url())) :
                            ?>
                            <div class="image">
                                <a hreflang="<?php echo esc_attr($pll); ?>" href="<?php echo $permalink; ?>"><img src="<?php echo has_post_thumbnail() ? get_the_post_thumbnail_url($b) : home_url('/wp-content/uploads/2023/06/iStock-97970805-scaled.jpg'); ?>" alt="image"></a>
                            </div>
                            <?php // endif; 
                            ?>
                            <div class="content">
                                <h6><a hreflang="<?php echo esc_attr($pll); ?>" href="<?php echo $permalink; ?>"><?php echo __($postType->labels->singular_name, 'picostrap5-child-base'); // __('Bulletin', 'picostrap5-child-base'); 
                                                                        ?></a></h6>
                                <p><?php the_title(); ?></p>

                                <?php
                                $moreText = '';
                                if ($pll == 'de') {
                                    $moreText = 'Mehr';
                                } elseif ($pll == 'es') {
                                    $moreText = 'MÁS';
                                } else {
                                    $moreText = 'More';
                                }
                                ?>
                                <a hreflang="<?php echo esc_attr($pll); ?>" href="<?php echo $permalink; ?>"><?php echo $moreText; //__('MORE', 'picostrap5-child-base'); 
                                                                    ?></a>
                            </div>
                        </div>
                    </div>
            <?php
                }
            }
            wp_reset_postdata();
            ?>
            <?php
            if ($auto_list_of_insights) {
                if ($query->have_posts()) {
                    while ($query->have_posts()) {
                        $query->the_post();
                        $post_type = get_post_type();

                        $permalink = '';
                        if ($pll == 'es') {
                            if ($post_type == 'bulletin') {
                                $permalink = str_replace("/es/Bolet%C3%ADn/", "/bulletin/", get_permalink());
                            } elseif ($post_type == 'news') {
                                $permalink = str_replace("/es/noticias/", "/news/", get_permalink());
                            } elseif ($post_type == 'upc_news') {
                                $permalink = str_replace("/es/", "/", get_permalink());
                            }
                        } elseif ($pll == 'de') {
                            if ($post_type == 'bulletin') {
                                $permalink = str_replace("/de/bulletin/", "/bulletin/", get_permalink());
                            } elseif ($post_type == 'news') {
                                $permalink = str_replace("/de/news/", "/news/", get_permalink());
                            } elseif ($post_type == 'upc_news') {
                                $permalink = str_replace("/de/", "/", get_permalink());
                            }
                        } else {
                            $permalink = get_permalink();
                        }

                        $postType = get_post_type_object(get_post_type($post));
            ?>
                        <div class="col-lg-3 col-md-6 col-12">
                            <div class="insight-item style-yellow">
                                <?php // if(!empty(get_the_post_thumbnail_url())) :
                                ?>
                                <div class="image">
                                    <a href="<?php echo $permalink; ?>"><img src="<?php echo has_post_thumbnail() ? get_the_post_thumbnail_url($b) : home_url('/wp-content/uploads/2023/06/iStock-97970805-scaled.jpg'); ?>" alt="image"></a>
                                </div>
                                <?php // endif; 
                                ?>
                                <div class="content">
                                    <h6><a href="<?php echo $permalink; ?>"><?php echo __($postType->labels->singular_name, 'picostrap5-child-base'); // __('Bulletin', 'picostrap5-child-base'); 
                                                                            ?></a></h6>
                                    <p><?php the_title(); ?></p>

                                    <?php
                                    $moreText = '';
                                    if ($pll == 'de') {
                                        $moreText = 'Mehr';
                                    } elseif ($pll == 'es') {
                                        $moreText = 'MÁS';
                                    } else {
                                        $moreText = 'More';
                                    }
                                    ?>
                                    <a hreflang="<?php echo esc_attr($pll); ?>" href="<?php echo $permalink; ?>"><?php echo $moreText; //__('MORE', 'picostrap5-child-base'); 
                                                                        ?></a>
                                </div>
                            </div>
                        </div>
                    <?php
                    }
                }
                wp_reset_postdata();
            } else {
                if ($insights) {
                    foreach ($insights as $value) {
                        // $postType = get_post_type_object(get_post_type($value));
                    ?>
                        <div class="col-lg-3 col-md-6 col-6">
                            <div class="insight-item style-yellow">
                                <?php // if(!empty(get_the_post_thumbnail_url())) :
                                ?>
                                <div class="image">
                                    <a hreflang="<?php echo esc_attr($pll); ?>" href="<?php echo get_the_permalink($value); ?>"><img src="<?php echo has_post_thumbnail($value->ID) ? get_the_post_thumbnail_url($value, 'full') : home_url('/wp-content/uploads/2023/06/iStock-97970805-scaled.jpg'); ?>" alt="image"></a>
                                </div>
                                <?php // endif; 
                                ?>
                                <div class="content">
                                    <h6><a hreflang="<?php echo esc_attr($pll); ?>" href="<?php echo get_the_permalink($value); ?>"><?php // echo __($postType->labels->singular_name, 'picostrap5-child-base'); // __('Bulletin', 'picostrap5-child-base'); 
                                                                                            ?></a></h6>
                                    <p><?php echo get_the_title($value); ?></p>
                                    <a hreflang="<?php echo esc_attr($pll); ?>" href="<?php echo get_the_permalink($value); ?>"><?php echo __('MORE', 'picostrap5-child-base'); ?></a>
                                </div>
                            </div>
                        </div>
            <?php
                    }
                }
            }
            ?>

            <!-- <div class="col-lg-3 col-md-6 col-6" >
                    <div class="insight-item style-yellow">
                        <div class="image">
                            <img src="assets/images/insights/insight-1.jpg" alt="image">
                        </div>
                        <div class="content">
                            <h6><a href="insight-details.html">INSIGHTS</a></h6>
                            <p>Lorem ipsum dolor sit
                                amet, consectetuer.</p>
                            <a href="insight-details.html">MORE</a>
                        </div>
                    </div>
                </div>
                <div class="col-lg-3 col-md-6 col-6" >
                    <div class="insight-item style-yellow">
                        <div class="image">
                            <img src="assets/images/insights/insight-2.jpg" alt="image">
                        </div>
                        <div class="content">
                            <h6><a href="insight-details.html">NEWS</a></h6>
                            <p>Lorem ipsum dolor sit
                                amet, consectetuer.</p>
                            <a href="insight-details.html">MORE</a>
                        </div>
                    </div>
                </div>
                <div class="col-lg-3 col-md-6 col-6" >
                    <div class="insight-item style-yellow">
                        <div class="image">
                            <img src="assets/images/insights/insight-3.jpg" alt="image">
                        </div>
                        <div class="content">
                            <h6><a href="insight-details.html">NEWS</a></h6>
                            <p>Lorem ipsum dolor sit
                                amet, consectetuer.</p>
                            <a href="insight-details.html">MORE</a>
                        </div>
                    </div>
                </div>
                <div class="col-lg-3 col-md-6 col-6" >
                    <div class="insight-item style-yellow">
                        <div class="image">
                            <img src="assets/images/insights/insight-4.jpg" alt="image">
                        </div>
                        <div class="content">
                            <h6><a href="insight-details.html">INSIGHTS</a></h6>
                            <p>Lorem ipsum dolor sit
                                amet, consectetuer.</p>
                            <a href="insight-details.html">MORE</a>
                        </div>
                    </div>
                </div> -->
        </div>
        <a hreflang="<?php echo esc_attr($pll); ?>" target="_blank" href="<?php echo home_url('/insights/'); ?>" class="eg-btn btn--primary-yellow btn--lg2"><?php echo __('INSIGHTS', 'picostrap5-child-base'); ?><i class="bi bi-arrow-right"></i></a>
    </div>
</div>

<!-- ========== insights section end ============= -->


<?php get_footer();
