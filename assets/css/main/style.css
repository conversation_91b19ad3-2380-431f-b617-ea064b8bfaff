@import url(https://fonts.googleapis.com/css2?family=Open+Sans:ital,wght@0,300;0,400;0,500;0,600;0,700;0,800;1,300;1,400;1,500;1,600;1,700&display=swap);
*,
html {
  scroll-behavior: smooth;
}
*,
body {
  margin: 0;
  padding: 0;
}
body,
h1,
h2,
h3,
h4,
h5,
h6 {
  color: var(--text-primary);
  font-family: var(--font-open);
}
.eg-btn,
body,
h1,
h2,
h3,
h4,
h5,
h6 {
  font-family: var(--font-open);
}
.btn--lg,
.btn--lg2 {
  font-size: 13px;
  text-transform: capitalize;
}
.btn--lg,
.btn--lg2,
.eg-btn {
  text-transform: capitalize;
}
header.style-1 .main-nav > ul > li a,
ul.language-list > li a {
  padding: 30px 0;
  font-family: var(--font-work-sans);
  transition: 0.5s ease-out;
  text-decoration: none;
  text-transform: uppercase;
}
header.style-1 .main-nav > ul > li ul.sub-menu > li a::after,
ul.language-list > li a::after {
  background: linear-gradient(90deg, var(--primary-color1), transparent);
  border-radius: 30px;
}
/* .banner-section,
.page-template-template-career-experienced-attorneys .banner-section,
.page-template-template-insights .banner-section,
.page-template-template-ip-basics .banner-section,
.page-template-template-people-list-filter .banner-section,
.page-template-template-responsible-business .banner-section {
  min-height: 620px;
} */
:root {
  --text-primary: rgb(29, 29, 27);
  --border: rgba(29, 29, 27, 0.3);
  --text-secondary: #575756;
  --white: #ffffff;
  --dark-bg1: #3c3c3b;
  --dark-bg2: #1d1d1b;
  --primary-red: rgb(227, 6, 19);
  --primary-red-light: rgba(227, 6, 19, 0.04);
  --primary-yellow: rgba(249, 178, 51);
  --primary-yellow-light: rgb(254, 243, 224);
  --primary-yellow-light2: #f9b23314;
  --primary-blue: #36a9e1;
  --primary-blue-light: #f1f9fd;
  --primary-green: #94c11f;
  --primary-green-dark: rgba(58, 170, 53, 1);
  --primary-green-dark-light: #f3faf3;
  --primary-green-light: rgba(147, 213, 0, 0.04);
  --font-open: "Open Sans", sans-serif;
}
html {
  font-size: 100%;
}
body {
  font-size: 15px;
  font-weight: 200;
  line-height: 1.5;
  box-sizing: border-box;
}
h1,
h2,
h3,
h4,
h5,
h6 {
  font-weight: 600;
  line-height: 1.4;
}
button {
  outline: 0;
  border: none;
}
i.bx {
  vertical-align: middle;
}
img {
  max-width: 100%;
  height: auto;
}
a {
  text-decoration: none;
}
.pt-40 {
  padding-top: 40px;
}
.pb-40 {
  padding-bottom: 40px;
}
.mb-140 {
  margin-bottom: 140px;
}
.pt-50 {
  padding-top: 50px;
}
.new-section-top-index,
.page-template-template-expertise .banner-section + .container-one,
.page-template-template-expertise .banner-section + .people-section,
.page-template-template-people-list-filter .banner-section + .container-one,
.page-template-template-people-list-filter .banner-section + .people-section,
.pt-60 {
  padding-top: 60px;
}
.pb-60 {
  padding-bottom: 60px;
}
.postid-6947 .banner-section + .container-one,
.pt-100,
.sjb-archive-page,
.sjb-page .sjb-detail {
  padding-top: 100px;
}
.pt-240 {
  padding-top: 240px;
}
.pt-150,
.single-service .banner-section + .people-section {
  padding-top: 160px;
}
.pb-100 {
  padding-bottom: 100px;
}
.postid-6945 .banner-section + .container-one,
.pt-120,
.single-ip_basics .banner-section + .container-one {
  padding-top: 120px;
}
.pb-120 {
  padding-bottom: 120px;
}
.pb-70 {
  padding-bottom: 70px;
}
.mb-60,
.page-template-template-career-trainee-placement .recent-highlight-quotes {
  margin-bottom: 60px;
}
.mb-65 {
  margin-bottom: 65px;
}
.mt-120 {
  margin-top: 120px;
}
.mb-120 {
  margin-bottom: 120px;
}
.mb-100,
.page-id-14797 .ip-experts-section.mt-70.mb-500 {
  margin-bottom: 100px;
}
.mobile-search .container-one,
.mt-100 {
  margin-top: 100px !important;
}
.mb-90 {
  margin-bottom: 90px;
}
.address-list li:first-child,
.mb-15,
.section-title-four,
.testimonial-block .testi-single {
  margin-bottom: 15px;
}
.mb-70 {
  margin-bottom: 70px;
}
.mb-45 {
  margin-bottom: 45px !important;
}
.mb-35 {
  margin-bottom: 35px;
}
.eligibility-card .date,
.experience-block,
.mb-20 {
  margin-bottom: 20px;
}
.about-people-card .social-link,
.footer-list .footer-social,
.mt-15,
.publication-block .subtitle h6 {
  margin-top: 15px;
}
.mt-40,
.page-id-642 .apply-card-section {
  margin-top: 40px;
}
.contact-form .form-inner,
.mb-30,
.section-title-three,
.section-title-two,
.section-title-two-borderless {
  margin-bottom: 30px;
}
.mb-25,
.offer-card .list-block,
.t1 {
  margin-bottom: 25px;
}
.mt-50,
.page-template-template-career-recruitment-process .apply-card-section,
.page-template-template-people-list-filter .expertise-section-1 .right-box-blue,
.sjb-page {
  margin-top: 50px;
}
.mt-25 {
  margin-top: 25px;
}
.mb-50 {
  margin-bottom: 50px;
}
.mt-60,
.page-template-template-people-list-filter .expertise-section-1 {
  margin-top: 60px;
}
.mb-40,
.recent-highlight-inner {
  margin-bottom: 40px;
}
.pt-20 {
  padding-top: 20px;
}
.mt-65 {
  margin-top: 65px;
}
.mt-70,
.postid-1360 .focus-list-area {
  margin-top: 70px;
}
.title-pb-150 {
  padding-bottom: 150px;
}
.pl-container {
  padding-left: calc((100% - 1040px) / 2);
}
.pr-container {
  padding-right: calc((100% - 1040px) / 2);
}
.container-one {
  width: 100%;
  max-width: 1040px;
  padding-left: 15px;
  padding-right: 15px;
}
.page-template-home-ger header.style-1 .container-one,
.page-template-home-jp header.style-1 .container-one,
[lang="es-ES"] header.style-1 .container-one {
  max-width: 1230px;
}
.image-adjust {
  background-size: cover;
  background-repeat: no-repeat;
}
.contact-form .mobile-search textarea,
.mobile-search .contact-form textarea,
.mobile-search input {
  border: none;
  border-radius: unset;
  width: 100%;
  background: #fff0;
  transition: 0.3s ease-in-out;
  color: var(--white);
  border-bottom: 1px solid #7d7d7d;
  padding: 5px 0;
}
.contact-form .mobile-search textarea:focus,
.mobile-search .contact-form textarea:focus,
.mobile-search input:focus {
  border-bottom: 1px solid var(--white);
  outline: 0;
}
.contact-form .mobile-search textarea:-ms-input-placeholder,
.mobile-search .contact-form textarea:-ms-input-placeholder,
.mobile-search input:-ms-input-placeholder {
  color: var(--white);
}
.contact-form .mobile-search textarea::placeholder,
.mobile-search .contact-form textarea::placeholder,
.mobile-search input::placeholder {
  color: var(--white);
}
.contact-form .mobile-search textarea::-moz-placeholder,
.mobile-search .contact-form textarea::-moz-placeholder,
.mobile-search input::-moz-placeholder {
  color: var(--white);
  font-size: 14px;
  color: var(--text-secondary);
}
.contact-form .mobile-search textarea:-ms-input-placeholder,
.mobile-search .contact-form textarea:-ms-input-placeholder,
.mobile-search input:-ms-input-placeholder {
  font-size: 14px;
  color: var(--text-secondary);
}
.contact-form .mobile-search textarea::placeholder,
.mobile-search .contact-form textarea::placeholder,
.mobile-search input::placeholder {
  font-size: 14px;
  color: var(--text-secondary);
}
.bg-green-light,
.counter-section,
.search-section.style-green,
.search-section2.style-green {
  background-color: var(--primary-green-dark-light);
}
.section-title-one p {
  font-size: 18px;
  margin-bottom: 10px;
  font-weight: 300;
  line-height: 1.3;
}
.bg-primary-yellow,
.career-card-two.style-yellow,
.career-card.style-yellow .content,
.page-template-template-ip-application-process .sector-item,
.page-template-template-ip-basics .sector-item,
.section-title-one.style-yellow h1::after,
.section-title-one.style-yellow h2::after,
.sector-item-two.style-yellow,
.sector-item.style-yellow,
.title-bg-yellow {
  background-color: var(--primary-yellow);
}
.bg-primary-blue,
.career-card-two.style-blue,
.career-card.style-blue .content,
.page-template-home-ger .sector-item.style-blue:hover,
.search-section.style-deep-blue,
.search-section2.style-deep-blue,
.section-title-one.style-blue h1::after,
.section-title-one.style-blue h2::after,
.sector-item-two.style-blue,
.sector-item.style-blue {
  background-color: var(--primary-blue);
}
.btn--primary-blue::before,
.btn--primary-red::before {
  background: var(--text-primary);
  z-index: -1;
  height: 100%;
  top: 0;
  opacity: 1;
  content: "";
  left: 0;
  width: 0%;
}
.bg-primary-green,
.section-title-one.style-green h1::after,
.section-title-one.style-green h2::after,
.title-bg-green {
  background-color: var(--primary-green);
}
.bg-primary-red,
.section-title-one.style-red h1::after,
.section-title-one.style-red h2::after,
.title-bg-red {
  background-color: var(--primary-red);
}
.btn--primary-blue,
.btn--primary-red {
  border-radius: 0;
  z-index: 1;
  display: inline-flex;
  justify-content: center;
  white-space: nowrap;
  background: #fff0;
  color: var(--white);
}
.section-title-one.style-white h1,
.section-title-one.style-white h2 {
  color: var(--white);
}
.section-title-one.style-white h1::after,
.section-title-one.style-white h2::after,
header.style-1 .header-icons.style-white ul li::after,
ul.language-list.style-white li::after {
  background-color: var(--white);
}
.section-title-one h1,
.section-title-one h2 {
  font-size: 32px;
  font-weight: 300;
  line-height: 1;
  padding-bottom: 15px;
  margin-bottom: 20px;
  display: inline-block;
  position: relative;
}
.section-title-one h1::after,
.section-title-one h2::after {
  content: "";
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 2px;
  display: block;
}
.section-title-two h4 {
  position: relative;
  font-size: 22px;
  font-weight: 600;
  padding-top: 10px;
}
.section-title-two h4::before {
  content: "";
  width: 100%;
  max-width: 310px;
  height: 1px;
  background-color: var(--text-primary);
  position: absolute;
  top: 0;
  left: 0;
}
.section-title-two-borderless h4 {
  position: relative;
  font-size: 24px;
  font-weight: 600;
}
.section-title-three h5 {
  position: relative;
  font-size: 18px;
  font-weight: 800;
}
.section-title-four h4 {
  font-size: 20px;
  font-weight: 600;
  margin-bottom: 0;
  line-height: 1;
  padding-bottom: 15px;
  color: var(--white);
  display: inline-block;
  position: relative;
}
.section-title-four h4::after {
  content: "";
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 2px;
  display: block;
  background-color: var(--white);
}
.banner-section .swiper-slide,
.banner-video-section,
.btn--primary-red,
.business-card-section,
.business-card-section .container-one,
.business-card-section1,
.career-card-section,
.career-green-box,
.culture-section,
.extra_link,
.home-banner-section,
.insight-box-area,
.no-person-image,
.search-block,
.search-section.with-box,
.search-section2.with-box,
.sector-section-v,
.service-section-1 .container-one,
.service-section-2 .container-one,
.skill-section,
.subtitle,
.team-item,
.team-item.style-two .team-image,
form.filter-search-form .form-inner {
  position: relative;
}
.subtitle h6 {
  font-size: 15px;
  font-weight: 700;
  line-height: 1;
}
.eg-btn {
  text-align: center;
  display: inline-flex;
  text-decoration: none;
  transition: 0.45s ease-in-out;
  cursor: pointer;
  border-radius: 0;
}
.btn--primary-blue {
  position: relative;
  transition: 0.5s;
  background: var(--primary-blue);
  text-align: left;
}
.btn--primary-blue::before {
  position: absolute;
  transition: 0.5s;
}
.btn--primary-blue:hover {
  color: var(--white);
}
.btn--primary-blue:hover i {
  right: 0;
  color: var(--white);
}
.btn--primary-blue:hover::before {
  width: 100%;
}
.btn--primary-red {
  transition: 0.5s;
  background: var(--primary-red);
  text-align: left;
}
.btn--primary-black,
.btn--primary-green {
  justify-content: center;
  background: #fff0;
  z-index: 1;
  display: inline-flex;
  transition: 0.5s;
  border-radius: 0;
  position: relative;
  text-align: left;
  white-space: nowrap;
}
.btn--primary-red::before {
  position: absolute;
  transition: 0.5s;
}
.btn--primary-red:hover {
  color: var(--white);
}
.btn--primary-red:hover i {
  right: 0;
  color: var(--white);
}
.btn--primary-red:hover::before {
  width: 100%;
}
.btn--primary-black {
  color: var(--white);
  background: var(--text-primary);
}
.banner-content.style-dark h1,
.banner-content.style-dark h2,
.btn--primary-black.style-two:hover .bi,
.btn--primary-black:hover,
.culture-section .recent-highlight-quotes .box-inner-content a,
.page-template-template-insights .banner-content.style-dark h1,
.page-template-template-responsible-business .banner-content.style-dark h1,
.page-template-template-responsible-business .banner-content.style-dark h2,
.pagination ul li a {
  color: var(--text-primary);
}
.banner-single-post-title .display-44,
.blog-item ul.blog-icon-list li:hover i,
.btn--primary-black.style-two .bi,
.btn--primary-black:hover i,
.btn--primary-green,
.btn--primary-green:hover,
.btn--primary-green:hover i,
.btn--primary-yellow,
.btn--primary-yellow:hover,
.page-template-template-career-recruitment-process .offer-card,
.service-details-wrap P,
.service-details-wrap ul li,
.single-ip_app_process .banner-single-post-title .breadcrumps-wrap span,
.single-ip_basics .banner-single-post-title .breadcrumps-wrap span,
header.style-1 .header-icons.style-white ul li i,
ul.language-list.style-white li a {
  color: var(--white);
}
.btn--primary-black::before {
  content: "";
  width: 0%;
  height: 100%;
  background: var(--white);
  position: absolute;
  left: 0;
  top: 0;
  transition: 0.5s;
  z-index: -1;
  opacity: 1;
}
.btn--primary-black:hover i {
  right: 0;
}
.btn--primary-black:hover::before {
  width: 100%;
}
.btn--primary-green {
  background: var(--primary-green-dark);
}
.btn--primary-green::before {
  content: "";
  width: 0%;
  height: 100%;
  background: var(--text-primary);
  position: absolute;
  left: 0;
  top: 0;
  transition: 0.5s;
  z-index: -1;
  opacity: 1;
}
.btn--primary-green:hover i {
  right: 0;
}
.btn--primary-green:hover::before {
  width: 100%;
}
.btn--primary-yellow {
  border-radius: 0;
  position: relative;
  z-index: 1;
  display: inline-flex;
  justify-content: center;
  white-space: nowrap;
  background: #fff0;
  transition: 0.5s;
  background: var(--primary-yellow);
  text-align: left;
}
.btn--primary-yellow::before {
  content: "";
  width: 0%;
  height: 100%;
  background: var(--text-primary);
  position: absolute;
  left: 0;
  top: 0;
  transition: 0.5s;
  z-index: -1;
  opacity: 1;
}
.btn--primary-yellow:hover i {
  right: 0;
  color: var(--white);
}
.btn--primary-yellow:hover::before,
.single-upc_news .content-area .row > .col-sm-8,
header.style-1 .main-nav > ul > li ul.sub-menu > li a:hover:after {
  width: 100%;
}
.arrow-button i {
  font-size: 60px;
  color: var(--text-secondary);
  opacity: 0.7;
}
.btn--lg {
  font-weight: 600;
  padding: 6px 30px;
}
.btn--lg2 {
  margin-top: 1em;
  font-weight: 600;
  padding: 8px 35px 8px 10px;
  position: relative;
  justify-content: flex-start;
  min-width: 120px;
}
.btn--lg2 i {
  position: absolute;
  right: -15px;
  top: 50%;
  transform: translateY(-50%);
  color: var(--text-primary);
  font-size: 30px;
  display: inline-block;
  transition: 0.4s;
}
.blog-btn {
  min-width: 142px;
}
.btn--md {
  padding: 10px 35px;
  font-size: 15px;
  font-weight: 700;
}
.btn--sm {
  font-size: 12px;
  font-weight: 600;
  padding: 6px 18px;
}
.btn--sm2 {
  font-size: 10px;
  font-weight: 600;
  padding: 3px 15px 3px 5px;
}
.bg-primary-blue-light {
  background-color: var(--primary-blue-light1);
}
.bg-primary-blue-light2 {
  background-color: var(--primary-blue-light2);
}
.bg-primary-green-light {
  background-color: var(--primary-green-light);
}
.bg-primary-yellow-light,
.big-card.style-yellow,
.filter-search-area {
  background-color: var(--primary-yellow-light);
}
.bg-primary-pink-light {
  background-color: var(--primary-pink-light);
}
.bg-primary-pink-light2 {
  background-color: var(--primary-pink-light2);
}
.mobile-search {
  background: rgb(0 0 0 / 0.85);
  box-shadow: 0 0 10px rgb(0 0 0 / 0.09);
  width: 100%;
  height: 100%;
  border-radius: 4px;
  display: flex;
  justify-content: center;
  flex-wrap: wrap;
  align-items: center;
  align-items: center;
  position: fixed;
  cursor: pointer;
  transform: scale(0.7);
  top: 0;
  left: 0;
  z-index: 9999;
  opacity: 0;
  visibility: hidden;
  transition: 0.65s;
  padding: 35px 100px;
}
.banner-section,
.company-vdo {
  background-size: cover;
  background-repeat: no-repeat;
}
.mobile-search label {
  color: #fff;
  margin-bottom: 20px;
  font-family: var(--font-nunito);
}
.mobile-search.slide {
  transform: scale(1);
  opacity: 1;
}
.mobile-search .search-cross-btn {
  color: #fff;
  cursor: pointer;
  background: rgba(var(--white), 0.6);
  border-radius: 50%;
  height: 40px;
  width: 40px;
  text-align: center;
  line-height: 43px;
  transition: 0.5s;
}
.mobile-search .search-cross-btn:hover {
  transform: scale(1.1);
}
.mobile-search .search-cross-btn i,
.page-template-home-ger .quote-box2 h3,
.page-template-home-ger .section-title-one h2,
.page-template-home-jp .quote-box2 h3,
.page-template-home-jp .section-title-one h2,
.page-template-home-kr .section-title-one h2 {
  font-size: 25px;
}
form.filter-search-form {
  z-index: 2;
}
form.filter-search-form .form-inner i {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  left: 20px;
  color: var(--text-secondary);
  opacity: 0.7;
}
.contact-form form.filter-search-form textarea,
form.filter-search-form .contact-form textarea,
form.filter-search-form input {
  border: none;
  border-radius: unset;
  width: 100%;
  background: var(--white);
  transition: 0.3s ease-in-out;
  color: var(--text-primary);
  padding: 25px 20px;
  height: 80px;
}
.contact-form form.filter-search-form textarea:focus,
form.filter-search-form .contact-form textarea:focus,
form.filter-search-form input:focus {
  border: none;
  outline: 0;
}
.contact-form form.filter-search-form textarea::-moz-placeholder,
form.filter-search-form .contact-form textarea::-moz-placeholder,
form.filter-search-form input::-moz-placeholder {
  font-size: 14px;
  color: var(--text-secondary);
  font-weight: 600;
}
.contact-form form.filter-search-form textarea:-ms-input-placeholder,
form.filter-search-form .contact-form textarea:-ms-input-placeholder,
form.filter-search-form input:-ms-input-placeholder {
  font-size: 14px;
  color: var(--text-secondary);
  font-weight: 600;
}
.contact-form form.filter-search-form textarea::placeholder,
form.filter-search-form .contact-form textarea::placeholder,
form.filter-search-form input::placeholder {
  font-size: 14px;
  color: var(--text-secondary);
  font-weight: 600;
}
form.filter-search-form .nice-select {
  -webkit-tap-highlight-color: #fff0;
  background-color: #fff;
  border-radius: 0;
  border: 1px solid #fff0;
  box-sizing: border-box;
  clear: both;
  cursor: pointer;
  display: block;
  float: left;
  font-family: inherit;
  font-size: 14px;
  font-weight: 600;
  height: 80px;
  line-height: 80px;
  outline: 0;
  padding-left: 30px;
  padding-right: 30px;
  position: relative;
  text-align: left !important;
  transition: 0.2s ease-in-out;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  white-space: nowrap;
  width: 100%;
}
form.filter-search-form .nice-select span.current {
  color: var(--text-secondary);
}
form.filter-search-form .nice-select:after {
  border-bottom: 1px solid #999;
  border-right: 1px solid #999;
  content: "";
  display: block;
  height: 8px;
  margin-top: -4px;
  pointer-events: none;
  position: absolute;
  right: 90%;
  top: 50%;
  transform-origin: 66% 66%;
  transform: rotate(315deg);
  transition: 0.15s ease-in-out;
  width: 8px;
}
form.filter-search-form .nice-select .list {
  border-radius: 0;
  width: 100%;
}
header.style-1 {
  background-color: #fff0;
  width: 100%;
  z-index: 99;
  justify-content: center;
  align-items: center;
  position: absolute;
}
header.style-1 .mobile-logo-wrap {
  max-width: 100px;
}
header.style-1 .header-icons {
  position: absolute;
  top: 30px;
  right: -25px;
  width: 100%;
  max-width: 90px;
  min-width: 90px;
}
header.style-1 .header-icons ul {
  list-style: none;
  margin: 0;
  padding: 0;
  display: flex;
  justify-content: flex-end;
  align-items: center;
  gap: 16px;
}
header.style-1 .header-icons ul li {
  position: relative;
  cursor: pointer;
}
header.style-1 .header-icons ul li:last-child::after {
  content: unset;
}
header.style-1 .header-icons ul li::after {
  content: "";
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  right: -8px;
  width: 1px;
  height: 20px;
  background-color: var(--text-primary);
}
header.style-1 .header-icons ul li i {
  color: var(--text-primary);
  font-size: 22px;
  line-height: 1;
}
header.style-1.sticky {
  position: fixed;
  top: 0;
  left: 0;
  z-index: 999;
  box-shadow: 5px 3px 40px rgb(100 100 100 / 0.1);
  -webkit-animation: 0.65s linear smooth-header;
  animation: 0.65s linear smooth-header;
}
@-webkit-keyframes smooth-header {
  0% {
    transform: translateY(-30px);
  }
  100% {
    transform: translateY(0);
  }
}
@keyframes smooth-header {
  0% {
    transform: translateY(-30px);
  }
  100% {
    transform: translateY(0);
  }
}
#home-page-banner-search-list:empty,
.banner-single-post-title:before,
.contact-section,
.culture-section .recent-highlight-quotes .box-with-border-inner,
.only-for-mobile,
.page-id-1050 .video-popup-button,
.page-id-1051 .video-popup-button,
.page-id-10520 .display-4,
.page-id-1230 .recruitment-process-quotes.left-qoutes,
.page-id-1255 .footer-list li:nth-child(7),
.page-id-1255 .patent-section,
.page-id-1259 .footer-list li:nth-child(7),
.page-id-1259 .quote-box2,
.page-id-14797 .about-us-quote-border-box .border-box,
.page-id-14797 .patent-section,
.page-id-639 .search-section,
.page-id-6938 .sector-item p,
.page-id-8044 .sector-item p,
.page-template-template-career-experienced-attorneys
  .recent-highlight-quotes
  .box-with-border-inner,
.page-template-template-people-list-filter .footer-top-design-one,
.page-template-template-upc .sector-item p,
.recent-highlight-quotes
  .box-inner-image
  img[src$="picostrap5-child-base/assets/images/team/team.jpg"],
.single-sector .sector-item P,
.single-upc_news .content-area .row > .col-sm-4,
.sjb-archive-page > h3,
.team-item.style-two .team-image br,
.tribe-events-back,
.video-background-controls,
button.cky-btn.cky-btn-customize,
header.style-1 .main-nav .mobile-menu-logo,
header.style-1 .main-nav > ul > li.language-list-inner-menu,
img[src=""] {
  display: none;
}
header.style-1 .main-nav > ul {
  list-style: none;
  margin: 0;
  padding: 0 25px 0 0;
}
header.style-1 .main-nav > ul > li {
  display: inline-block;
  position: relative;
  padding: 0 4px;
}
.bx-plus:before,
.home .expertise-wrapper .expertise-image::after,
.page-template-template-career-experienced-attorneys
  .testimonial-section:before,
.single-bulletin .experience-box:after,
.single-bulletin .experience-box:before,
header.style-1 .main-nav > ul > li:last-child::after,
ul.language-list > li:last-child::after {
  content: none;
}
header.style-1 .main-nav > ul > li a {
  color: var(--text-primary);
  font-weight: 600;
  display: block;
  position: relative;
}
.counter-section .counter-row > .counter-item:first-child .odometer,
.counter-single.style-red h3,
.search-block button:hover i,
header.style-1 .main-nav > ul > li a:hover,
header.style-1 .main-nav > ul > li ul.sub-menu > li a:hover {
  color: var(--primary-red);
}
header.style-1 .main-nav > ul > li a:hover:after,
ul.language-list > li a:hover:after {
  opacity: 1;
  width: 100%;
}
header.style-1 .main-nav > ul > li a::after {
  content: "";
  position: absolute;
  bottom: 25px;
  left: 0;
  width: 0%;
  height: 2px;
  border-radius: 30px;
  display: block;
  background: var(--text-primary);
  opacity: 0;
  transition: 0.5s ease-out;
}
.single-people header.style-1 .main-nav > ul > li.nav-item-1077 > a,
.single-people header.style-1 .main-nav > ul > li.nav-item-1078 > a,
.single-people header.style-1 .main-nav > ul > li.nav-item-420 > a,
.single-sector header.style-1 .main-nav > ul > li.nav-item-1076 > a,
.single-sector header.style-1 .main-nav > ul > li.nav-item-1079 > a,
.single-sector header.style-1 .main-nav > ul > li.nav-item-631 > a,
header.style-1 .main-nav > ul > li a.active {
  color: var(--primary-red);
  position: relative;
  display: inline-block;
}
header.style-1 .main-nav > ul > li i,
ul.language-list > li i {
  width: 30px;
  font-size: 14px;
  text-align: center;
  color: var(--text-primary);
  font-style: normal;
  position: absolute;
  right: -8px;
  top: 31px;
  z-index: 999;
  cursor: pointer;
  display: none;
  opacity: 0;
}
header.style-1 .main-nav > ul > li ul.sub-menu {
  position: absolute;
  left: 0;
  right: 0;
  margin: 0;
  min-width: 300px;
  border-radius: 0;
  padding: 5px 0;
  opacity: 0;
  visibility: hidden;
  background: var(--text-primary);
  text-align: left;
  transition: transform 0.35s ease-out;
  transform: translateY(20px);
  z-index: 999;
  top: 50px;
}
header.style-1 .main-nav > ul > li ul.sub-menu > li {
  padding: 0;
  display: block;
  position: relative;
}
header.style-1 .main-nav > ul > li ul.sub-menu > li i {
  position: absolute;
  top: 10px;
  right: 6px;
  display: block;
  color: var(--text-primary);
}
header.style-1 .main-nav > ul > li ul.sub-menu > li a {
  display: block;
  padding: 10px 17px;
  color: var(--white);
  font-weight: 400;
  font-size: 11px;
  line-height: 1;
  transition: 0.4s ease-out;
  position: relative;
}
header.style-1 .main-nav > ul > li ul.sub-menu > li a::after {
  content: "";
  position: absolute;
  bottom: 0;
  left: 0;
  width: 0;
  height: 1px;
  display: block;
  transition: 0.5s ease-in-out;
}
header.style-1 .main-nav > ul > li ul.sub-menu > li a.active {
  color: var(--primary-color1);
  color: var(--white);
  color: var(--primary-red);
  text-decoration: none;
  color: #fff;
}
header.style-1 .main-nav > ul > li ul.sub-menu > li .sub-menu {
  position: absolute;
  background: #0b0f14;
  top: 0;
  left: 150px;
}
.not-for-mobile,
.page-template-home ul.language-list,
.post-date.title-tag,
header.style-1 .main-nav > ul li.menu-item-has-children > i,
header.style-1 .main-nav > ul > li ul.sub-menu > li .sub-menu li i {
  display: block;
}
header.style-1 .main-nav > ul > li ul.sub-menu > li:last-child {
  border-bottom: none;
}
header.style-1 .main-nav > ul > li:hover > ul.sub-menu {
  visibility: visible;
  opacity: 1;
  transform: translateY(0);
}
header.style-1 .sidebar-button {
  display: flex;
  flex-direction: column;
  gap: 7px;
  cursor: pointer;
  align-items: flex-end;
  text-align: right;
  z-index: 9;
  position: relative;
}
header.style-1 .sidebar-button span {
  display: inline-block;
  width: 40px;
  height: 2px;
  border-radius: 3px;
  background-color: var(--text-primary);
  transition: 0.5s;
}
header.style-1 .sidebar-button span:nth-child(2) {
  width: 25px;
}
header.style-1 .sidebar-button span:last-child {
  width: 30px;
}
header.style-1 .sidebar-button:hover span:nth-child(2),
header.style-1 .sidebar-button:hover span:nth-child(3) {
  width: 40px;
}
.logo-area img {
  max-width: 160px;
}
.logo-section .logo-area {
  transform: translateY(0);
}
ul.language-list {
  list-style: none;
  margin: 0;
  padding: 0 30px 0 0;
}
ul.language-list > li {
  display: inline-block;
  position: relative;
  padding: 0 5px;
}
ul.language-list > li::after {
  content: "";
  position: absolute;
  top: 35px;
  right: 0;
  width: 1px;
  height: 12px;
  background-color: var(--text-primary);
}
ul.language-list > li a {
  font-size: 13px;
  color: var(--text-primary);
  font-weight: 600;
  display: block;
  position: relative;
}
ul.language-list > li a.active,
ul.language-list > li a:hover {
  color: var(--primary-one);
}
ul.language-list > li a::after {
  content: "";
  position: absolute;
  bottom: 25px;
  left: 0;
  width: 0%;
  height: 2px;
  display: block;
  opacity: 0;
  transition: 0.5s ease-out;
}
.home-banner-section .swiper-pagination-bullets.swiper-pagination-horizontal {
  bottom: 0;
  left: 66%;
  width: 100%;
  max-width: 450px;
  background-color: var(--primary-yellow);
  z-index: 3;
  position: absolute;
  height: 300px;
  padding: 70px 35px;
}
.home-banner-section
  .swiper-pagination-bullets.swiper-pagination-horizontal::after {
  content: "";
  position: absolute;
  top: 100%;
  right: 0;
  width: 100%;
  max-width: 450px;
  height: 90px;
  background-color: var(--primary-yellow);
  display: block;
  z-index: 9;
}
.banner-section
  .swiper-pagination-bullets.swiper-pagination-horizontal
  .swiper-pagination-bullet,
.banner-section
  .swiper-pagination-bullets.swiper-pagination-horizontal
  .swiper-pagination-bullet-active,
.home-banner-section
  .swiper-pagination-bullets.swiper-pagination-horizontal
  .swiper-pagination-bullet,
.home-banner-section
  .swiper-pagination-bullets.swiper-pagination-horizontal
  .swiper-pagination-bullet-active {
  width: 16px;
  height: 16px;
  border-radius: 0;
  background: #fff;
  opacity: 1;
  border: 1px solid #fff;
}
.blog-item,
.right-box {
  border: 1px solid var(--border);
}
.banner-section
  .swiper-pagination-bullets.swiper-pagination-horizontal
  .swiper-pagination-bullet-active,
.home-banner-section
  .swiper-pagination-bullets.swiper-pagination-horizontal
  .swiper-pagination-bullet-active,
.page-id-14797 .section-title-two h4.partner::before,
.search-block button,
.single-upc_news .section-title-two h4.partner::before {
  background-color: #fff0;
}
.search-area {
  position: relative;
  margin-top: -78px;
  z-index: 9;
}
.search-area .search-block {
  z-index: 4;
  width: 100%;
  max-width: 780px;
  margin-left: auto;
  margin-right: auto;
  transform: translateY(-70px);
}
.banner-section {
  position: relative;
  z-index: 1;
  min-height: 100vh;
}
.page-template-template-expertise .banner-section,
.page-template-template-upc .banner-section,
.single-service .banner-section {
  min-height: 100vh;
}
.banner-section .swiper-slide img {
  max-height: auto;
}
.banner-section .swiper-pagination-bullets.swiper-pagination-horizontal {
  bottom: 0;
  left: 59%;
  width: 100%;
  max-width: 450px;
  background-color: var(--primary-red);
  z-index: 3;
  position: absolute;
  height: 200px;
  padding: 70px 35px;
}
.banner-section .swiper-pagination-bullets.swiper-pagination-horizontal::after {
  content: "";
  position: absolute;
  top: 100%;
  right: 0;
  width: 100%;
  max-width: 450px;
  height: 150px;
  background-color: var(--primary-red);
  display: block;
  z-index: 9;
}
.banner-slider-content {
  position: relative;
  z-index: 2;
  min-height: 780px;
  display: flex;
  justify-content: center;
  align-items: center;
}
.page-template-template-expertise .sector-item,
.sector-item {
  min-height: 230px;
}
.banner-slider-content h1 {
  color: var(--text-primary);
  font-size: 100px;
  font-weight: 300;
}
.banner-content h1,
.banner-content h2 {
  color: var(--white);
  font-size: 100px;
  font-weight: 300;
}
.right-box {
  height: 135px;
  width: 100%;
  position: relative;
  z-index: 1;
}
.right-box::after {
  content: "";
  position: absolute;
  left: 60px;
  bottom: 60px;
  width: 100%;
  background-color: var(--primary-yellow);
  height: 270px;
}
.right-box-two {
  height: 470px;
  background-color: var(--primary-yellow);
  width: 100%;
  max-width: 470px;
  position: absolute;
  z-index: 1;
  transform: translate(60px, -200px);
}
.sector-item,
.sector-item-two {
  background-color: var(--primary-green);
  transition: 0.5s;
}
.right-box-two.style-blue {
  height: 270px;
  background-color: var(--primary-blue);
}
.banner-video-section .scroll-down {
  position: absolute;
  left: 50%;
  transform: translateX(-50%);
  bottom: 80px;
  z-index: 2;
  -webkit-animation: 3s linear infinite alternate jump;
  animation: 3s linear infinite alternate jump;
}
.banner-video-section .scroll-down:hover {
  -webkit-animation-play-state: paused;
  animation-play-state: paused;
}
@-webkit-keyframes jump {
  0%,
  100%,
  50% {
    transform: translate(-50%, 0);
  }
  25% {
    transform: translate(-50%, 10px);
  }
  75% {
    transform: translate(-50%, -10px);
  }
}
@keyframes jump {
  0%,
  100%,
  50% {
    transform: translate(-50%, 0);
  }
  25% {
    transform: translate(-50%, 10px);
  }
  75% {
    transform: translate(-50%, -10px);
  }
}
.content-section .content {
  width: 100%;
  max-width: 500px;
}
.content-section .content h6 {
  margin-bottom: 0;
  font-size: 16px;
  font-weight: 600;
}
.team-item.style-two .team-image {
  overflow: hidden;
  height: 155px;
}
.team-item.style-two .team-image img {
  transition: 0.5s;
  transform: scale(1);
  width: 100%;
  -o-object-fit: cover;
  object-fit: cover;
}
.team-item .team-content,
.team-item.style-two .team-content {
  padding-top: 8px;
}
.business-card-section.new-style .business-card-box p,
.home-page-banner-search-list .search-result-list a:hover,
.team-item.style-two .team-content h6,
b,
strong {
  font-weight: 700;
}
.address-list li a,
.key-contact-list li a,
.team-item .team-content h6 a,
.team-item.style-two .team-content h6 a {
  color: inherit;
}
.team-item .team-tag {
  position: absolute;
  display: inline-block;
  left: 0;
  top: 0;
  min-width: 30px;
  padding: 5px;
  background-color: var(--text-primary);
  color: var(--white);
  font-size: 12px;
  font-weight: 700;
  text-align: center;
  z-index: 1;
}
.blog-item .image,
.career-card .image-wrap,
.team-item .team-image {
  overflow: hidden;
}
.team-item .team-image img {
  transition: 0.5s;
  transform: scale(1);
  width: 100%;
  -o-object-fit: cover;
  object-fit: cover;
  height: 155px;
}
.team-item .team-content h6 {
  color: var(--text-primary);
  line-height: 1.2;
  margin-bottom: 3px;
}
.team-item .team-content span {
  display: block;
  font-size: 9px;
  color: var(--text-primary);
  text-transform: uppercase;
}
.team-item.team-sidebar {
  width: 100%;
  max-width: 260px;
}
.insight-card .sector-item {
  background-color: #f89d48;
}
.sector-item {
  position: relative;
  padding: 15px 6px 15px 15px;
  text-align: left;
  height: 100%;
}
.sector-item .arrow-btn {
  position: absolute;
  right: 20px;
  z-index: 2;
  transition: 0.4s;
}
.job-post-name a img:hover,
.sector-item .arrow-btn:hover,
.sector-item-two .arrow-btn:hover {
  transform: rotate(-45deg);
}
.sector-item-two:hover,
.sector-item:hover {
  background-color: #3aaa35;
}
.sector-item .title {
  font-size: 13px;
  font-weight: 700;
  color: var(--white);
  margin-bottom: 0;
  padding-right: 0;
  min-height: 24px;
}
.sector-item p {
  font-size: 13px;
  font-weight: 300;
  color: var(--white);
  margin-bottom: 0;
}
.sector-item-two.style-blue:hover,
.sector-item-two.style-yellow:hover,
.sector-item.style-blue:hover,
.sector-item.style-yellow:hover {
  background-color: #000;
}
.sector-item-two {
  position: relative;
  padding: 15px;
  text-align: left;
}
.sector-item-two .arrow-btn {
  position: absolute;
  left: 20px;
  bottom: 20px;
  z-index: 2;
  transition: 0.4s;
}
.sector-item-two .title {
  font-size: 13px;
  font-weight: 700;
  color: var(--white);
  margin-bottom: 0;
  padding-right: 0;
}
.sector-item-two p {
  font-size: 14px;
  font-weight: 300;
  color: var(--white);
  margin-bottom: 20px;
}
.sector-item-two span.date {
  font-size: 13px;
  text-transform: uppercase;
  font-weight: 600;
  color: var(--white);
  display: inline-block;
  position: relative;
  padding-top: 20px;
  margin-bottom: 60px;
}
.sector-item-two span.date::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 90px;
  height: 1px;
  background-color: var(--white);
  display: block;
}
.service-section-1 {
  overflow: hidden;
  position: relative;
  z-index: 1;
}
.service-section-1 .container-one::before {
  content: "";
  position: absolute;
  top: -85px;
  right: -100px;
  width: 100%;
  max-width: 730px;
  height: 340px;
  background-color: var(--primary-blue);
  display: block;
  z-index: -1;
}
.service-item {
  position: relative;
  width: 100%;
}
.insight-item .image,
.service-item .image {
  margin-bottom: 15px;
  overflow: hidden;
}
.service-item .image img {
  transition: 0.62s;
  height: 230px;
  -o-object-fit: cover;
  object-fit: cover;
  width: 100%;
}
.service-item .content {
  text-align: left;
  min-height: 100px;
}
.service-item .content h6 {
  line-height: 1.1;
  margin-bottom: 0;
  min-height: 24px;
}
.insight-item .content h6 a,
.service-item .content h6 a {
  font-size: 13px;
  color: var(--primary-red);
  text-transform: uppercase;
  font-weight: 700;
}
.service-item .content p {
  font-size: 13px;
  font-weight: 300;
  color: var(--text-primary);
  margin-bottom: 20px;
}
.service-item .content > a {
  display: inline-block;
  font-size: 13px;
  text-transform: uppercase;
  color: var(--text-primary);
  font-weight: 700;
  position: relative;
  padding-top: 12px;
  transition: 0.45s;
}
.insight-item .content > a:hover,
.service-item .content > a:hover {
  letter-spacing: 1px;
  color: var(--primary-red);
}
.insight-item .content > a::before,
.service-item .content > a::before {
  content: "";
  position: absolute;
  left: 0;
  top: 0;
  width: 50px;
  height: 1px;
  background-color: var(--text-primary);
}
.service-item .content > a i {
  font-size: 12px;
  font-weight: 600;
  display: inline-block;
  margin-right: 5px;
}
.service-details-wrap {
  background-color: var(--primary-blue);
  padding: 60px 100px;
}
.service-details-wrap h6 {
  color: var(--white);
  margin-bottom: 0;
}
.service-details-section {
  padding-right: 80px;
}
.bg-light-box {
  position: relative;
  z-index: 1;
}
.bg-light-box::before {
  content: "";
  position: absolute;
  bottom: -100px;
  right: 0;
  width: 75%;
  height: 370px;
  background-color: var(--primary-yellow-light2);
  display: block;
  z-index: -1;
}
.insight-item {
  position: relative;
  width: 100%;
  max-width: 320px;
}
.counter-section .counter-row > .counter-item:nth-child(2) .odometer,
.insight-item.style-blue .content h6 a,
.insight-item.style-blue .content > a:hover {
  color: var(--primary-blue);
}
.about-people-card ul li:hover a,
.counter-single.style-green h3,
.insight-item.style-green .content h6 a,
.insight-item.style-green .content > a:hover,
.pagination ul li.active,
.pagination ul li.active a {
  color: var(--primary-green-dark);
}
.counter-section .counter-row > .counter-item:nth-child(4) .odometer,
.insight-item.style-yellow .content h6 a,
.insight-item.style-yellow .content > a:hover {
  color: var(--primary-yellow);
}
.insight-item .image img {
  transition: 0.62s;
  height: 210px;
  -o-object-fit: cover;
  object-fit: cover;
}
.insight-item .content {
  text-align: left;
  padding-right: 0;
}
.insight-item .content h6 {
  margin-bottom: 5px;
}
.insight-item .content p {
  font-size: 13px;
  font-weight: 300;
  color: var(--text-primary);
  margin-bottom: 15px;
}
.insight-item .content > a {
  display: inline-block;
  font-size: 13px;
  text-transform: uppercase;
  color: var(--text-primary);
  font-weight: 700;
  position: relative;
  padding-top: 10px;
  transition: 0.45s;
}
.blog-item:hover .image img,
.guide-single:hover img {
  transform: scale(1.2);
}
.blog-item .content {
  padding: 15px;
  background-color: var(--white);
}
.blog-item .content h5 a {
  color: var(--primary-red);
  font-size: 17px;
  font-weight: 300;
}
.blog-item .content P,
.career-card H5,
.career-card P {
  font-size: 14px;
}
.blog-item header span {
  display: block;
  font-weight: 400;
}
.blog-item header span:first-child {
  font-size: 13px;
  color: var(--primary-red);
}
.blog-item header span:last-child {
  font-size: 12px;
  color: var(--text-primary);
}
.blog-item ul.blog-icon-list {
  list-style: none;
  margin: 0 0 5px;
  padding: 0 5px 0 0;
  display: flex;
  flex-direction: row;
  justify-content: flex-end;
  gap: 5px;
  position: relative;
  z-index: 1;
}
.blog-item ul.blog-icon-list::after {
  content: "";
  position: absolute;
  top: 15px;
  right: 0;
  display: block;
  width: 100%;
  height: 1px;
  background-color: var(--border);
  z-index: -1;
}
.blog-item ul.blog-icon-list li {
  width: 30px;
  height: 30px;
  line-height: 25px;
  border: 1px solid var(--border);
  border-radius: 50%;
  text-align: center;
  background-color: var(--white);
  transition: 0.4s;
}
.blog-item ul.blog-icon-list li:hover {
  border: 1px solid var(--primary-red);
  background-color: var(--primary-red);
}
.blog-item ul.blog-icon-list li a i {
  font-size: 21px;
  transition: 0.4s;
  color: var(--text-primary);
}
.blog-item .image img {
  transition: 0.62s;
  height: 270px;
  -o-object-fit: cover;
  object-fit: cover;
}
.software-section {
  padding-left: 7%;
  padding-right: 7%;
}
.software-content {
  width: 100%;
  max-width: 520px;
  margin-left: 0;
  margin-right: auto;
}
footer {
  background-color: var(--text-primary);
  padding: 20px 10px;
}
.choose-us-card ul,
.contact-list,
.culture-section .value-card ul,
.focus-list-area ul,
.footer-list {
  list-style: none;
  margin: 0;
  padding: 0;
}
.footer-list li,
.job-post-name a img {
  transition: 0.4s;
}
.footer-list li:hover a {
  color: var(--primary-red);
  padding-left: 3px;
  opacity: 1;
}
.footer-list a {
  font-size: 11px;
  font-weight: 300;
  color: #ddd;
  transition: 0.4s;
}
.about-people-card .social-link:hover i,
.footer-list .footer-social:hover i {
  background-color: var(--primary-blue);
  color: var(--white);
}
.footer-list .footer-social i {
  font-size: 18px;
  width: 25px;
  height: 25px;
  line-height: 25px;
  border-radius: 50%;
  background-color: var(--white);
  color: var(--text-primary);
  text-align: center;
}
.footer-address h6 {
  font-size: 11px;
  font-weight: 700;
  color: var(--white);
  margin-bottom: 3px;
}
.footer-address p {
  margin-bottom: 0;
  font-size: 11px;
  font-weight: 300;
  color: #ddd;
  transition: 0.4s;
  min-height: 95px;
}
.contact-list li a {
  font-size: 11px;
  font-weight: 300;
  color: var(--white);
  transition: 0.4s;
  line-height: 1.2em;
}
.footer-col-wrap {
  display: grid;
  grid-template-columns: repeat(8, 1fr);
  grid-gap: 18px;
}
.footer-bottom {
  margin-top: 45px;
  font-size: 12px;
  font-weight: 300;
  color: #ddd;
}
.footer-bottom p {
  font-size: 10px;
}
.footer-top-design-one.style-two .box {
  width: 50%;
  margin-left: auto;
  margin-right: 5%;
  min-height: 280px;
  background-color: var(--primary-green-dark-light);
  position: relative;
  display: block;
  z-index: 1;
}
.footer-top-design-one .box {
  width: 47%;
  margin-left: auto;
  margin-right: 10%;
  min-height: 280px;
  background-color: var(--primary-red-light);
  position: relative;
  display: block;
  z-index: -1;
}
.footer-top-design-one .box::after {
  content: "";
  position: absolute;
  bottom: -1px;
  left: -55%;
  height: 25%;
  width: 68%;
  background-color: var(--primary-red);
}
.footer-top-design-three .footer-box::before,
.footer-top-design-two .footer-box::before {
  max-width: 980px;
  background-color: #fff0;
  z-index: -1;
  bottom: 0;
  display: block;
  border: 1px solid var(--border);
  content: "";
}
.footer-top-design-one .box::before {
  content: "";
  position: absolute;
  bottom: 0;
  left: -25%;
  height: 50%;
  width: 80%;
  border: 1px solid var(--border);
  z-index: -1;
}
.footer-top-design-two {
  overflow: hidden;
  padding-top: 100px;
}
.footer-top-design-two .footer-box {
  width: 100%;
  max-width: 640px;
  height: 180px;
  background-color: var(--primary-red);
  display: block;
  margin-left: 100px;
  margin-right: auto;
  position: relative;
  z-index: 1;
}
.footer-top-design-two .footer-box::before {
  position: absolute;
  left: 80%;
  width: 100%;
  height: 70px;
}
.footer-top-design-three {
  padding-top: 100px;
  margin-top: 100px;
}
.footer-top-design-three .footer-box {
  width: 100%;
  max-width: 640px;
  height: 100px;
  background-color: var(--primary-red);
  display: block;
  margin-left: 100px;
  margin-right: auto;
  position: relative;
}
.footer-top-design-three .footer-box::before {
  position: absolute;
  left: 80%;
  width: 100%;
  height: 280px;
}
.footer-top-design-five,
.footer-top-design-four {
  position: relative;
  padding-left: 10%;
  padding-right: 10%;
}
.footer-top-design-four .main-box {
  width: 100%;
  max-width: 670px;
  height: 280px;
  background-color: var(--primary-red-light);
}
.footer-top-design-four .main-box::before {
  content: "";
  position: absolute;
  right: 10%;
  bottom: 0;
  border: 1px solid var(--border);
  width: 100%;
  max-width: 60%;
  height: 190px;
}
.footer-top-design-five .small-box,
.footer-top-design-four .small-box {
  width: 37%;
  height: 90px;
  background-color: var(--primary-red);
  position: absolute;
  bottom: 0;
  left: 20%;
  z-index: 2;
}
.footer-top-design-five .main-box {
  width: 100%;
  max-width: 670px;
  height: 190px;
}
.footer-top-design-five .main-box::before {
  content: "";
  position: absolute;
  right: 10%;
  bottom: 0;
  border: 1px solid var(--border);
  width: 100%;
  max-width: 50%;
  height: 190px;
}
.footer-top-design-seven .footer-box,
.footer-top-design-six .footer-box {
  width: 28%;
  height: 180px;
  display: block;
  margin-left: 27%;
  margin-right: auto;
  position: relative;
  z-index: 1;
  background-color: #fff0;
  border-left: 1px solid var(--border);
  border-right: 1px solid var(--border);
}
.footer-top-design-seven .footer-box::before,
.footer-top-design-six .footer-box::before {
  content: "";
  position: absolute;
  left: 80%;
  bottom: 0;
  width: 100%;
  max-width: 980px;
  height: 70px;
  background-color: var(--primary-red);
  display: block;
  z-index: -1;
}
.footer-top-design-eight {
  padding-left: 10%;
  margin-top: 30px;
}
.footer-top-design-eight .box {
  width: 38%;
  height: 370px;
  background-color: var(--primary-red-light);
  position: relative;
}
.footer-top-design-eight .box::after {
  content: "";
  position: absolute;
  bottom: 0;
  left: 25%;
  height: 50%;
  width: 100%;
  background-color: var(--primary-red);
}
.footer-top-design-eight .box::before {
  content: "";
  position: absolute;
  bottom: 0;
  left: 85%;
  height: 125%;
  width: 100%;
  border: 1px solid var(--border);
  z-index: -1;
}
.footer-top-design-ten,
.page-id-6938 .sector-section,
.page-id-8044 .sector-section,
.page-id-8236 .sector-section {
  margin-top: -80px;
}
.footer-top-design-ten .box {
  width: 47%;
  margin-left: 33%;
  min-height: 370px;
  border: 1px solid var(--border);
  position: relative;
  display: block;
  z-index: -1;
}
.footer-top-design-ten .box::after {
  content: "";
  position: absolute;
  bottom: -1px;
  left: -15%;
  height: 25%;
  width: 68%;
  background-color: var(--primary-red);
}
.footer-top-design-ten .box::before {
  content: "";
  position: absolute;
  bottom: 0;
  left: 25%;
  height: 50%;
  width: 105%;
  background-color: var(--primary-red-light);
  z-index: -1;
}
.expertise-section-1,
.mt-200 {
  margin-top: 200px;
}
.expertise-section-1 .right-box-blue {
  position: relative;
  height: 200px;
  background-color: var(--primary-blue);
  width: 100%;
  max-width: 340px;
  left: 0;
  margin-left: 40px;
  margin-top: -90px;
}
.expertise-section-1 .right-box-blue::before {
  content: "";
  position: absolute;
  top: 50%;
  right: -100px;
  transform: translateY(-50%);
  width: 100%;
  max-width: 270px;
  height: 340px;
  background-color: var(--primary-blue-light);
  display: block;
  z-index: -1;
}
.search-section,
.search-section2 {
  background-color: var(--primary-blue);
  padding: 90px 10px;
}
.big-card.style-blue,
.business-parnter-single.style-blue::before,
.search-section.style-blue,
.search-section2.style-blue {
  background-color: var(--primary-blue-light);
}
.search-section.with-box::before,
.search-section2.with-box::before {
  content: "";
  position: absolute;
  top: -180px;
  left: 27%;
  height: 180px;
  width: 28%;
  border: 1px solid var(--border);
  border-bottom: unset;
}
.box-design-11,
.box-design-14,
.box-design-15,
.box-design-16,
.box-design-one,
.box-design-two,
.box-with-border,
.guide-single {
  border: 1px solid var(--border);
}
.search-block {
  background-color: var(--white);
  padding: 20px 30px;
  display: flex;
  justify-content: flex-start;
  align-items: center;
}
.search-block button i {
  font-size: 25px;
  color: var(--text-primary);
  margin-right: 10px;
  transition: 0.4s;
}
.contact-form .search-block textarea,
.search-block .contact-form textarea,
.search-block input {
  outline: 0;
  border: none;
  background-color: #fff0;
  width: 100%;
}
.contact-form .search-block textarea::-moz-placeholder,
.search-block .contact-form textarea::-moz-placeholder,
.search-block input::-moz-placeholder {
  font-size: 10px;
  text-transform: uppercase;
  font-weight: 800;
  color: var(--text-primary);
}
.contact-form .search-block textarea:-ms-input-placeholder,
.search-block .contact-form textarea:-ms-input-placeholder,
.search-block input:-ms-input-placeholder {
  font-size: 10px;
  text-transform: uppercase;
  font-weight: 800;
  color: var(--text-primary);
}
.contact-form .search-block textarea::placeholder,
.search-block .contact-form textarea::placeholder,
.search-block input::placeholder {
  font-size: 10px;
  text-transform: uppercase;
  font-weight: 800;
  color: var(--text-primary);
}
.box-with-border {
  width: 100%;
  max-width: 360px;
  height: auto;
  min-height: 270px;
  background-color: #fff0;
  position: relative;
  margin-top: 135px;
}
.box-with-border::after {
  content: "";
  position: absolute;
  left: -150px;
  top: -90px;
  width: 100%;
  max-width: 340px;
  height: 270px;
  background-color: var(--primary-green-light);
}
.focus-list-area {
  background-color: var(--primary-yellow-light2);
  padding: 40px 45px;
  width: 100%;
  max-width: 750px;
  margin-left: 0;
  margin-right: auto;
  margin-top: 100px;
}
.focus-list-area h6 {
  font-weight: 700;
  margin-bottom: 5px;
}
.focus-list-area ul li {
  position: relative;
  margin-bottom: 3px;
}
#breadcrumbs,
.address-list li:last-child,
.eligibility-card .date p,
.experience-box ul,
.faq-wrap .faq-item:last-child,
.focus-list-area ul li:last-child,
.key-contact-list li:last-child,
.offer-card .list-block:last-child,
.search-option-filters ul li label,
.single-people .about-people-card,
.single-sector .team-section .section-title-one h2,
footer ul li,
header.style-1 .main-nav > ul > li ul.sub-menu > li {
  margin-bottom: 0;
}
.focus-list-area ul li::before {
  content: "\f285";
  font-family: Bootstrap-icons;
  position: absolute;
  left: -15px;
  top: 5px;
  color: var(--text-primary);
  font-weight: 700;
  font-size: 12px;
  opacity: 0;
}
.focus-list-area ul a {
  color: var(--text-primary);
  font-size: 15px;
}
.box-design-one {
  width: 100%;
  max-width: 860px;
  background-color: var(--white);
  height: 190px;
  margin-left: auto;
  margin-right: auto;
  position: relative;
  z-index: 2;
  display: block;
}
.box-design-one::before {
  content: "";
  position: absolute;
  left: -25%;
  top: -320px;
  width: 100%;
  max-width: 480px;
  height: 380px;
  background-color: var(--primary-green-dark);
  display: block;
}
.box-design-one::after {
  content: "";
  position: absolute;
  right: -30%;
  bottom: -390px;
  width: 100%;
  max-width: 580px;
  height: 480px;
  background-color: var(--primary-green-light);
  display: block;
}
.box-design-two {
  width: 380px;
  height: 280px;
  background-color: #fff0;
  position: relative;
  margin-top: 190px;
}
.box-design-two::after {
  content: "";
  position: absolute;
  width: 400px;
  height: 380px;
  background-color: var(--primary-yellow);
  bottom: 90px;
  right: 90px;
}
.box-design-three {
  width: 100%;
  max-width: 660px;
  height: 460px;
  background-color: var(--primary-blue-light);
  position: absolute;
  top: -250px;
  right: 7%;
  z-index: -1;
}
.box-design-five,
.box-design-four {
  width: 100%;
  max-width: 660px;
  position: absolute;
  top: 0;
  left: 7%;
  z-index: -1;
}
.box-design-four {
  height: 270px;
  background-color: var(--primary-red-light);
}
.box-design-five {
  background-color: var(--primary-blue-light);
  height: 75%;
}
.box-design-12,
.box-design-eight {
  width: 100%;
  position: absolute;
  left: 0;
}
.box-design-six {
  width: 100%;
  max-width: 540px;
  background-color: var(--primary-yellow-light2);
  position: absolute;
  top: 45px;
  right: 7%;
  z-index: -1;
  height: 75%;
}
.box-design-seven {
  width: 100%;
  max-width: 960px;
  height: 370px;
  background-color: var(--primary-blue-light);
  position: absolute;
  top: 0;
  right: 7%;
  z-index: -1;
}
.box-design-eight {
  max-width: 90%;
  height: 60%;
  background-color: var(--primary-green-light);
  top: 50%;
  transform: translateY(-50%);
  z-index: -1;
}
.box-design-nine {
  max-width: 60%;
  width: 100%;
  height: 100%;
  background-color: var(--primary-blue-light);
  position: absolute;
  top: 0;
  bottom: 0;
  right: 0;
  z-index: -1;
  display: block;
}
.box-design-ten {
  width: 100%;
  max-width: 50%;
  height: 270px;
  background-color: var(--primary-yellow-light2);
  position: absolute;
  bottom: 0;
  right: 10%;
  z-index: -1;
  display: block;
}
.box-design-11 {
  height: 120px;
  width: 100%;
  max-width: 470px;
  background-color: var(--white);
  position: relative;
  z-index: 1;
  margin-left: 70px;
  margin-bottom: 30px;
}
.box-design-11::before {
  content: "";
  position: absolute;
  left: 90px;
  bottom: 90px;
  width: 100%;
  max-width: 470px;
  height: 200px;
  background-color: var(--primary-green-dark);
  display: block;
}
.box-design-12 {
  max-width: 60%;
  height: 380px;
  background-color: var(--primary-yellow-light2);
  top: -180px;
  z-index: -1;
}
.box-design-13,
.box-design-14 {
  top: 0;
  right: 5%;
  width: 100%;
  height: 270px;
  position: absolute;
}
.box-design-13 {
  max-width: 50%;
  background-color: var(--primary-green-dark-light);
  z-index: -1;
}
.box-design-14 {
  max-width: 370px;
}
.box-design-14::after {
  content: "";
  position: absolute;
  bottom: 90px;
  right: 90px;
  background-color: var(--primary-blue-light);
  height: 270px;
  width: 150%;
  z-index: -1;
}
.box-design-15 {
  height: 180px;
  width: 100%;
  max-width: 470px;
  background-color: var(--white);
  position: relative;
  z-index: 1;
  margin-right: 70px;
  margin-left: auto;
  margin-bottom: 90px;
}
.box-design-15::before {
  content: "";
  position: absolute;
  right: 90px;
  bottom: 90px;
  width: 100%;
  max-width: 470px;
  height: 370px;
  background-color: var(--primary-yellow);
  display: block;
}
.box-design-16 {
  width: 100%;
  max-width: 470px;
  height: 380px;
  margin-left: 180px;
  transform: translateY(100px);
}
.career-left-image img,
.company-vdo {
  max-width: 100%;
}
.expertise-section {
  padding-right: 8%;
}
.expertise-wrapper {
  padding-bottom: 90px;
  display: flex;
  gap: 10%;
  align-items: self-start;
  flex-direction: row;
  flex-wrap: nowrap;
}
.expertise-wrapper .expertise-image {
  max-width: 290px;
  position: relative;
}
.home .expertise-wrapper .expertise-image {
  max-width: 200px;
  position: relative;
  bottom: -137px;
  left: 175px;
  z-index: 9;
}
.home .career-green-box {
  margin-top: 115px;
}
.home .career-green-box::before {
  height: 180%;
}
.home .career-green-box::after {
  bottom: 40px;
}
.home .expertise-wrapper {
  gap: 3%;
}
.expertise-wrapper .expertise-image::after {
  content: "";
  position: absolute;
  width: 100%;
  min-width: 380px;
  height: 100%;
  border: 1px solid var(--border);
  bottom: -90px;
  left: 70%;
  z-index: -1;
}
.expertise-wrapper .quote-box2 {
  background-color: var(--primary-red);
  padding: 35px;
  min-height: auto;
  width: 100%;
  max-width: 480px;
  min-width: auto;
  margin-top: -60px;
  position: relative;
}
.expertise-wrapper .quote-box2::after {
  content: "";
  position: absolute;
  left: 90px;
  bottom: -90px;
  width: 100%;
  min-height: 580px;
  background-color: var(--primary-red-light);
  z-index: -1;
}
.about-people-card .quote-box p,
.about-section .quote-box p,
.career-right-quote p,
.expertise-wrapper .quote-box2 p {
  color: var(--white);
  font-size: 20px;
  font-weight: 300;
  line-height: 1.3;
  margin-bottom: 10px;
}
.about-people-card .quote-box span,
.about-section .quote-box span,
.career-right-quote span,
.expertise-wrapper .quote-box2 span {
  font-size: 12px;
  font-weight: 700;
  color: var(--white);
}
.guide-single {
  overflow: hidden;
}
.guide-single img {
  transition: 0.65s;
}
.company-vdo {
  background-position: center;
  width: 100%;
  min-height: 100vh;
  margin-left: auto;
  margin-right: auto;
  position: relative;
  z-index: 1;
  display: flex;
  justify-content: center;
  align-items: center;
  flex-direction: column;
  text-align: center;
}
.company-vdo .play-icon {
  width: 65px;
  height: 45px;
  display: flex;
  justify-content: center;
  align-items: center;
}
.company-vdo .play-icon img {
  z-index: 99;
  filter: invert(35%) sepia(87%) saturate(4112%) hue-rotate(333deg)
    brightness(74%) contrast(162%);
  transform: scale(2);
}
.company-vdo h3 {
  color: var(--white);
  font-size: 70px;
  font-weight: 300;
  margin-bottom: 40px;
}
.contact-form .form-inner p {
  margin-bottom: 0;
  font-size: 10px;
  line-height: 1.3;
}
.contact-form label {
  font-size: 11px;
  color: var(--text-primary);
  font-weight: 700;
  margin-bottom: 5px;
  padding-left: 15px;
}
.contact-form input,
.contact-form textarea {
  width: 100%;
  background-color: var(--white);
  outline: 0;
  border: 1px solid #fff0;
  padding: 12px 15px;
  line-height: 1;
  transition: 0.4s;
  font-size: 12px;
}
.contact-form input:focus,
.contact-form textarea:focus {
  border: 1px solid var(--primary-green-dark);
}
.contact-form input::-moz-placeholder,
.contact-form textarea::-moz-placeholder {
  font-size: 10px;
  color: var(--text-secondary);
  line-height: 1;
}
.contact-form input:-ms-input-placeholder,
.contact-form textarea:-ms-input-placeholder {
  font-size: 10px;
  color: var(--text-secondary);
  line-height: 1;
}
.contact-form input::placeholder,
.contact-form textarea::placeholder {
  font-size: 10px;
  color: var(--text-secondary);
  line-height: 1;
}
.contact-form textarea {
  min-height: 144px;
}
.contact-form .submit-btn {
  font-size: 11px;
  font-weight: 600;
  padding: 8px 15px;
  background: var(--primary-green-dark);
  width: auto;
}
.location-card-section,
.page-template-template-career-experienced-attorneys .facilities-section,
.page-template-template-locations .box-design-16 {
  margin-top: -100px;
}
.faq-area {
  background-color: var(--primary-blue);
  padding: 40px 70px;
  width: 100%;
  max-width: 650px;
  position: relative;
  margin-top: 100px;
}
.faq-area::before {
  content: "";
  position: absolute;
  right: -90px;
  top: -90px;
  display: block;
  width: 100%;
  max-width: 560px;
  background-color: var(--primary-blue-light);
  height: 100%;
  z-index: -1;
}
.faq-wrap .faq-item {
  border: none;
}
.faq-wrap .accordion-button {
  font-weight: 600;
  font-size: 16px;
  border-radius: 0;
  color: var(--white);
  cursor: pointer;
  transition: 0.4s ease-in-out;
  padding: 8px 60px 8px 0;
  margin-bottom: 0;
  line-height: 1.4;
  background-color: var(--primary-blue);
}
.faq-wrap .accordion-button:hover {
  padding-left: 22px;
}
.faq-wrap .accordion-button:focus {
  color: var(--primary-blue);
  z-index: unset;
  border-color: unset;
  outline: 0;
}
.faq-wrap .accordion-button::after {
  flex-shrink: 0;
  margin-left: auto;
  background: var(--primary-blue);
  font-family: bootstrap-icons !important;
  position: absolute;
  left: -18px;
  top: 11px;
  content: "\f4fe";
  transition: unset;
  font-size: 20px;
  width: 15px;
  height: 15px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--white);
}
.faq-wrap .accordion-button:not(.collapsed)::after {
  transform: unset;
  font-family: bootstrap-icons !important;
  content: "\f2ea";
  color: var(--white);
  background-image: none;
}
.faq-wrap .accordion-button:not(.collapsed) {
  color: var(--white) !important;
  border-radius: 0 !important;
  box-shadow: none;
  color: var(--text-primary);
}
.faq-wrap .faq-body {
  font-weight: 400;
  font-size: 14px;
  color: var(--white);
  border-top: none;
  padding: 0 0 15px;
  line-height: 1.3;
  text-align: left;
}
.experience-block .experice-text {
  border-top: 1px solid var(--border);
  border-bottom: 1px solid var(--border);
  border-bottom: none;
  padding-top: 10px;
  padding-bottom: 15px;
}
.testimonial-block {
  border-bottom: 1px solid var(--border);
  border-bottom: none;
  margin-bottom: 20px;
}
.testimonial-block .testi-single p {
  font-style: italic;
  margin-bottom: 0;
  font-size: 16px;
}
.testimonial-block .testi-single span {
  display: inline-block;
  font-size: 10px;
  font-weight: 700;
  color: var(--text-primary);
  line-height: 1;
}
.publication-block {
  border-bottom: 1px solid var(--border);
  border-bottom: none;
}
.publication-text h6 {
  font-size: 15px;
  font-weight: 700;
  margin-bottom: 0;
}
.people-info-section {
  padding-left: 8%;
}
.testimonial-card {
  background-color: var(--primary-red);
  padding: 35px 40px;
  width: 100%;
  max-width: 550px;
  margin-left: 100px;
  margin-top: -350px;
}
.testimonial-card p {
  color: var(--white);
  font-style: italic;
  font-size: 16px;
  font-weight: 300;
}
.testimonial-image2 {
  max-width: 320px;
  width: 100%;
  margin-left: auto;
  margin-right: 40px;
}
.tesitmonial-section {
  margin-top: -20px;
  position: relative;
}
.about-people-card {
  border: 1px solid var(--border);
  padding: 40px 100px;
  margin-top: -400px;
  z-index: 2;
  position: relative;
  width: 100%;
  max-width: 600px;
  margin-bottom: 380px;
}
.about-people-card .designation h1,
.about-people-card .designation h2 {
  font-size: 40px;
  margin-bottom: 0;
  color: var(--text-primary);
  font-weight: 300;
  line-height: 1.1;
}
.about-people-card .box {
  width: 100%;
  max-width: 380px;
  background-color: var(--primary-green);
  margin-top: 30px;
  margin-bottom: 30px;
  margin-left: -190px;
}
.about-people-card h6 {
  font-size: 14px;
  font-weight: 700;
  margin-bottom: 0;
}
.about-people-card ul {
  list-style: none;
  margin: 0 0 25px;
  padding: 0;
}
.about-people-card ul li {
  color: var(--text-secondary);
  font-size: 14px;
  margin-bottom: 4px;
}
.about-people-card ul li a {
  color: inherit;
  text-decoration: none;
  transition: 0.4s;
  text-transform: lowercase;
}
.about-people-card .social-link i {
  font-size: 18px;
  width: 25px;
  height: 25px;
  line-height: 25px;
  border-radius: 50%;
  color: var(--white);
  background-color: var(--text-primary);
  text-align: center;
  transition: 0.4s;
}
.career-box-right,
.career-green-box .border-box,
.choose-card-box,
.experience-box::after,
.insight-box-area::after,
.testimonial-section::before {
  border: 1px solid var(--border);
}
.about-people-card .quote-box {
  background-color: var(--primary-red);
  padding: 20px;
  width: 100%;
  max-width: 390px;
  position: absolute;
  right: 40px;
  color: #fff;
  font-weight: 700;
  min-height: auto;
  bottom: auto;
  margin-top: 30px;
}
.experience-box {
  background-color: var(--primary-blue);
  padding: 30px 70px;
  color: var(--white);
  width: 100%;
  max-width: 540px;
  display: block;
  position: relative;
}
.experience-box::after {
  content: "";
  position: absolute;
  display: block;
  right: -30px;
  bottom: -90px;
  height: 180px;
  width: 270px;
  background-color: var(--white);
  z-index: -1;
}
.experience-box::before {
  content: "";
  position: absolute;
  display: block;
  left: -90px;
  top: -90px;
  height: 85%;
  width: 100%;
  max-width: 540px;
  background-color: var(--primary-blue-light);
  z-index: -1;
}
.experience-box .title {
  font-size: 18px;
  font-weight: 700;
  color: var(--white);
}
.experience-box .subtitle {
  font-size: 15px;
  font-weight: 600;
  color: var(--white);
  margin-bottom: 0;
  position: relative;
}
.experience-box .subtitle::before,
.experience-box h6:before {
  content: "\f285";
  font-family: Bootstrap-icons;
  position: absolute;
  top: 4px;
  left: -20px;
  font-size: 10px;
  color: var(--white);
}
.experience-box ul {
  list-style: none;
  margin: 0 0 20px;
  padding: 0;
}
.experience-box ul li {
  color: var(--white);
  font-size: 14px;
  font-weight: 300;
  margin-bottom: 4px;
}
.career-card {
  position: relative;
  z-index: 2;
  height: 100%;
}
.career-card-two.style-green,
.career-card.style-green .content {
  background-color: var(--primary-green-dark);
}
.career-card .image-wrap img {
  height: 210px;
  width: 100%;
  -o-object-fit: cover;
  object-fit: cover;
  transition: 0.4s;
}
.career-card .content {
  padding: 15px 15px 60px;
  min-height: calc(100% - 270px);
}
.career-card .content .btn--lg2 {
  position: absolute;
  bottom: 15px;
}
.career-card .content h5 {
  font-size: 13px;
  color: var(--white);
  margin-bottom: 0;
}
.career-card .content p {
  color: var(--white);
  margin-bottom: 35px;
  font-size: 13px;
}
.career-card-two {
  position: relative;
  padding: 20px 20px 60px;
  z-index: 2;
  min-height: 100%;
}
.career-card-two .btn--lg2 {
  position: absolute;
  bottom: 20px;
  text-transform: none;
}
.career-card-two .title {
  position: relative;
  display: inline-block;
  padding-bottom: 10px;
  margin-bottom: 10px;
}
.career-card-two .title::after {
  content: "";
  position: absolute;
  bottom: 0;
  left: 0;
  display: block;
  height: 1px;
  width: 100%;
  background-color: var(--white);
}
.career-card-two .title h5 {
  font-size: 16px;
  color: var(--white);
  margin-bottom: 0;
}
.career-card-two p {
  color: var(--white);
  margin-bottom: 60px;
}
.career-card-section .red-box {
  position: absolute;
  top: -100px;
  right: 5%;
  background-color: var(--primary-red);
  height: 380px;
  width: 100%;
  max-width: 670px;
  z-index: 1;
}
.career-box-right {
  width: 100%;
  max-width: 570px;
  height: 100%;
  max-height: 770px;
  margin-top: -170px;
  position: absolute;
}
.career-box-right::before {
  content: "";
  position: absolute;
  bottom: 90px;
  right: -30%;
  width: 100%;
  max-width: 570px;
  background-color: var(--primary-green-dark-light);
  height: 380px;
}
.career-right-image {
  width: 100%;
  max-width: 570px;
  margin-top: 310px;
  margin-left: 90px;
  z-index: 1;
  position: relative;
  margin-bottom: 190px;
}
.big-card,
.choose-us-card {
  z-index: 2;
  position: relative;
}
.career-right-image2 {
  width: 100%;
  max-width: 280px;
}
.career-right-quote {
  background-color: var(--primary-red);
  padding: 20px;
  min-height: 300px;
  width: 100%;
  max-width: 480px;
  margin-top: -180px;
}
.career-box-right2 {
  width: 100%;
  max-width: 570px;
  background-color: var(--primary-yellow-light2);
  min-height: 270px;
  display: block;
  margin-top: -100px;
  margin-left: 30px;
}
.career-right-quote2 {
  background-color: var(--primary-red);
  padding: 40px 35px 110px;
}
.career-right-quote2 p {
  font-size: 16px;
  font-style: italic;
  color: var(--white);
}
.career-green-box::after {
  content: "";
  width: 50%;
  height: 280px;
  background-color: var(--primary-green);
  position: absolute;
  right: 0;
  bottom: 90px;
  z-index: -1;
}
.career-green-box::before {
  content: "";
  width: 50%;
  height: 230%;
  background-color: var(--primary-green-dark-light);
  position: absolute;
  right: 90px;
  top: -90px;
  z-index: -1;
}
.career-green-box .border-box {
  width: 75%;
  height: 180px;
}
.insight-box-area::after {
  content: "";
  width: 45%;
  height: 180px;
  position: absolute;
  right: 30px;
  bottom: 90px;
}
.insight-box-area .red-box {
  width: 68%;
  height: 180px;
  background-color: var(--primary-red);
  margin-right: auto;
}
.skill-card {
  background-color: var(--primary-blue);
  padding: 20px;
  min-height: 300px;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  transition: 0.45s;
}
.skill-card .content h5 {
  font-size: 17px;
  font-weight: 600;
  color: var(--white);
}
.skill-card .content p {
  color: var(--white);
  margin-bottom: 20px;
  font-size: 15px;
}
.skill-card .icon {
  max-width: 90px;
  margin-left: auto;
  margin-right: 0;
  transition: 0.45s 0.3s;
}
.skill-card .icon.communicate svg {
  fill: none;
  stroke: var(--text-primary);
  max-width: 90px;
}
.skill-card .icon svg {
  fill: var(--text-primary);
  transition: 0.45s;
  max-width: 90px;
}
.choose-us-card ul li:first-child,
.content-italic h6,
.content-italic p {
  font-style: italic;
}
.testimonial-section {
  padding-left: 90px;
  position: relative;
  padding-top: 180px;
}
.ecpertise-img-cont.t-tropic,
.page-id-651 .expertise-section-1,
.page-id-651 .testimonial-section,
.page-id-8268 .expertise-section-1,
.page-template-template-career-business-services .expertise-section-1 {
  margin-top: 0;
}
.testimonial-section::before {
  content: "";
  content: "";
  position: absolute;
  right: 58%;
  top: 0;
  width: 100%;
  max-width: 35%;
  height: 30%;
  display: block;
  z-index: -2;
}
.testimonial-section .testi-top-image {
  max-width: 460px;
  position: absolute;
  right: 10%;
  top: -275px;
}
.testimonial-section .box-style-new-two {
  background-color: var(--primary-yellow-light);
  width: 250px;
  height: 250px;
  display: block;
  position: absolute;
  left: 31%;
  top: -15%;
  z-index: -3;
}
.testimonial-section .testi-top-image img {
  width: 100%;
  height: 365px;
  -o-object-fit: cover;
  object-fit: cover;
}
.big-card {
  padding: 90px 90px 80px 80px;
  display: flex;
  gap: 50px;
  height: auto;
  width: 100%;
  max-width: 1260px;
}
.big-card.style-blue .body {
  border-top: 1px solid var(--primary-blue);
  border-bottom: 1px solid var(--primary-blue);
}
.big-card.style-yellow .body {
  border-top: 1px solid var(--primary-yellow);
  border-bottom: 1px solid var(--primary-yellow);
}
.big-card.style-red {
  background-color: var(--primary-red-light);
}
.eligibility-card.style-green::before,
.skill-section .skill-box-design {
  width: 100%;
  background-color: var(--primary-green-dark-light);
  position: absolute;
  z-index: -1;
}
.big-card.style-red .body {
  border-top: 1px solid var(--primary-red);
  border-bottom: 1px solid var(--primary-red);
}
.big-card .content .title h4 {
  font-size: 24px;
  font-weight: 300;
}
.big-card .content .body {
  padding: 20px 0;
}
.big-card .content .body p {
  font-size: 17px;
  font-style: italic;
}
.big-card .author {
  width: 100%;
  max-width: 190px;
  min-width: 190px;
  margin-top: 42px;
}
.big-card .author img {
  margin-bottom: 10px;
}
.big-card .author h6 {
  margin-bottom: 0;
  font-size: 13px;
  font-weight: 600;
}
.big-card .author span {
  display: block;
  font-size: 12px;
  line-height: 1.3;
}
.skill-section .skill-box-design {
  bottom: 0;
  right: 5%;
  max-width: 460px;
  height: 60%;
}
.choose-us-card {
  background-color: var(--primary-red);
  padding: 140px 70px 90px;
  width: 100%;
  max-width: 640px;
  margin-left: auto;
  margin-right: 0;
  margin-top: -300px;
}
.choose-us-card h2 {
  font-size: 40px;
  font-weight: 700;
  line-height: 1;
  position: relative;
  color: var(--white);
  padding-bottom: 55px;
  margin-bottom: 30px;
}
.choose-us-card h2::after {
  content: "";
  position: absolute;
  left: 0;
  bottom: 0;
  width: 80px;
  background-color: var(--white);
  height: 1px;
}
.choose-us-card ul li {
  font-size: 35px;
  font-weight: 300;
  color: var(--white);
  line-height: 1.3;
}
.choose-card-box {
  width: 100%;
  max-width: 475px;
  margin-left: 0;
  margin-right: auto;
  margin-top: -180px;
}
.career-details-section {
  padding-left: 90px;
}
.career-left-image {
  max-width: 460px;
  margin-left: auto;
  position: relative;
  margin-top: -150px;
}
.career-left-image::before {
  content: "";
  position: absolute;
  bottom: 90px;
  right: 35%;
  width: 460px;
  height: 100%;
  background-color: var(--primary-red-light);
  z-index: -1;
}
.eligibility-card {
  background-color: var(--primary-yellow);
  padding: 30px 60px;
  width: 100%;
  max-width: 670px;
  margin-left: auto;
  margin-right: -90px;
  margin-top: -90px;
  position: relative;
}
.eligibility-card.style-green {
  background-color: var(--primary-green-dark);
  margin-left: -90px;
  margin-right: auto;
  margin-top: 15%;
  padding: 30px 50px;
  max-width: 9 0%;
}
.eligibility-card.style-green::before {
  content: "";
  max-width: 770px;
  height: 380px;
  display: block;
  left: -90px;
  top: -15%;
}
.eligibility-card.style-blue {
  background-color: var(--primary-blue);
  padding: 30px 40px;
  width: 95%;
  max-width: 670px;
  margin-left: 0;
  margin-right: auto;
  margin-top: 50px;
  position: relative;
}
.eligibility-card.style-blue::before,
.eligibility-card::before {
  height: 100%;
  display: block;
  position: absolute;
  left: -90px;
  bottom: 160px;
  content: "";
  max-width: 670px;
  z-index: -1;
  width: 100%;
}
.eligibility-card.style-blue::before {
  background-color: var(--primary-blue-light);
}
.eligibility-card::before {
  background-color: var(--primary-yellow-light);
}
.eligibility-card .title {
  position: relative;
  padding-bottom: 5px;
  display: inline-block;
  margin-bottom: 18px;
}
.eligibility-card .title h6 {
  color: var(--white);
  font-size: 18px;
  text-transform: uppercase;
}
.eligibility-card .title::after {
  content: "";
  position: absolute;
  left: 0;
  bottom: 0;
  width: 100%;
  height: 1px;
  background-color: var(--white);
}
.eligibility-card p {
  font-size: 15px;
  color: var(--white);
}
.eligibility-card .date span {
  display: inline-block;
  color: var(--white);
  font-size: 15px;
}
.case-author-single {
  position: relative;
  width: 100%;
  max-width: 290px;
  margin-left: auto;
  margin-right: -90px;
  margin-top: 190px;
}
.business-parnter-single .image-wrap,
.case-author-single .image-wrap {
  margin-bottom: 8px;
}
.business-parnter-single .content h6,
.case-author-single .content h6 {
  margin-bottom: 0;
  font-size: 15px;
}
.business-parnter-single .content span,
.case-author-single .content span {
  display: block;
  font-size: 15px;
  line-height: 1.3;
}
.business-parnter-single {
  position: relative;
  width: 100%;
  max-width: 380px;
  margin-left: auto;
  margin-right: 0;
  margin-top: 100px;
}
.business-parnter-single::before {
  content: "";
  position: absolute;
  right: 45%;
  bottom: 155px;
  width: 100%;
  min-width: 580px;
  height: 480px;
  background-color: var(--primary-yellow-light2);
  z-index: -1;
}
.senior-parnter-single h6 {
  font-size: 12px;
  margin-bottom: 0;
  line-height: 1.3;
  font-weight: 600;
}
.senior-parnter-single span {
  display: block;
  line-height: 1.2;
  font-size: 12px;
}
.apply-area {
  position: relative;
  display: block;
}
.apply-area::before {
  content: "";
  position: absolute;
  left: 40%;
  bottom: 90px;
  background-color: var(--primary-yellow-light);
  width: 100%;
  max-width: 670px;
  height: 280px;
  z-index: -1;
}
.apply-card {
  background-color: var(--primary-blue);
  min-height: 270px;
  width: 100%;
  max-width: 400px;
  padding: 30px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  margin: 0 auto;
}
.apply-card.style-yellow {
  background-color: var(--primary-yellow);
  margin-top: 90px;
}
.apply-card.style-green,
.offer-card {
  background-color: var(--primary-green-dark);
}
.apply-card.style-green {
  margin-top: 90px;
}
.apply-card h5 {
  color: var(--white);
  margin-bottom: 0;
  line-height: 1;
  font-size: 18px;
}
.apply-card .h-line {
  width: 100%;
  display: block;
  max-width: 240px;
  height: 1px;
  background-color: var(--white);
  margin: 30px 0;
}
.apply-image::before,
.trainee-placement-image::before {
  height: 280px;
  left: -90px;
  z-index: -1;
  width: 100%;
  position: absolute;
  border: 1px solid var(--border);
  content: "";
}
.apply-image {
  width: 100%;
  max-width: 380px;
  height: 380px;
  margin-left: auto;
  margin-right: auto;
  position: relative;
  margin-top: 0;
}
.apply-image::before {
  max-width: 380px;
  top: -150px;
}
.apply-image img {
  -o-object-fit: cover;
  object-fit: cover;
}
.trainee-placement-image {
  position: relative;
  margin-top: 90px;
}
.trainee-placement-image::before {
  bottom: 0;
}
.facility-image {
  position: relative;
  width: 100%;
  height: 390px;
  border: 1px solid var(--border);
}
.facility-image img {
  position: absolute;
  right: 90px;
  bottom: 90px;
  z-index: 1;
  width: 570px;
  height: 470px;
  -o-object-fit: cover;
  object-fit: cover;
}
.attorny-left-image {
  position: relative;
  margin-top: 190px;
  text-align: right;
}
.attorny-left-image::before {
  content: "";
  position: absolute;
  background-color: var(--primary-yellow-light2);
  width: 100%;
  height: 380px;
  left: -90px;
  top: -190px;
  z-index: -1;
}
.attorny-left-image img {
  z-index: 1;
  -o-object-fit: cover;
  object-fit: cover;
  max-width: 340px;
  text-align: right;
}
.offer-card {
  padding: 50px;
  width: 100%;
  min-width: 670px;
  margin-left: -212px;
  margin-top: 270px;
  z-index: 3;
  position: relative;
}
.offer-card .title h4 {
  font-size: 14px;
  color: var(--white);
  text-transform: uppercase;
}
.offer-card .list-block h6 {
  color: var(--white);
  font-size: 16px;
  font-weight: 600;
  margin-bottom: 0;
}
.offer-card .list-block ul {
  list-style: none;
  padding: 0;
  margin: 0;
}
.offer-card .list-block ul li {
  font-size: 15px;
  color: var(--white);
  margin-bottom: 5px;
}
.recruitment-image {
  position: relative;
  margin-bottom: 180px;
}
.recruitment-image::after {
  content: "";
  position: absolute;
  bottom: -180px;
  right: 90px;
  min-width: 650px;
  width: 100%;
  height: 400px;
  background-color: var(--primary-green-dark-light);
  z-index: -1;
}
.tip-left-box {
  width: 100%;
  max-width: 570px;
  height: 470px;
  border: 1px solid var(--border);
  margin-top: -90px;
  z-index: 2;
  position: relative;
}
.tips-section {
  padding-left: 3%;
}
.about-section .quote-box {
  background-color: var(--primary-red);
  padding: 35px 45px;
  min-height: 380px;
  width: 100%;
  max-width: 450px;
  margin-left: auto;
  margin-right: auto;
  position: relative;
}
.about-section .quote-box::before {
  content: "";
  position: absolute;
  left: 50%;
  transform: translateX(-50%);
  height: 380px;
  width: 130%;
  background-color: var(--primary-red-light);
  top: -190px;
  z-index: 1;
}
.about-section .author-image {
  max-width: 380px;
  margin-top: 190px;
  position: relative;
}
.about-section .author-image::before {
  content: "";
  position: absolute;
  border: 1px solid var(--border);
  width: 100%;
  max-width: 380px;
  height: 380px;
  z-index: -1;
  left: 82%;
  bottom: 75%;
}
.about-section .author-image img,
.culture-section .value-card img {
  width: 100%;
  -o-object-fit: cover;
  object-fit: cover;
  margin-left: 90px;
}
.counter-single,
.home H1 {
  text-align: center;
}
.counter-single.style-yellow h3 {
  color: var(--primary-yellow);
  position: relative;
}
.counter-single.style-yellow h3::after {
  content: "\f4d1";
  font-family: Bootstrap-icons;
  position: absolute;
  right: -25px;
  top: 32px;
  font-size: 32px;
  color: var(--primary-yellow);
}
.counter-single.style-blue h3 {
  color: var(--primary-blue);
  position: relative;
}
.counter-single.style-blue h3::after {
  content: "\f64d";
  font-family: Bootstrap-icons;
  position: absolute;
  right: -30px;
  top: 50%;
  transform: translateY(-50%);
  font-size: 32px;
  color: var(--primary-blue);
}
.counter-single h3 {
  font-size: 130px;
  font-weight: 300;
  line-height: 1;
  letter-spacing: -8px;
  margin-bottom: 15px;
}
.counter-single p {
  margin-bottom: 0;
  font-size: 15px;
  line-height: 1.4;
}
.sponsor-wrapper {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 15px;
  flex-wrap: nowrap;
}
.sponsor-wrapper a {
  max-width: auto;
}
.culture-section .bottom-box {
  position: absolute;
  width: 100%;
  max-width: 380px;
  height: 300px;
  background-color: var(--primary-red-light);
  right: 15%;
  bottom: 0;
}
.culture-section .quote-box {
  background-color: var(--primary-green-dark-light);
  padding: 35px 45px;
  min-height: 380px;
  width: 100%;
  max-width: 450px;
  margin-left: auto;
  margin-right: auto;
  position: relative;
}
.culture-section .quote-box p {
  color: var(--text-primary);
  font-size: 20px;
  font-weight: 300;
  line-height: 1.3;
  margin-bottom: 10px;
}
.culture-section .quote-box span {
  font-size: 12px;
  font-weight: 700;
  color: var(--text-primary);
}
.culture-section .value-card {
  max-width: 350px;
  margin-top: 90px;
  position: relative;
  background-color: var(--primary-red);
  padding: 45px;
  min-height: 350px;
}
.trending-right-box,
.un-logo-wrap::after {
  background-color: var(--primary-red-light);
  position: absolute;
}
.culture-section .value-card h6 {
  color: var(--white);
  font-weight: 600;
  margin-bottom: 0;
  font-size: 19px;
}
.culture-section .value-card p,
.culture-section .value-card ul li {
  color: var(--white);
  font-size: 19px;
  line-height: 1.2;
}
.culture-section .value-card::before {
  content: "";
  position: absolute;
  border: 1px solid var(--border);
  width: 100%;
  max-width: 380px;
  height: 380px;
  z-index: -1;
  left: 90%;
  bottom: 75%;
}
.about-news-box {
  position: absolute;
  width: 30%;
  right: 30%;
  height: 300px;
  border: 1px solid var(--border);
  top: -90px;
}
.trending-right-box {
  top: 0;
  right: 5%;
  width: 100%;
  max-width: 40%;
  height: 380px;
}
.un-logo-wrap {
  margin-top: 35px;
  display: grid;
  grid-template-columns: repeat(9, 1fr);
  grid-gap: 15px;
  position: relative;
  padding-bottom: 180px;
  display: flex;
  flex-direction: row;
  flex-wrap: nowrap;
  /* justify-content: center; */
  align-content: center;
  align-items: stretch;
}
.address-list,
.key-contact-list {
  padding: 15px 0 0;
  position: relative;
  list-style: none;
}
.un-logo-wrap::after {
  content: "";
  right: -90px;
  top: -90px;
  width: 80%;
  height: 485px;
}
.un-logo-wrap .logo-wrap {
  max-width: 160px;
  flex-grow: 1;
  flex-basis: 0%;
}
.business-card-section .business-card-box {
  position: absolute;
  border: 1px solid var(--border);
  bottom: 150px;
  left: -3%;
  max-width: 280px;
  width: 100%;
}
.business-card-section::before {
  content: "";
  position: absolute;
  top: 40%;
  left: calc((100% - 1040px) / 2);
  transform: translateY(-50%);
  width: 28.8%;
  height: 55%;
  background-color: var(--primary-blue-light);
  z-index: -1;
}
.address-section {
  background-color: var(--primary-yellow-light2);
}
.address-single h6 {
  font-size: 14px;
  margin-bottom: 0;
}
.address-single .details {
  min-height: 220px;
  margin-bottom: 25px;
}
.address-list {
  margin: 0 0 30px;
}
.address-list::before,
.key-contact-list::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  max-width: 90px;
  height: 1px;
  background-color: var(--text-primary);
}
.address-list li,
h6 {
  line-height: 1.3;
}
.key-contact-list {
  margin: 0;
}
.key-contact-list li {
  line-height: 1.3;
  margin-bottom: 15px;
}
.key-contact-list li span {
  display: block;
  font-size: 14px;
  font-weight: 300;
  line-height: 1;
}
.openday-section {
  padding-left: 8%;
  padding-right: 8%;
}
.openday-section .box-right {
  width: 100%;
  max-width: 550px;
  height: 320px;
  border: 1px solid var(--border);
  margin-top: -300px;
}
.openday-section .openday-apply {
  margin-left: 90px;
  margin-top: -90px;
}
.openday-section .attendents {
  max-width: 280px;
  width: 100%;
  margin-right: auto;
  text-align: right;
}
.openday-section .attendents h2 {
  font-size: 180px;
  margin-bottom: 0;
  font-weight: 300;
  color: var(--primary-green-dark);
  letter-spacing: -8px;
  position: relative;
  display: inline-block;
  line-height: 1;
}
.openday-section .attendents h2::after {
  content: "\f4d1";
  font-family: Bootstrap-icons;
  position: absolute;
  right: -35px;
  top: 55px;
  font-size: 45px;
  color: vvar(--primary-green-dark);
}
.openday-section .attendents h2:first-letter {
  letter-spacing: -30px;
}
.openday-section .attendents span {
  display: block;
  width: 100%;
  max-width: 170px;
  margin-left: auto;
  font-size: 12px;
  font-weight: 700;
  text-align: left;
}
.trending-section {
  padding-right: 5%;
}
.trending-section .trending-image {
  margin-top: 90px;
}
form.filter-search-form select {
  border: none;
  border-radius: unset;
  width: 100%;
  background: var(--white);
  transition: 0.3s ease-in-out;
  color: var(--text-primary);
  padding: 25px 20px;
  height: 80px;
  outline: 0;
  display: block;
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
  appearance: none;
  background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
  background-repeat: no-repeat;
  background-position: right 1rem center;
  background-size: 1em;
}
.home-page-banner-search-list {
  padding: 20px 30px;
  background-color: rgb(0 0 0 / 0.85);
  position: absolute;
  top: 0;
  z-index: 9;
  width: 100%;
}
.close-home-search {
  color: #fff;
  font-size: 30px;
  font-weight: 700;
}
.sjb-page .btn-primary,
.sjb-page .btn-primary:active:focus,
.sjb-page .btn-primary:active:hover,
.sjb-page .btn-primary:hover,
.sjb-page .sjb-detail .jobpost-form .file div,
.sjb-page .sjb-detail .jobpost-form .file:hover div {
  background-color: var(--primary-yellow) !important;
}
.sjb-page .sjb-filters.sjb-filters-v2 {
  background-color: var(--primary-yellow-light);
  display: none;
}
.sjb-page .sjb-filters.sjb-filters-v2 .form-control {
  box-shadow: none;
  border: none;
  padding: 25px 20px;
  height: 80px;
  color: var(--text-primary);
  font-size: inherit;
  font-weight: inherit;
  font-family: inherit;
}
.sjb-page .sjb-filters.sjb-filters-v2 .btn {
  height: 80px;
  font-size: 30px;
}
.about-people-card .designation {
  background-color: #ff2500;
  padding: 25px 30px;
  position: absolute;
}
.about-people-card .designation h1,
.about-people-card .designation h2,
.download-card-box-content a,
.home-page-banner-search-list .search-result-list a,
.single-people .experience-box a {
  color: #fff;
}
.about-people-card .designation h1 {
  font-size: 30px;
  margin-bottom: 60px;
}
.about-people-card .designation h2 {
  font-size: 18px;
}
.about-people-card .box {
  height: 250px;
}
header.style-1 .main-nav > ul > li a {
  font-size: 12px;
}
.sjb-filters .row > [class^="sjb-search-"] {
  width: 20%;
  padding-right: 5px;
  padding-left: 5px;
}
.single-sector .about-people-card {
  margin-top: 40px;
  min-height: 200px;
}
.sjb-page .job-features h3,
.sjb-page .list-data .v1 .job-date,
.sjb-page .list-data .v1 .job-location,
.sjb-page .list-data .v1 .job-type,
.sjb-page .list-data .v2 .job-date,
.sjb-page .list-data .v2 .job-location,
.sjb-page .list-data .v2 .job-type,
.sjb-page .sjb-archive-page .job-title,
.sjb-page .sjb-detail .list-data .v1 .job-detail h3,
.sjb-page .sjb-detail .list-data .v1 h3,
.sjb-page .sjb-detail .list-data .v2 .job-detail h3,
.sjb-page .sjb-detail .list-data .v2 h3,
.sjb-page .sjb-detail .list-data ul li::before {
  color: #000 !important;
}
.banner-content h1,
.banner-content.style-dark h1,
.banner-content.style-dark h2,
.banner-slider-content H1 {
  color: #fff;
  font-size: 3.6em;
}
.single-bulletin h1.display-4 {
  font-size: 22px;
}
.single-sector .team-section .container-one > div:nth-of-type(2) h4,
header.style-1 {
  padding: 0;
}
.menu-container-box {
  z-index: 99;
}
.trademark-iage-green-box {
  background-color: var(--primary-green-dark-light);
  width: 200px;
  margin: 200px 0 0;
  height: 250px;
  position: relative;
}
.trademark-iage-green-box .border-box {
  border: 1px solid var(--border);
  position: absolute;
  top: -150px;
  left: -50px;
  z-index: 1;
  width: 150%;
  height: 250px;
}
.trademark-iage-green-box .image-box {
  width: 170px;
  height: 170px;
  position: absolute;
  z-index: 2;
  top: 50px;
  right: -100px;
}
.design-team-image-box {
  background-color: var(--primary-red-light);
  width: 250px;
  margin-left: auto;
  margin-right: 0;
  height: 250px;
  position: relative;
  margin-top: 20px;
}
.design-team-image-box img {
  position: absolute;
  top: 55%;
  left: -30%;
  width: 300px;
}
.plant-rights-border-box {
  background-color: var(--primary-blue-light);
  width: 250px;
  height: 250px;
  margin-top: 100px;
  position: relative;
}
.plant-rights-border-box .border-box {
  border: 1px solid var(--border);
  position: absolute;
  width: 100%;
  height: 100%;
  left: -20%;
  top: -80px;
}
.about-us-quote-border-box {
  position: relative;
  background-color: var(--primary-yellow-light2);
  width: 300px;
  height: 350px;
  margin-left: auto;
  margin-right: 0;
  margin-top: 100px;
}
.about-us-quote-border-box .border-box {
  position: absolute;
  width: 90%;
  height: 50%;
  top: 80%;
  right: 80%;
  border: 1px solid var(--border);
  z-index: 1;
}
.about-us-quote-border-box .quote-box {
  position: absolute;
  background-color: var(--primary-yellow);
  width: 100%;
  height: 50%;
  top: 40%;
  right: 40%;
  z-index: 2;
  font-size: 13px;
  color: #fff;
  padding: 10px 15px;
}
.working-image-box {
  width: 200px;
  margin: 250px auto auto;
  height: 400px;
  position: relative;
  background-color: var(--primary-red-light);
}
.working-image-box .box-1 {
  position: absolute;
  top: -20%;
  left: -40%;
  z-index: 1;
  width: 100%;
}
.working-image-box .box-2 {
  position: absolute;
  z-index: 2;
  width: 130%;
  top: 50%;
  left: 20%;
}
.mt-150 {
  margin-top: 150px;
}
.ip-expert-image-border-box {
  position: relative;
  width: 150px;
  background-color: var(--primary-blue-light);
  height: 300px;
  margin-left: auto;
  margin-right: 0;
}
.ip-expert-image-border-box .border-box {
  position: absolute;
  z-index: 1;
  border: 1px solid var(--border);
  width: 300%;
  height: 300px;
  right: 10%;
  top: 60%;
}
.ip-expert-image-border-box .image-box {
  position: absolute;
  z-index: 2;
  width: 270%;
  height: 300px;
  top: 110%;
  right: 0;
}
.mb-500 {
  margin-bottom: 300px;
}
.ip-experts-section .sector-item p {
  font-size: 13px;
  margin-bottom: 10px;
}
.page-template-home-ger .quote-box2 p,
.page-template-home-ger p,
.page-template-home-jp .quote-box2 p,
.page-template-home-jp p,
.page-template-home-kr p {
  font-size: 15px;
  margin-bottom: 10px;
}
.page-template-home-ger .footer-address p,
.page-template-home-jp .footer-address p,
.page-template-home-kr .footer-address p {
  font-size: 12px;
  margin-bottom: 0;
}
.page-template-home-ger .footer-bottom p,
.page-template-home-jp .footer-bottom p,
.page-template-home-kr .footer-bottom p {
  font-size: 11px;
  margin-bottom: 1rem;
}
.passle-blog-excerpt {
  height: 85px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 6;
  -webkit-line-clamp: 4;
  white-space: pre-wrap;
}
.passle-blog-heading {
  min-height: 85px;
}
.experience-box h6 {
  font-weight: 500;
  font-size: 15px;
  color: var(--white);
  margin-bottom: 0;
  position: relative;
}
#tribe-events-pg-template,
.tribe-events-view {
  padding-top: 200px;
}
.tribe-common--breakpoint-medium.tribe-events .tribe-events-l-container {
  max-width: 1020px;
}
.tribe-common--breakpoint-medium.tribe-common .tribe-common-l-container {
  padding-left: 15px;
  padding-right: 15px;
}
.tribe-common--breakpoint-medium.tribe-events
  .tribe-events-header--has-event-search {
  background-color: var(--primary-yellow-light);
  padding-top: 100px;
  padding-bottom: 100px;
}
.container-one {
  margin: auto !important;
}
.tribe-events-header .container-one {
  margin: auto;
}
.tribe-events .tribe-events-header {
  padding-top: 100px;
  padding-bottom: 100px;
}
.tribe-events-calendar-latest-past {
  padding-top: 100px !important;
  padding-bottom: 100px !important;
}
.page-template-template-career-business-services
  .testimonial-section
  .testi-top-image,
.page-template-template-career-experienced-attorneys
  .testimonial-section
  .testi-top-image {
  top: -210px;
}
.counter-section > .container-one {
  max-width: 1200px;
}
.culture-content-2 {
  position: absolute;
  max-width: 400px;
  margin-top: 10px;
  background-color: var(--primary-blue-light);
  padding: 30px;
  font-size: 16px;
}
.culture-content-2:after {
  display: block;
  border: 1px solid var(--border);
  position: absolute;
  top: 80%;
  left: 55%;
  width: 100%;
  height: 80%;
  z-index: -1;
}
.page-template-template-career-business-services .facilities-section {
  margin-top: -400px;
}
.video-banner-bg {
  z-index: -2;
}
.inner-overlay {
  position: absolute;
  width: 100%;
  height: 100%;
  left: 0;
  top: 0;
  z-index: -1;
  background-image: linear-gradient(rgba(0, 0, 0, 0.15), rgba(0, 0, 0, 0.15));
}
.banner-slider-content {
  background: linear-gradient(rgba(0, 0, 0, 0.15), rgba(0, 0, 0, 0.15));
}
.experience-box h6,
.page-template-template-career-recruitment-process
  .event-section.position-relative {
  margin-top: 20px;
}
.page-template-template-services .section-title-one h5 {
  font-size: 1.25rem;
  font-weight: 300;
  margin-top: 20px;
}
.mt--50,
.page-template-template-career-business-services .testimonial-section {
  margin-top: -50px;
}
.banner-single-post-title {
  background-color: var(--primary-blue);
  color: var(--white);
  padding: 50px 0 100px;
  top: calc(10vh);
  left: 0;
}
.banner-single-post-title:before {
  content: "";
  border: 1px solid var(--border);
  width: 300px;
  height: 300px;
  position: absolute;
  right: -50px;
  bottom: 60%;
  z-index: -1;
}
.banner-single-post-meta {
  position: absolute;
  bottom: 50px;
  right: 50px;
}
.title-position-design {
  text-align: left;
  display: inline-block;
  margin-bottom: 0;
}
.title-position-design > span {
  border-top: 1px solid var(--white);
  display: block;
}
.recent-highlight-inner {
  background-color: #3aaa35;
  color: var(--white);
  padding: 40px;
  margin-top: 50px;
}
.recent-highlight-inner h2 {
  color: var(--white);
  text-transform: uppercase;
}
.border-bar {
  display: block;
  width: 100px;
  border-top: 1px solid var(--white);
  margin-top: 25px;
  margin-bottom: 20px;
}
.what-we-do-section {
  background-color: var(--primary-yellow-light);
  z-index: -1;
}
.box-design-new-one {
  background-color: var(--primary-blue);
  width: 550px;
  height: 400px;
  position: absolute;
  right: -150px;
  top: -14%;
  z-index: -1;
}
.recruitment-process-quotes-single {
  background-color: var(--primary-yellow-light);
  background-color: #fff7f7;
  padding: 30px;
}
.address-single .details p,
.recruitment-process-quotes-single p,
.single-bulletin.postid-15293 .author-name-section h6 a,
.single-bulletin.postid-15293 .author-name-section span,
.single-service .what-we-do-section,
.single-service .what-we-do-section h6,
.single-trending_topics .author-name-section h6 a,
.single-trending_topics .author-name-section span,
.team-item .team-content h6 {
  font-size: 13px;
}
.recruitment-process-quotes-single h2 {
  display: block;
  font-size: 13px;
  font-weight: 700;
}
.quote-author,
.section-title-one p.quote-author {
  font-size: 13px;
  font-weight: 600;
}
.author .author-img {
  width: 100%;
  height: 180px;
  margin-bottom: 10px;
}
.author > span,
.breadcrumps-wrap .breadcrumb_last {
  text-transform: capitalize;
}
.author span,
.pagination ul li {
  text-transform: uppercase;
}
.author .author-img img {
  margin-bottom: 0;
  width: 100%;
  height: 100%;
  object-fit: cover;
}
.no-person-image-box {
  position: absolute;
  width: 100%;
  height: 100%;
  left: 0;
  top: 0;
  background-color: var(--primary-green);
}
.page-template-template-about
  .counter-section
  .counter-row
  > .counter-item:nth-child(2)
  .odometer
  .letter:nth-child(4),
.page-template-template-about
  .counter-section
  .counter-row
  > .counter-item:nth-child(4)
  .odometer
  .letter:nth-child(3) {
  font-size: 50%;
}
.page-template-template-career-early-days
  .counter-section
  .counter-row
  > .counter-item
  .odometer
  .letter:last-child {
  font-size: 50%;
}
.counter-section .counter-row > .counter-item:nth-child(3) .odometer {
  color: var(--primary-green);
}
.banner-auther {
  position: absolute;
  left: 50px;
  top: 70%;
  display: flex;
  gap: 40px;
  align-items: end;
}
.banner-author-list {
  display: flex;
  gap: 30px;
  align-items: end;
}
.cky-notice-btn-wrapper,
.home-page-banner-search-list .people-list:first-child {
  display: block !important;
}
.banner-auther img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}
.banner-auther .banner-auther-image {
  width: 180px;
  height: 180px;
}
.author-name-section p {
  margin-bottom: 0;
  line-height: 5px;
  color: #000;
}
.author-name-section h6 {
  color: #000;
  font-weight: 600;
  font-size: 20px;
  margin-bottom: 0;
}
.author-name-section span,
.banner-content H1,
.banner-slider-content H1,
.company-vdo H3 {
  color: #ffffff;
}
.author-name-label {
  font-style: italic;
  font-size: 15px;
}
.author-name-section h6 a {
  color: #000 !important;
  font-weight: 700;
  font-size: 15px;
}
.key-contact-list li,
.pagination ul li {
  font-size: 13px;
  font-weight: 700;
}
.author-name-section .border-bottom-el {
  display: block;
  border-top: 1px solid var(--border);
  width: 80px;
  margin-top: 10px;
}
.single-bulletin #theme-main a,
.single-news #theme-main a,
.single-newsletter #theme-main a,
.single-upc_news #theme-main a {
  color: #00008b;
}
.single-bulletin #theme-main a:hover,
.single-ip_app_process .banner-single-post-title .breadcrumps-wrap span a:hover,
.single-ip_basics .banner-single-post-title .breadcrumps-wrap span a:hover,
.single-news #theme-main a:hover,
.single-newsletter #theme-main a:hover,
.single-patent_essential
  .banner-single-post-title
  .breadcrumps-wrap
  span
  a:hover,
.single-upc_news #theme-main a:hover {
  text-decoration: underline;
}
.author .no-person-image-box {
  position: relative;
  width: 100%;
  height: 180px;
  margin-bottom: 10px;
}
.recent-highlight-quotes {
  position: relative;
  margin-bottom: 200px;
}
.recent-highlight-quotes .box-inner-content {
  background-color: var(--primary-red);
  color: #fff;
  padding: 30px;
}
.recent-highlight-quotes .box-inner-content h6 {
  margin-bottom: 0;
  margin-top: 20px;
}
.recent-highlight-quotes .box-inner-content span {
  font-size: 11px;
  display: block;
}
.recent-highlight-quotes .box-inner-content a {
  color: #fff;
  text-transform: uppercase;
  font-weight: 700;
  font-size: 11px;
}
.recent-highlight-quotes .box-with-border-inner {
  border: 1px solid var(--border);
  position: absolute;
  left: -20%;
  bottom: 0;
  width: 250px;
  height: 250px;
  z-index: -1;
}
.recent-highlight-quotes .box-inner-image {
  width: 160px;
  height: 160px;
  right: -20px;
  top: 90%;
  background-color: var(--primary-green);
  text-align: right;
  margin: -30px -20px 0 0;
  float: right;
}
.recent-highlight-quotes .box-inner-image img {
  object-fit: cover;
  width: 100%;
  height: 100%;
}
.section-title-one.style-pink h2:after {
  background-color: #f46060;
}
.page-template-template-expertise .service-item .image img {
  height: 230px;
}
.page-template-template-responsible-business .service-item .image img {
  height: 280px;
}
.container-one .container {
  max-width: 100%;
  width: 100%;
  padding: 0;
}
.page-template-template-about .recent-highlight-quotes {
  max-width: 450px;
  margin: auto;
}
.person-main-content-box {
  margin-bottom: 20px;
  padding-bottom: 15px;
}
.page-template-template-career-experienced-attorneys .expertise-section-1 {
  margin-top: 0;
  margin-bottom: 40px;
}
.culture-section .recent-highlight-quotes .box-inner-content {
  background-color: var(--primary-green-dark-light);
  color: var(--text-primary);
}
.business-card-box-content {
  padding: 20px;
}
.download-card-box-content {
  background-color: var(--primary-blue);
  color: #fff;
}
.page-template-template-privacy-policy #theme-main > .container-one {
  margin-top: -50px !important;
}
.page-template-template-privacy-policy.page-id-1233
  #theme-main
  > .container-one {
  margin-top: -185px !important;
}
.mobile-search {
  justify-content: start;
  align-items: start;
}
.main-search-ajax-loader {
  text-align: center;
  padding: 30px;
  color: #fff;
  font-size: 40px;
}
.bg-primary-green-dark-light,
.bg-primary-green-dark-light a {
  color: var(--text-primary) !important;
}
header.style-1 .main-nav > ul > li > ul.sub-menu > li:hover > ul.sub-menu {
  visibility: visible;
  opacity: 1;
  transform: translateY(0);
  left: 100%;
}
.bg-primary-green-dark-light {
  background-color: var(--primary-green-dark-light) !important;
}
.no-search-esult {
  font-size: 18px;
  color: #fff;
  padding: 20px 0;
}
.pt-30 {
  padding-top: 30px;
}
.mt-30 {
  margin-top: 30px;
}
.single-sector .team-section .container-one > div:nth-of-type(2) h4:before {
  content: none;
  display: none;
}
.business-card-section.new-style .business-card-box {
  width: auto;
  bottom: unset;
  left: unset;
  margin-top: 20px;
}
.new-style.business-card-section:before {
  top: 0;
  left: -50px;
  width: 300px;
  height: 300px;
}
.z-index-11 {
  z-index: 11;
}
.single-service .box-design-new-one {
  height: 350px;
}
.page-template-template-career-early-days .openday-section .openday-apply {
  margin-top: -130px;
  margin-bottom: 30px;
  margin-left: 0;
}
.page-template-template-career-recruitment-process
  .recent-highlight-quotes
  .box-with-border-inner {
  top: 80%;
  left: -40%;
  width: 350px;
}
.page-template-template-career .recent-highlight-quotes .box-with-border-inner {
  left: -5%;
  top: 50%;
}
.page-id-14797 .section-title-two h4.partner,
.page-template-template-career .footer-top-design-two,
.page-template-template-insights .banner-section + .container-one,
.page-template-template-insights .banner-section + .people-section,
.single-upc_news .section-title-two h4.partner {
  padding-top: 0;
}
.container-one.position-relative {
  height: 100%;
}
.container-one.people-meta-exists,
.page-id-6936 .banner-section + .people-section {
  padding-top: 70px;
}
.search-option-filters ul {
  display: flex;
  gap: 20px;
  list-style: none;
  padding: 0;
  margin-top: 15px;
}
.search-option-filters ul li input {
  width: auto;
}
.page-template-home.home
  .home-banner-section
  .swiper-pagination-bullets.swiper-pagination-horizontal {
  bottom: -80px;
}
.page-template-template-insights
  .home-banner-section
  .swiper-pagination-bullets.swiper-pagination-horizontal {
  bottom: -80px;
}
.mobile-search.slide {
  visibility: visible;
  overflow-x: hidden;
  overflow-y: auto;
}
.mobile-search .people-list ul::-webkit-scrollbar {
  -webkit-appearance: none;
  width: 7px;
}
.mobile-search .people-list ul::-webkit-scrollbar-thumb {
  border-radius: 4px;
  background-color: rgb(255 255 255 / 0.5);
  box-shadow: 0 0 1px rgb(255 255 255 / 0.5);
}
form.search-inpage-filter.filter-search-form input[type="submit"] {
  background: var(--text-primary);
  color: var(--white);
}
form.search-inpage-filter.filter-search-form input[type="submit"] + i {
  left: calc(50% - 10px);
  opacity: 1;
  color: #fff;
}
.page-id-651 .testimonial-section {
  padding-left: 0;
}
.recent-highlight-quotes.zn-1 {
  max-width: 600px;
}
ul {
  padding-left: 1rem;
}
ul li {
  margin-bottom: 3px !important;
}
.btn--primary-blue {
  margin-top: 1em !important;
}
.page-template-template-services .service-item .image img {
  height: 190px !important;
}
.page-template-template-locations .service-item .image img {
  height: 310px !important;
}
.sector-item-two,
.single-sector .sector-item {
  min-height: 220px;
}
.address-list li {
  font-size: 13px;
  font-weight: 400;
}
.banner-single-post-title H1 {
  font-weight: 200;
}
.recent-highlight H2 {
  font-size: 1em;
}
.culture-section .value-card ul li {
  font-size: 16px;
  padding-bottom: 14px;
}
.banner-content.style-dark h1.title-position-design,
.experience-box a {
  color: #fff !important;
}
.banner-section + .container-one,
.banner-section + .people-section {
  padding-top: 180px;
}
.banner-section + .container-one.people-meta-not-exists,
.banner-section + .people-section.people-meta-not-exists,
.single-ip_app_process .banner-section + .container-one {
  padding-top: 150px;
}
.btn--lg {
  text-transform: none;
}
.single-service .what-we-do-section UL LI {
  margin-bottom: 15px !important;
}
.page-template-home header.style-1 .main-nav > ul,
ul.language-list {
  padding-right: 40px;
}
.eligibility-card p {
  font-weight: 300;
}
.t1,
.t1 td,
.t1 th {
  border: 1px solid;
  font-size: 12px;
  padding-left: 10px;
}
.t1 th {
  color: #fff;
  background: #666;
}
.t1 td {
  padding-left: 4px;
}
.banner-single-post-title H1 {
  font-size: 3em;
}
.content-italic p {
  font-style: normal;
}
.team-item.style-two .team-content span {
  line-height: 1.4;
  font-weight: 400;
  font-size: 11px;
}
.sector-item .arrow-btn {
  bottom: 14px;
}
.page-id-636 .recent-highlight-quotes {
  margin-bottom: 120px !important;
}
.news-template-default.single.single-news
  .video-banner
  .banner-single-post-title {
  top: calc(100% - 210px);
}
.ht-cus-privary-policy {
  height: 30vh;
}
.choose-card-box {
  height: 220px;
}
.business-card-section .business-card-box {
  height: 110px;
  margin-top: -50px;
}
button.cky-btn.cky-btn-accept {
  background: #e30613 !important;
  border-color: #e30613 !important;
  color: #fff !important;
  margin-left: 10px;
}
button.cky-btn {
  color: #6c6b6b !important;
  font-size: 12px;
  padding: 2px 20px;
  border-color: #e30613 !important;
  font-weight: 400;
  border: 1px solid;
  border-radius: 0;
}
.cky-notice .cky-title,
.cky-notice-des p {
  color: #565662 !important;
}
.cky-notice-btn-wrapper {
  margin-top: 22px !important;
}
button.cky-btn.cky-btn-reject {
  border-color: #ddd !important;
}
.cky-notice .cky-title {
  font-size: 15px;
  margin-bottom: 4px;
}
.cky-consent-container .cky-consent-bar {
  border: 1px solid #d4d8df !important;
  border-radius: 0 !important;
}
.extra_link h3 {
  position: absolute;
  top: 45%;
  left: 3em;
}
.page-id-651 .testimonial-section::before,
.page-id-8268 .testimonial-section::before {
  border: none !important;
}
.recent-highlight-inner H2 {
  font-size: 1.1em;
}
.person-profile-pic-box {
  position: relative;
  margin-top: -200px;
  z-index: 999;
  height: 260px;
  width: 100%;
  top: 0;
}
.person-profile-img {
  float: right;
  max-width: 248px;
}
.person-profile-pic-box-phone {
  margin-top: -278px;
  display: none;
  margin-bottom: 20px;
  text-align: center;
}
.single-ip_app_process .sector-item {
  min-height: 210px;
}
.page-template-template-career-experienced-attorneys
  .attorny-left-image::before {
  top: -140px;
}
.page-template-template-career-experienced-attorneys .attorny-left-image {
  margin-top: 130px;
}
.single-people .banner-section {
  min-height: 780px;
}
.banner-single-post-title.no-author .banner-single-post-meta {
  left: auto;
  right: auto;
}
.banner-single-post-title {
  min-height: 300px;
  position: relative;
  max-width: 1140px;
  width: 100%;
  margin: -215px auto 0;
  top: inherit;
  left: inherit;
  right: inherit;
  bottom: inherit;
  z-index: 9;
}
.container-one.people-meta-exists.content-wrapper {
  margin-top: 110px !important;
  padding-top: 0;
}
.container-one.content-wrapper {
  margin-top: 40px !important;
  padding-top: 0;
}
.banner-section,
.single-ip_basics .banner-section {
  background-position: top center !important;
  background-position-y: 0px !important;
}
.page-template-template-insights .right-box-two.style-blue {
  height: 150px;
  transform: translate(60px, -80px);
}
.single-service .banner-section {
  background-position-y: -40px !important;
}
.page-template-template-privacy-policy .banner-single-post-title,
.service-template-default .banner-single-post-title,
.single-ip_app_process .banner-single-post-title,
.single-ip_basics .banner-single-post-title {
  margin-top: -65px;
  min-height: 230px;
}
.single-people .quat-box-wrap .quote-box {
  background-color: var(--primary-red);
  padding: 20px;
  width: 100%;
  max-width: 390px;
  color: #fff;
  font-weight: 700;
  min-height: auto;
  margin-top: -10px;
  margin-left: 142px;
  margin-bottom: 50px;
  position: relative;
  z-index: 9;
}
@media (max-width: 1399px) {
  .single-people .quat-box-wrap .quote-box {
    margin-left: 122px;
  }
}
@media (max-width: 1199px) {
  .single-people .quat-box-wrap .quote-box {
    margin-left: 70px;
  }
}
@media (max-width: 768px) {
  .single-people .quat-box-wrap .quote-box {
    margin-left: 0;
    margin-top: 20px;
  }
}
.pagination {
  justify-content: center;
}
.pagination ul {
  margin: 0;
  padding: 0;
  list-style: none;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 10px;
}
.pagination ul li {
  margin-bottom: 0 !important;
  display: inline-block;
  color: var(--text-primary);
  position: relative;
  padding-top: 10px;
  transition: 0.45s;
}
.service-template-default .what-we-do-section {
  background-color: var(--primary-yellow-light);
  z-index: 0;
}
.page-template-template-privacy-policy .job-post-name {
  color: var(--white);
  width: 100%;
  display: flex;
  align-items: center;
  padding: 20px;
  background-color: #f9b234;
  font-size: 22px;
}
.breadcrumps-wrap,
.language-list .de,
.postid-15525 .focus-list-area,
.postid-15526 .focus-list-area {
  display: none;
  visibility: hidden;
}
.page-template-template-privacy-policy .job-post-name span b {
  font-size: 14px !important;
}
.current-vacancies-banner {
  min-height: 620px;
  background-size: cover;
}
.page-template-template-upc .team-item .team-image img,
.page-template-template-upc .team-item.style-two .team-image {
  height: 180px;
}
.page-template-template-expertise.page-id-628 .service-item .image a img {
  zoom: unset;
  object-position: unset;
}
h1.env-banner-title {
  position: absolute;
  top: 350px;
  text-align: center;
  left: 50%;
  transform: translateX(-50%);
  width: 100%;
  font-weight: 300;
}
h1.env-banner-title b {
  font-weight: 300;
  font-size: 3.6rem;
}
.refunds-policy {
  background-color: #f2f2f2;
  padding: 70px 0;
}
.page-id-1233 .display-4,
.page-id-8260 .display-4,
.page-id-9078 .display-4 {
  font-size: 3.1em;
  color: var(--white);
  position: absolute;
  top: 255px;
  left: 50%;
  transform: translateX(-50%);
}
.page-template-template-privacy-policy.page-id-8260
  #theme-main
  > .container-one,
.page-template-template-privacy-policy.page-id-9078
  #theme-main
  > .container-one {
  margin-top: 0 !important;
}
.home.page-id-8263 .expertise-wrapper .expertise-image,
.home.page-id-8511 .expertise-wrapper .expertise-image {
  bottom: -165px;
}
.breadcrumps-wrap {
  margin-top: 15px;
  align-items: center;
}
.logo-area.only-for-mobile {
  padding-top: 10px;
}
.page-template-home-ger p {
  font-size: 15px;
  margin-bottom: 10px;
  line-height: unset;
}
.page-template-home-ger .banner-slider-content h1 {
  font-size: 3.2em;
  text-align: center;
}
.page-template-home-ger
  .home-banner-section
  .swiper-pagination-bullets.swiper-pagination-horizontal {
  height: 200px;
}
.page-id-14797 .working-image-box {
  height: 240px;
  margin-top: 55px;
  width: 100%;
}
.page-id-14797 .working-image-box .box-1 {
  left: 0 !important;
}
.page-id-14797 .about-us-quote-border-box {
  height: 278px;
  margin-top: 0;
}
.page-id-14797 .footer-address p,
.page-id-14797 .footer-bottom p {
  font-size: 11px;
}
.page-id-14797 .working-section.mt-200 {
  margin-top: 100px;
}
.page-template-home-ger .ip-expert-image-border-box .image-box,
.page-template-home-jp .ip-expert-image-border-box .image-box,
.page-template-home-kr .ip-expert-image-border-box .image-box {
  top: 42%;
}
.page-template-home-ger .trademark-iage-green-box {
  width: 100%;
  margin-top: 143px;
}
.page-template-home-ger .trademark-iage-green-box .border-box {
  top: -80px;
  width: 70%;
}
.page-template-home-ger .trademark-iage-green-box .image-box {
  width: 100%;
  top: 50px;
  right: unset;
}
.single-ip_app_process .banner-single-post-title .breadcrumps-wrap,
.single-ip_basics .banner-single-post-title .breadcrumps-wrap {
  display: flex;
  display: flex;
  visibility: visible;
  align-items: center;
  gap: 5px;
  flex-wrap: wrap;
}
.single-ip_app_process .banner-single-post-title .breadcrumps-wrap span a,
.single-ip_basics .banner-single-post-title .breadcrumps-wrap span a,
.single-patent_essential .banner-single-post-title .breadcrumps-wrap span a {
  color: var(--white);
  font-weight: 400;
  transition: 0.35s;
}
.single-patent_essential .banner-single-post-title .breadcrumps-wrap {
  display: flex;
  visibility: visible;
  align-items: center;
  gap: 5px;
  flex-wrap: wrap;
}
.single-bulletin.postid-15293 .people-list-bulletin .banner-auther,
.single-trending_topics .people-list-bulletin .banner-auther {
  gap: 0;
  flex-direction: column;
  align-items: flex-start;
}
.single-bulletin.postid-15293 .banner-auther .banner-auther-image,
.single-trending_topics .banner-auther .banner-auther-image {
  width: 100%;
  height: 140px;
}
.bulletin-people-wrap .people-list-bulletin {
  display: block;
  position: relative;
  top: unset;
  gap: 20px;
  flex-wrap: wrap;
}
.bulletin-people-wrap {
  margin-top: -40px;
  position: relative;
  z-index: 11;
}
.bulletin-people-wrap .people-list-bulletin .banner-auther {
  position: relative;
  top: unset;
  left: unset;
  display: block;
  gap: 20px;
}
.container-one.people-meta-exists.content-wrapper.bulletins {
  margin-top: 30px !important;
  padding-top: 0;
}
.single-bulletin .people-list-bulletin .banner-auther {
  bottom: unset;
}
.single-upc_news .people-list-bulletin .banner-auther {
  bottom: unset;
  position: relative;
  top: unset;
  left: unset;
  display: block;
}
.vacency-list-section {
  margin-top: -150px;
}
.vacency-list-section .job-post-name {
  color: var(--white);
  width: 100%;
  padding: 20px;
  background-color: #f9b234;
  font-size: 22px;
  min-height: 185px;
  position: relative;
}
.vacency-list-section .job-post-name a {
  color: var(--white);
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  margin-bottom: 50px;
}
.vacency-list-section .job-post-name a img {
  transition: 0.4s;
  padding-top: 15px;
  width: 28px;
  position: absolute;
  left: 20px;
  bottom: 20px;
}
.vacency-list-section .job-post-name a img:hover {
  transform: rotate(45deg);
}
.mobile-search .people-list ul li.sub-sector a {
  padding-left: 15px;
  position: relative;
}
.mobile-search .people-list ul li.sub-sector a::before {
  content: "";
  height: 1px;
  width: 5px;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  position: absolute;
  background-color: var(--white);
}
header.style-1 .flag-img {
  position: absolute;
  top: -27px;
  right: 4px;
  display: none;
}
.single-bulletin .content-box {
  border: 1px solid #000;
  padding: 15px;
  margin-bottom: 15px;
}
.business-card-section1 .business-card-box .business-card-box-content span {
  text-transform: lowercase;
}
.business-card-section1.sectors {
  display: flex;
  justify-content: center;
}
.business-card-section1.sectors .business-card-box {
  display: block !important;
  position: relative;
  left: unset;
  transform: unset;
  bottom: unset;
  margin-top: 0;
}
.single-footer-menu #theme-main .bg-light,
.single-ft #theme-main .bg-light {
  background-color: #fff !important;
}
.business-card-section1 .business-card-box {
  display: table-cell !important;
  position: absolute;
  border: 1px solid var(--border);
  bottom: -120px;
  left: -3%;
  max-width: 280px;
  width: 100%;
  height: 110px;
  margin-top: -50px;
}
.business-card-section1 .business-card-box .business-card-box-content {
  padding: 20px;
  display: flex;
  width: 100%;
  height: 100%;
  align-items: center;
}
.related-sectors-box a {
  text-decoration: none !important;
  color: #fff !important;
}
@media all and (max-width: 500px) {
  .business-card-section1 .business-card-box {
    position: relative;
    margin-top: 0;
    left: 0;
    bottom: 0;
  }
}
.pdf-flip-area {
  margin-top: 45px;
}
.pdf-flip-area h6 {
  font-weight: 700;
  margin-bottom: 5px;
  text-align: center;
}
.ecpertise-img-cont {
  border: 1px solid var(--border);
  padding: 20px;
  margin-top: 45px;
}
.ecpertise-img-cont.sectors h6 {
  margin-bottom: 10px;
  text-align: center;
  color: #000;
}
.ecpertise-img-cont.sectors h6 a {
  color: #0d6efd;
}
.sign-up-text p {
  font-size: 19px;
  color: #fff;
  font-weight: 600;
  line-height: 30px;
  padding: 20px;
  margin: 0;
}
.sign-up-text p a {
  text-decoration: underline;
  color: #fff;
}
.people-list-bulletin div .col {
  margin-right: 40px;
}
.single-trending_topics .people-list-bulletin div .col {
  margin-right: 0;
}
.single-vacancy H1 {
  display: none;
}
.single-vacancy .designation H1 {
  display: block;
}
.single-trending_topics .culture-content-2 {
  position: relative;
  margin: 0;
}
.single-trending_topics .pdf-flip-area {
  margin-top: 0;
}
.page-template-boult-renew .display-4 {
  font-size: 3.6em;
  font-weight: 300;
  position: absolute;
  top: 370px;
  width: 100%;
  left: 0;
  text-align: center;
}
.page-template-boult-renew .current-vacancies-banner {
  margin-bottom: 60px;
  background-size: cover;
  background-repeat: no-repeat;
  background-position-y: -170px !important;
}
.page-template-boult-renew .recent-highlight-quotes {
  max-width: 450px;
  margin: auto;
}
.page-template-boult-renew .recent-highlight-quotes h4 {
  font-size: 22px;
  font-weight: 400;
  color: #fff;
}
@media (max-width: 767px) {
  .page-template-boult-renew .recent-highlight-quotes {
    max-width: 100%;
  }
}
.page-template-template-career-trainee-placement .apply-card.style-yellow {
  display: none;
}
.page-template-template-career-trainee-placement .apply-area::before {
  display: none;
}
[lang="de-DE"] header.style-1 .container-one {
  max-width: 1230px;
}
[lang="es-ES"] .page-template-template-insights .right-box-two.style-blue {
  height: 187px;
}
/* .page-template-template-insights .banner-section {
  min-height: 780px;
}
.page-template-template-responsible-business .banner-section {
  min-height: 780px;
}
.page-template-template-career-experienced-attorneys .banner-section {
  min-height: 780px;
} */
