@media (min-width: 1910px) {
  /* .page-template-template-insights .banner-section {
    min-height: 780px;
  }
  .page-template-template-career-experienced-attorneys .banner-section {
    min-height: 780px;
  } */
  /* .page-template-template-responsible-business .banner-section {
    min-height: 780px;
  } */
  /* .banner-section {
    min-height: 590px;
  } */
  .current-vacancies-banner {
    min-height: 590px;
    background-size: cover;
  }
  /* .company-vdo {
    min-height: 780px;
  } */
  /* .page-template-template-expertise .banner-section {
    min-height: 590px;
  } */
  .single-people .banner-section {
    min-height: 590px;
    background-position-y: 22% !important;
  }
  .single-people .banner-section {
    background-position-y: 15% !important;
  }
}
@media (min-width: 2020px) {
  .single-people .banner-section {
    min-height: 550px;
    background-position-y: 14% !important;
  }
  .current-vacancies-banner {
    min-height: 550px;
    background-size: cover;
  }
  .banner-section {
    min-height: 550px;
  }
  .company-vdo {
    min-height: 550px;
  }
  .page-template-template-expertise .banner-section {
    min-height: 550px;
  }
}
@media (min-width: 2400px) {
  .page-id-1233 .display-4 {
    position: absolute;
    top: 500px;
    left: 50%;
    transform: translateX(-50%);
  }
  .page-id-8260 .display-4 {
    position: absolute;
    top: 500px;
    left: 50%;
    transform: translateX(-50%);
  }
  .page-id-9078 .display-4 {
    position: absolute;
    top: 500px;
    left: 50%;
    transform: translateX(-50%);
  }
  .single-people .banner-section {
    min-height: 1200px;
    background-position-y: 40% !important;
  }
  .page-template-template-vacancy .banner-section {
    min-height: 785px;
  }
  .banner-section {
    min-height: 1200px;
  }
  .company-vdo {
    min-height: 1200px;
  }
  .current-vacancies-banner {
    min-height: 1200px;
    background-size: cover;
  }
  h1.env-banner-title {
    top: 552px;
  }
  .page-template-template-expertise .banner-section {
    min-height: 1200px;
  }
  .banner-content.style-dark h1,
  .banner-content h1 {
    font-size: 4.6em;
  }
  .banner-content.style-dark h2,
  .banner-content h2 {
    font-size: 4.6em;
  }
  .company-vdo H3 {
    font-size: 4.6em;
  }
}
@media (min-width: 2800px) {
  .page-id-1233 .display-4 {
    position: absolute;
    top: 236px;
    left: 50%;
    transform: translateX(-50%);
  }
  .page-id-8260 .display-4 {
    position: absolute;
    top: 236px;
    left: 50%;
    transform: translateX(-50%);
  }
  .page-id-9078 .display-4 {
    position: absolute;
    top: 236px;
    left: 50%;
    transform: translateX(-50%);
  }
  .page-template-template-vacancy .banner-section {
    min-height: 785px;
  }
  .single-people .banner-section {
    min-height: 1600px;
    background-position-y: 200% !important;
  }
  .banner-section {
    min-height: 540px;
  }
  .current-vacancies-banner {
    min-height: 540px;
    background-size: cover;
  }
  .company-vdo {
    min-height: 1660px;
  }
  .page-template-template-expertise .banner-section {
    min-height: 1640px;
  }
  .banner-content.style-dark h1 {
    font-size: 5.6em;
  }
  .banner-content.style-dark h2 {
    font-size: 5.6em;
  }
  .company-vdo H3 {
    font-size: 5.6em;
  }
}
@media (min-width: 1400px) and (max-width: 1599px) {
  .home .expertise-wrapper .expertise-image {
    bottom: -165px;
  }
  .home-banner-section .swiper-pagination-bullets.swiper-pagination-horizontal {
    height: 250px;
  }
  .footer-top-design-three .footer-box::before {
    left: 65%;
  }
  .box-design-one::before {
    left: -15%;
  }
  .box-design-one::after {
    right: -15%;
  }
  .box-design-two::after {
    width: 330px;
  }
  .box-design-14::after {
    width: 130%;
  }
  .faq-area::before {
    right: -50px;
  }
  .testimonial-section .testi-top-image {
    max-width: 380px;
  }
  .choose-us-card {
    max-width: 540px;
  }
  .business-parnter-single::before {
    min-width: 470px;
  }
  .offer-card {
    min-width: 600px;
    margin-left: -162px;
  }
  .recruitment-image::after {
    min-width: 580px;
  }
  .culture-section .value-card::before {
    left: 65%;
  }
  .title-pb-150 {
    padding-bottom: 100px;
  }
}
@media (min-width: 992px) {
  header.style-1 .main-nav > ul.style-white li a {
    color: var(--white);
  }
  header.style-1 .main-nav > ul.style-white li:last-child::after {
    content: none;
  }
  header.style-1 .main-nav > ul.style-white li::after {
    content: "";
    background-color: var(--white);
  }
}
