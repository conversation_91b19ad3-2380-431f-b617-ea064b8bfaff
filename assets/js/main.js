(function ($) {
  "use strict";

  $(document).ready(function () {
    // Auto-click Cookie Notice
    $(window).on("load", function () {
      setTimeout(function () {
        $(".cky-notice-btn-wrapper .cky-btn-accept").trigger("click");
      }, 1000);
    });

    // Scroll-triggered animation
    $(window).scroll(function () {
      $(".animated-section").each(function () {
        var elementTop = $(this).offset().top;
        var elementBottom = elementTop + $(this).outerHeight();
        var viewportTop = $(window).scrollTop();
        var viewportBottom = viewportTop + $(window).height();
        if (elementBottom > viewportTop && elementTop < viewportBottom) {
          $(this).addClass("animated");
        } else {
          $(this).removeClass("animated");
        }
      });
    });

    // Mobile menu toggle
    $(".mobile-menu-btn").on("click", function () {
      $(".main-nav").addClass("show-menu");
    });
    $(".menu-close-btn").on("click", function () {
      $(".main-nav").removeClass("show-menu");
    });

    // Submenu toggle
    $(".main-nav .bi").on("click", function () {
      var $icon = $(this);
      $icon.parent().siblings().find(".sub-menu").slideUp();
      $icon.parent().siblings().find(".bi").addClass("bi-chevron-down");
      if ($icon.hasClass("bi-chevron-down")) {
        $icon.removeClass("bi-chevron-down").addClass("bi-chevron-up");
      } else {
        $icon.removeClass("bi-chevron-up").addClass("bi-chevron-down");
      }
      $icon.next(".sub-menu").slideToggle();
    });

    // Form submit scroll-to-top
    $(".submit").click(function (event) {
      event.preventDefault();
      var currentScroll = $(window).scrollTop();
      setTimeout(function () {
        $("html, body").animate({ scrollTop: 0 }, "slow");
      }, 3000);
    });

    // Mobile search open/close
    $(".search-btn").on("click", function () {
      $(".mobile-search form")[0]?.reset();
      $("#filtered-data").empty();
      $(".mobile-search").addClass("slide");
      $("body").addClass("modal-open");
    });
    $(".search-close-btn").on("click", function () {
      $(".mobile-search").removeClass("slide");
      $("body").removeClass("modal-open");
    });

    // Swiper sliders
    const eventBannerSlider = new Swiper(".event-banner-slider", {
      slidesPerView: 1,
      spaceBetween: 12,
      loop: true,
      speed: 1500,
      autoplay: { delay: 3000 },
      navigation: { nextEl: ".next-btn-121" },
      pagination: { el: ".swiper-pagination121", clickable: true },
    });

    const testimonialSlider = new Swiper(".testimonial-slider", {
      slidesPerView: 1,
      spaceBetween: 12,
      effect: "fade",
      fadeEffect: { crossFade: true },
      loop: true,
      speed: 1500,
      autoplay: { delay: 3000 },
      navigation: {
        nextEl: ".testimonial-next",
        prevEl: ".testimonial-prev",
      },
      pagination: { el: ".testimonial-pagination", clickable: true },
    });

    const bannerSlider = new Swiper(".banner-slider", {
      slidesPerView: 1,
      spaceBetween: 12,
      effect: "fade",
      fadeEffect: { crossFade: true },
      loop: true,
      speed: 1500,
      autoplay: { delay: 3000 },
      pagination: { el: ".banner-pagination", clickable: true },
    });

    const homeBannerSlider = new Swiper(".home-banner-slider", {
      slidesPerView: 1,
      spaceBetween: 0,
      pagination: { el: ".banner-pagination", clickable: true },
    });

    const gerBannerSlider = new Swiper(".ger-home-banner-slider", {
      slidesPerView: 1,
      spaceBetween: 0,
      speed: 800,
      pagination: { el: ".banner-pagination", clickable: true },
    });

    // Main banner with play/pause control
    const totalSlides = document.querySelectorAll(
      ".main-home-banner-slider .swiper-slide"
    ).length;

    const mainHomeBannerSwiper = new Swiper(".main-home-banner-slider", {
      slidesPerView: 1,
      spaceBetween: 0,
      loop: totalSlides > 1,
      speed: 800,
      autoplay: {
        delay: 3000,
        disableOnInteraction: false,
      },
      pagination: {
        el: ".banner-pagination",
        clickable: true,
      },
      navigation: {
        nextEl: ".next",
        prevEl: ".prev",
      },
    });

    if ($(".push-btn").length) {
      const pushBtn = document.querySelector(".push-btn i");
      let isPlaying = true;
      document
        .querySelector(".push-btn")
        .addEventListener("click", function () {
          if (isPlaying) {
            mainHomeBannerSwiper.autoplay.stop();
            pushBtn.classList.remove("bi-pause");
            pushBtn.classList.add("bi-play");
          } else {
            mainHomeBannerSwiper.autoplay.start();
            pushBtn.classList.remove("bi-play");
            pushBtn.classList.add("bi-pause");
          }
          isPlaying = !isPlaying;
        });
    }

    // Video popups
    if ($(".video-popup").length) {
      $(".video-popup").magnificPopup({ type: "iframe" });
    }

    // YouTube Backgrounds
    $("[data-vbg]").youtube_background?.();

    // Play icon click (video)
    if ($(".video-popupp.play-icon").length) {
      $(document).on("click", ".video-popupp.play-icon", function (e) {
        e.preventDefault();
        $(this).closest(".company-vdo").find("button.play-toggle").trigger("click");
        $(this).hide();
        $(this).closest(".company-vdo").find("h3").fadeOut("fast");
      });
    }

    // Odometer counter with character span
    if ($(".counter-single").length && $.fn.isInViewport) {
      $(".counter-single").each(function () {
        $(this).isInViewport(function (status) {
          if (status === "entered") {
            $(".odometer").each(function (i, el) {
              const value = el.getAttribute("data-odometer-final");
              let formatted = "";
              value.split("").forEach(function (ch, i) {
                formatted += `<span class="letter letter-${i}">${ch}</span>`;
              });
              el.innerHTML = formatted;
            });
          }
        });
      });
    }

    // Image error fallback
    $("img").on("error", function () {
      $(this).hide(); // Or replace with placeholder
    });

    // AJAX Search Logger
    const searchInput = document.getElementById("search-input");
    const searchButton = document.getElementById("search-btnn");

    if (searchInput && searchButton) {
      function logSearchData() {
        const keyword = searchInput.value.trim();
        if (keyword === "") return;
        const actionFrom = "popup";
        const searchType = "all";
        $.ajax({
          url: window.ajaxurl, // Ensure ajaxurl is defined in template
          method: "POST",
          data: {
            action: "get_main_search_data",
            keyword,
            type: searchType,
            action_from: actionFrom,
          },
          success: function (response) {
            console.log("Search data logged successfully:", response);
          },
          error: function (error) {
            console.log("Error logging search data:", error);
          },
        });
      }

      searchInput.addEventListener("keydown", function (event) {
        if (event.key === "Enter") {
          logSearchData();
        }
      });

      searchButton.addEventListener("click", function () {
        logSearchData();
      });
    }
  });
})(jQuery);
