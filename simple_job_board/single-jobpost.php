<?php
/**
 * The Template for displaying job details
 *
 * Override this template by copying it to yourtheme/simple_job_board/single-jobpost.php
 *
 * <AUTHOR>
 * @package     Simple_Job_Board
 * @subpackage  Simple_Job_Board/Templates
 * @version     1.1.0
 * @since       2.2.0
 * @since       2.2.3   Enqueued Front Styles & Revised the HTML structure.
 * @since       2.2.4   Enqueued Front end Scripts.
 * @since       2.3.0   Added "sjb_archive_template" filter.
 */
get_header();

?>

    <!-- ========== banner-section start============= -->

    <div class="banner-section d-flex flex-column align-items-staer justify-content-center"
        style="background-image:url('<?php echo get_the_post_thumbnail_url(get_the_ID(), 'full'); ?>')">
        <div class="container text-center">
            <div class="banner-content">
                <h1 data-aos="fade-down"><?php echo get_the_title(); ?></h1>
            </div>
        </div>
    </div>

    <!-- ========== banner-section end============= -->

    <style type="text/css">
        .sjb-page .sjb-detail {
            padding-top: 0;
        }
    </style>

    <div class="team-section pt-n-100">
        <div class="container-one">
            <?php echo do_shortcode('[job_details]'); ?>
        </div>
    </div>

<?php 

get_footer();