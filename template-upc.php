<?php 
/*
    Template Name: UPC Page
*/

// Exit if accessed directly.
defined( 'ABSPATH' ) || exit;

get_header();
$pll = pll_current_language();
?>

<!-- ========== banner-section start============= -->
<?php 
    $banner_title = get_field('banner_title');
?>
<div class="banner-section d-flex flex-column align-items-staer justify-content-center position-relative"
    style="background-image: url('<?php echo get_the_post_thumbnail_url(get_the_ID(), 'full'); ?>')">
    <div class="inner-overlay"></div>
    <!-- style="background-image:linear-gradient(to bottom, rgba(255,255,255,0.05) 0%, rgba(0,0,0,0.15) 50%), radial-gradient(at top center, rgba(255,255,255,0.20) 50%, rgba(0,0,0,0.40) 190%), url('<?php echo get_the_post_thumbnail_url(get_the_ID(), 'full'); ?>')"> -->
    <!-- style="background-image:linear-gradient(to bottom, rgba(255,255,255,0.05) 0%, rgba(0,0,0,0.15) 100%), radial-gradient(at top center, rgba(255,255,255,0.40) 0%, rgba(0,0,0,0.40) 120%), url('<?php echo get_the_post_thumbnail_url(get_the_ID(), 'full'); ?>')"> -->
    <div class="container text-center">
        <div class="banner-content">
            <h1 ><?php echo $banner_title;?></h1>
        </div>
    </div>
</div>

<!-- ========== banner-section end============= -->

<!-- ========== software-section start============= -->
<?php 
    $overview = get_field('overview');
?>
<div class="content-section pt-100 pl-container">
    <div class="container-fluid">
        <div class="row">
            <div class="col-lg-5">
                <div class="section-title-one style-yellow">
                    <h2 ><?php echo $overview['title']; ?></h2>
                </div>
                <div class="content" >
                    <?php echo $overview['content']; ?>
                </div>
            </div>
            <div class="col-lg-7">
                <div class="box-design-11"> </div>
                <div class="faq-area">
                    <div class="section-title-four">
                        <h4 ><?php echo __('FAQs:', 'picostrap5-child-base'); ?></h4>
                    </div>
                    <div class="faq-wrap">
                        <?php 
                            if(!empty($overview['faq_list'])){
                                $i = 1; 
                                foreach($overview['faq_list'] as $value){
                                    ?>
                                    <div class="faq-item" >
                                        <h5 id="heading<?php echo $i; ?>" class="accordion-button collapsed" data-bs-toggle="collapse"
                                            data-bs-target="#collapse<?php echo $i; ?>" aria-controls="collapse<?php echo $i; ?>">
                                            <?php echo $value['title']; ?>
                                        </h5>
                                        <div id="collapse<?php echo $i; ?>" class="accordion-collapse collapse " aria-labelledby="heading<?php echo $i; ?>">
                                            <div class="faq-body">
                                                <?php echo $value['content']; ?>
                                            </div>
                                        </div>
                                    </div>
                                    <?php 
                                    $i++;
                                }
                            }
                        ?>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- ========== software-section end============= -->

<!-- ========== software-section start============= -->
<?php 
    $first_card_box = get_field('first_card_box'); 
    if(!empty($first_card_box)) :
?>
<div class="details-card-section pt-100 position-relative">
    <div class="box-design-12"></div>
    <div class="container-one">
        <div class="section-title-one style-yellow">
            <h2 ><?php echo __('UPC insights', 'picostrap5-child-base'); ?></h2>
        </div>
        <div class="row justify-content-start g-md-4 g-2">
            <?php foreach($first_card_box as $value) : ?>
            <div class="col-lg-3 col-md-6 col-sm-6 col-12" >
                    <a hreflang="<?php echo esc_attr($pll); ?>" target="_blank" href="<?php echo $value['link']['url']; ?>">
                <div class="sector-item style-yellow">
                        <h6 class="title"><?php echo $value['title']; ?></h6>
                        <p><?php echo $value['content']; ?></p>
                        <div class="arrow-btn">
                            <img src="<?php echo get_stylesheet_directory_uri();?>/assets/images/icons/arrow-white.svg" alt="arrow">
                        </div>
                </div>
                    </a>
            </div>
            <?php endforeach; ?>
        </div>
    </div>
</div>
<?php endif; ?>
<!-- ========== software-section start============= -->

<!-- ========== ai-guide-section start============= -->
<?php 
    $patent_area = get_field('patent_area');
    if(!empty($patent_area['patent_image']) && !empty($patent_area['title'])) :
?>
<div class="ai-guide-section mt-60 pb-100">
    <div class="container-one">
        <div class="section-title-three">
            <h5 ><?php echo $patent_area['title']; ?></h5>
        </div>
        <div class="row row-cols-xl-6 row-cols-lg-5 row-cols-md-4 row-cols-sm-2 row-cols-2 g-lg-4 g-3">
            <?php 
                if(!empty($patent_area['title'])){
                    foreach($patent_area['patent_image'] as $value){
                        ?>
                        <div class="col" >
                            <div class="guide-single">
                                <a hreflang="<?php echo esc_attr($pll); ?>" href="<?php echo $value['pdf_link']; ?>">
                                    <img src="<?php echo $value['image']; ?>" alt="image">
                                </a>
                            </div>
                        </div>
                        <?php 
                    }
                }
            ?>
        </div>
    </div>
</div>
<?php endif; ?>

<!-- ========== team section start ============= -->
<?php 
    $the_team = get_field('the_team');
?>
<div class="team-section position-relative mt-100 <?php echo empty($the_team['select_partners']) && empty($the_team['senior_associates']) ? 'd-none' : ''; ?>">
    <div class="box-design-14">
    </div>
    <div class="container-one">
        <div class="section-title-one style-green">
            <h2><?php echo $the_team['title']; ?></h2>
        </div>
        <div class="section-title-two <?php echo !empty($the_team['select_partners']) ? '' : 'd-none'; ?>">
            <h4><?php echo __('Partners', 'picostrap5-child-base'); ?></h4>
        </div>

        <div class="row row-cols-xl-4 row-cols-lg-4 row-cols-md-3 row-cols-sm-2 row-cols-2 g-lg-4 g-3 mb-30 <?php echo !empty($the_team['select_partners']) ? '' : 'd-none'; ?>">
            <?php 
            if(!empty($the_team['select_partners'])) :
                foreach($the_team['select_partners'] as $value) :
                ?>
                <div class="col">
                    <div class="team-item style-two style-two">
                        <?php 
                            $thumImg = '';
                            $upc_image = get_field('upc_image', $value->ID); 
                            $thum_img = get_field('person_photo_2', $value->ID);
                            if(!empty($upc_image)){
                                $thumImg = $upc_image;
                            }else{
                                $thumImg = $thum_img['url']; 
                            }
                            
                           
                        ?>
                        <div class="team-image">
                            <a hreflang="<?php echo esc_attr($pll); ?>" href="<?php echo get_the_permalink($value->ID); ?>">
                                <?php  if(!empty($thumImg)) : ?>
                                    <img src="<?php echo $thumImg; ?>" alt="image">
                                <?php endif; ?>
                            </a>
                        </div>
                      
                        <?php 
                            $role =  get_the_terms( $value->ID, 'people_role' );
                            $location =  get_the_terms( $value->ID, 'location' );
                            
                        ?>
                        <div class="team-content">
                            <h6><a hreflang="<?php echo esc_attr($pll); ?>" href="<?php echo get_the_permalink($value->ID); ?>"><?php echo get_the_title($value->ID); ?></a></h6>
                            <span>
                                <?php 
                                    if(!empty($role)){
                                        echo $role[0]->name;
                                    }
                                ?>
                            </span>
                            <span>
                                <?php 
                                    if(!empty($location)){
                                        echo $location[0]->name;
                                    }
                                ?>
                            </span>
                        </div>
                    </div>
                </div>
                <?php 
                endforeach; 
            endif;
            ?>
        </div>

        <div class="section-title-two <?php echo !empty($the_team['senior_associates']) ? '' : 'd-none'; ?>">
            <h4><?php echo __('Senior associates', 'picostrap5-child-base'); ?></h4>
        </div>
        <div class="row row-cols-xl-4 row-cols-lg-4 row-cols-md-3 row-cols-sm-2 row-cols-2 g-lg-4 g-3 mb-30 <?php echo !empty($the_team['senior_associates']) ? '' : 'd-none'; ?>">
            <?php 
            if(!empty($the_team['senior_associates'])) :
                foreach($the_team['senior_associates'] as $value) :
                ?>
                <div class="col">
                    <div class="team-item style-two style-two">
                        <?php 
                            $thum_img = get_field('person_photo_2', $value->ID);
                        ?>
                        <div class="team-image">
                            <a hreflang="<?php echo esc_attr($pll); ?>" href="<?php echo get_the_permalink($value->ID); ?>">
                                <?php  if(!empty($thum_img)) : ?>
                                    <img src="<?php echo $thum_img['url']; ?>" alt="image">
                                <?php endif; ?>
                            </a>
                        </div>
                        <?php 
                            $role =  get_the_terms( $value->ID, 'people_role' );
                            $location =  get_the_terms( $value->ID, 'location' );
                            
                        ?>
                        <div class="team-content">
                            <h6><a hreflang="<?php echo esc_attr($pll); ?>" href="<?php echo get_the_permalink($value->ID); ?>"><?php echo get_the_title($value->ID); ?></a></h6>
                            <span>
                                <?php 
                                    if(!empty($role)){
                                        echo $role[0]->name;
                                    }
                                ?>
                            </span>
                            <span>
                                <?php 
                                    if(!empty($location)){
                                        echo $location[0]->name;
                                    }
                                ?>
                            </span>
                        </div>
                    </div>
                </div>
                <?php 
                endforeach; 
            endif;
            ?>
        </div>
        <div class="section-title-two <?php echo !empty($the_team['attorneys']) ? '' : 'd-none'; ?>">
            <h4><?php echo __('Attorneys', 'picostrap5-child-base'); ?></h4>
        </div>
        <div class="row row-cols-xl-4 row-cols-lg-4 row-cols-md-3 row-cols-sm-2 row-cols-2 g-lg-4 g-3 mb-60 <?php echo !empty($the_team['attorneys']) ? '' : 'd-none'; ?>">
            <?php 
            if(!empty($the_team['attorneys'])) :
                foreach($the_team['attorneys'] as $value) :
                ?>
                <div class="col">
                    <div class="team-item style-two style-two">
                        <?php 
                            $thum_img = get_field('person_photo_2', $value->ID);
                        ?>
                        <div class="team-image">
                            <a hreflang="<?php echo esc_attr($pll); ?>" href="<?php echo get_the_permalink($value->ID); ?>">
                                <?php  if(!empty($thum_img)) : ?>
                                    <img src="<?php echo $thum_img['url']; ?>" alt="image">
                                <?php endif; ?>
                            </a>
                        </div>
                        <?php 
                            $role =  get_the_terms( $value->ID, 'people_role' );
                            $location =  get_the_terms( $value->ID, 'location' );
                            
                        ?>
                        <div class="team-content">
                            <h6><a hreflang="<?php echo esc_attr($pll); ?>" href="<?php echo get_the_permalink($value->ID); ?>"><?php echo get_the_title($value->ID); ?></a></h6>
                            <span>
                                <?php 
                                    if(!empty($role)){
                                        echo $role[0]->name;
                                    }
                                ?>
                            </span>
                            <span>
                                <?php 
                                    if(!empty($location)){
                                        echo $location[0]->name;
                                    }
                                ?>
                            </span>
                        </div>
                    </div>
                </div>
                <?php 
                endforeach; 
            endif;
            ?>
        </div>
    </div>
</div>

<!-- ========== team section end ============= -->

<!-- ========== insights section start ============= -->

<div class="insights-section pt-50 position-relative">
    <div class="box-design-13"></div>
    <div class="container-one">
        <div class="section-title-one style-green">
            <h2 ><?php echo __('UPC updates', 'picostrap5-child-base'); ?></h2>
        </div>
        <div class="row justify-content-center mb-40 g-lg-4 g-3">
            <?php 
                    // $args = array(
                    //     'post_type' => 'bulletin',
                    //     'post_type' => 'upc_news',
                    //     'posts_per_page' => 4,
                    //     'orderby' => 'date',
                    //     'order' => 'DESC',
                    //     'post_status' => 'publish',
                        
                    // );
                    $args = [
                        'post_type' => ['upc_news', 'bulletin', 'news'],
                        'posts_per_page' => '4',
                        'post_status' => 'publish',
                        'orderby' => 'date',
                        'order' => 'desc',
                        'meta_query' => [
                            'relation' => 'OR',
                            [
                                'key' => 'include_in_upc_list',
                                'value' => '1',
                                'compare' => 'LIKE',
                            ],
                            [
                                'key' => 'include_in_upc_list',
                                'compare' => 'NOT EXISTS',
                            ],
                        ],
                    ];
                    
                    $query = new WP_Query( $args );
                    
                    if ( $query->have_posts() ) {
                        while ( $query->have_posts() ) {
                            $query->the_post();
                            $permalink = '';
                            if($pll == 'es'){
                                $permalink = str_replace("/es/","/", get_the_permalink());
                            }elseif($pll == 'de'){
                                $permalink = str_replace("/de/","/", get_the_permalink());
                            }else{
                                $permalink = get_the_permalink();
                            }
                            ?>
                            <div class="col-lg-3 col-md-6 col-12" >
                                <div class="insight-item style-green">
                                    <?php if(!empty(get_the_post_thumbnail_url())) :?>
                                    <div class="image">
                                       <a href="<?php echo $permalink; ?>"><img src="<?php echo get_the_post_thumbnail_url(get_the_ID(), 'full'); ?>" alt="image"></a> 
                                    </div>
                                    <?php endif; ?>
                                    <div class="content">
                                        <h6><a hreflang="<?php echo esc_attr($pll); ?>" href="<?php echo $permalink; ?>"><?php echo get_the_title(); ?></a></h6>
                                        <p><?php echo get_the_excerpt(); ?></p>
                                        <a hreflang="<?php echo esc_attr($pll); ?>" href="<?php echo $permalink; ?>"><?php echo __('MORE', 'picostrap5-child-base'); ?></a>
                                    </div>
                                </div>
                            </div>
                            <?php 
                        }
                    }
                        wp_reset_postdata();
            ?>
        </div>
        <a  hreflang="<?php echo esc_attr($pll); ?>" href="<?php echo home_url('/upc-insight/'); ?>" class="eg-btn btn--primary-green btn--lg2"><?php echo __('MORE', 'picostrap5-child-base'); ?><i
                class="bi bi-arrow-right"></i></a>
    </div>
</div>


<div class="maps-data-section pt-100 position-relative d-none">
    <div class="box-design-13"></div>
    <div class="container-one">
        <div class="section-title-one style-green">
            <h2 ><?php // echo __('Maps', 'picostrap5-child-base'); ?></h2>
        </div>
        <div class="row">
            <div class="col-lg-12">
                <?php echo get_field('maps_data'); ?>
            </div>
        </div>
    </div>
</div>

<!-- ========== insights section end ============= -->

        <!-- ========== search section start ============= -->

        <div class="search-section mt-100">
            <div class="container-one">
                <div class="search-block" >
                    <input type="text" placeholder="<?php echo __('SIGN UP TO OUR UPDATES', 'picostrap5-child-base'); ?>">
                </div>
            </div>
        </div>
    
        <!-- ========== search section start ============= -->

    <!-- ========== contact-section start ============= -->

    <div class="contact-section pt-100">
        <div class="container-one">
            <div class="section-title-one style-green">
                <h2 ><?php echo __('UPC enquiries', 'picostrap5-child-base'); ?></h2>
            </div>
        </div>
        <div class="container-fluid bg-green-light pt-40 pb-40">
            <div class="container-one" >
                <?php // echo do_shortcode('[contact-form-7 id="822" title="UPC enquiries"]')?>
                <?php // echo do_shortcode('[contact-form-7 id="'. pll_get_post(822, pll_default_language()) .'" title="UPC enquiries"]')?>
                <?php echo do_shortcode('[contact-form-7 id="'. pll_get_post(822) .'" title="UPC enquiries"]')?>
            </div>
        </div>
    </div>

    <!-- ========== contact-section start ============= -->


<?php get_footer();
