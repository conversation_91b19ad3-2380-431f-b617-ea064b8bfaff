<?php 
/*
    Template Name: Services Page
*/

// Exit if accessed directly.
defined( 'ABSPATH' ) || exit;
$pll = pll_current_language();
get_header();


$view_service = isset($_GET['view-service']) ? $_GET['view-service'] : false;
$view_service_id = false;
$post_content = false;
if($view_service){
    $args = array(
      'name'        => $view_service,
      'post_type'   => 'service',
      'post_status' => 'publish',
      'numberposts' => 1
    );
    $my_posts = get_posts($args);
    if($my_posts){
        $view_service_id = $my_posts[0]->ID;
        $post_content = $my_posts[0]->post_content;
    }
}



?>


<!-- ========== banner-section start============= -->

    <div class="banner-section d-flex flex-column align-items-staer justify-content-center position-relative"
        style="background-image: url('<?php echo $view_service_id ? get_the_post_thumbnail_url($view_service_id, 'full') : get_field('banner_image')['url']; ?>')">
        <div class="inner-overlay"></div>
        <!-- style="background-image:linear-gradient(to bottom, rgba(255,255,255,0.05) 0%, rgba(0,0,0,0.15) 50%), radial-gradient(at top center, rgba(255,255,255,0.20) 50%, rgba(0,0,0,0.40) 190%), url('<?php echo $view_service_id ? get_the_post_thumbnail_url($view_service_id, 'full') : get_field('banner_image')['url']; ?>')"> -->
        <!-- style="background-image:linear-gradient(to bottom, rgba(255,255,255,0.05) 0%, rgba(0,0,0,0.15) 100%), radial-gradient(at top center, rgba(255,255,255,0.40) 0%, rgba(0,0,0,0.40) 120%), url('<?php echo $view_service_id ? get_the_post_thumbnail_url($view_service_id, 'full') : get_field('banner_image')['url']; ?>')"> -->
        <div class="container position-relative">
            
        </div>
    </div>
    <div class="banner-content style-dark banner-single-post-title title-bg-yellow">
        <div class="container-one">
            <h1 class="title-position-design">
                <?php echo get_field('banner_title'); ?><?php echo $view_service_id ? ': ' . get_the_title($view_service_id) : ''; ?>

            </h1>
        </div>
       
    </div>
    <!-- ========== banner-section end============= -->

    <!-- ========== people details section start ============= -->

    <div class="people-section pt-60 pb-60">
        <div class="container-one">
            <div class="row">
                <div class="col-lg-6">
                    <div class="section-title-one style-yellow">
                        <h2><?php echo $view_service_id ? 'Services: ' . get_the_title($view_service_id) : get_field('after_banner_title');; ?></h2>
                        <div>
                            <?php 
                                if($view_service_id){
                                    $content = $post_content;
                                    // $content = apply_filters('the_content', $content);
                                    // $content = str_replace(']]>', ']]&gt;', $content);
                                    echo do_shortcode($content);
                                }
                                else{
                                    echo get_field('after_banner_content');
                                }
                            ?>
                        </div>
                    </div>
                </div>
                <div class="col-lg-6">
                    <!-- <div class="right-box-two"> -->

                    <!-- </div> -->
                </div>
            </div>
        </div>
    </div>

    <!-- ========== people details section end ============= -->

    <div class="what-we-do-section pt-100 position-relative mb-50 pb-100">
        <div class="container-one">
            <div class="section-title-one style-yellow">
                <h2><?php echo __('What we do', 'picostrap5-child-base'); ?></h2>
            </div>
            <div class="row">
                <?php $what_we_do_content = get_field('what_we_do_content'); ?>
                <div class="col-sm-4"><?php echo $what_we_do_content['column_1']; ?></div>
                <div class="col-sm-4"><?php echo $what_we_do_content['column_2']; ?></div>
                <div class="col-sm-4"><?php echo $what_we_do_content['column_3']; ?></div>
            </div>
        </div>
    </div>

    <!-- ========== service section start ============= -->

    <div class="service-section-2 pt-100 position-relative mb-50">
        <!-- <div class="box-design-four"></div> -->
        <div class="container-one">
            <div class="section-title-one style-blue">
                <h2><?php echo __('Services', 'picostrap5-child-base'); ?></h2>
            </div>
            <div class="row justify-content-center gy-4">

                <?php 
                    $args = array(
                        'post_type' => 'service',
                        'post_status' => 'publish',
                        'posts_per_page' => '-1',
                        'orderby' => 'date',
                        'order' => 'DESC',
                    );

                    $query = new WP_Query($args);

                    if($query->have_posts()){
                        while($query->have_posts()){
                            $query->the_post();

                            ?>
                                <div class="col-lg-3 col-md-6 col-sm-6 col-6">
                                    <div class="service-item">
                                        <div class="image">
                                            <img src="<?php echo get_the_post_thumbnail_url(get_the_ID(), 'full'); ?>" alt="image">
                                        </div>
                                        <div class="content">
                                            <h6><a hreflang="<?php echo esc_attr($pll); ?>" class="more-service-link" data-id="<?php echo get_the_ID(); ?>" href="<?php echo get_the_permalink(); ?>"><?php echo get_the_title(); ?></a></h6>
                                            <p><?php echo get_the_excerpt(); ?></p>
                                            <a hreflang="<?php echo esc_attr($pll); ?>" class="more-service-link" data-id="<?php echo get_the_ID(); ?>" href="<?php echo get_the_permalink(); ?>"><!-- <i class='bx bx-plus'></i> -->MORE</a>
                                        </div>
                                    </div>
                                </div>
                            <?php 
                        }
                    }
                    wp_reset_postdata();
                ?>

            </div>
            <div class="box-design-new-one"></div>
        </div>
        
    </div>

    <!-- ========== service section end ============= -->

    <div class="service-details-section position-relative pt-100 d-none">
        <div class="box-design-five"></div>
        <div class="container-fluid">
            <div class="row justify-content-end">
                <div class="col-lg-8">
                    <?php 
                        $first = true;
                        if($query->have_posts()){
                            while($query->have_posts()){
                                $query->the_post();

                                ?>
                                    <div class="service-details-wrap service-details-data service-details-<?php echo get_the_ID(); ?>" style="display: <?php echo $first ? 'block' : 'none'; ?>;">
                                        <div class="section-title-one style-white">
                                            <h2><?php echo get_the_title(); ?></h2>
                                        </div>
                                        <!-- <h6><?php // echo get_the_excerpt(); ?></h6> -->
                                        <div>
                                            <?php the_content(); ?>
                                        </div>
                                        <!-- <a href="#" class="eg-btn btn--primary-black btn--lg2 mt-50">CLOSE</a> -->
                                    </div>
                                <?php 
                                $first = false;
                            }
                        }
                        wp_reset_postdata();
                    ?>
                    
                </div>
            </div>
        </div>
    </div>

    <script type="text/javascript">
        jQuery(function($){
            $(document).on('click', '.more-service-link', function(e){
                return;
                e.preventDefault();

                var this_link = $(this);
                var this_id = this_link.data('id');

                $(document).find('.service-details-data').fadeOut('fast');
                $(document).find('.service-details-data.service-details-' + this_id).fadeIn('slow');

                $([document.documentElement, document.body]).animate({
                    scrollTop: $(document).find('.service-details-section').offset().top
                }, 500);
            });
        });
    </script>


<?php get_footer();