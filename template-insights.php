<?php
/*
Template Name: Insights
*/

// Exit if accessed directly.
defined('ABSPATH') || exit;

get_header();

$pll = pll_current_language();

?>
<style>
    .banner-slider-content{
        min-height: 100vh;
    }
    @media (max-width: 991px) {
        .insight-item .image img {
            width: 100%;
        }
    }

    .filter-search-area {
        background-color: transparent;
    }

    form.filter-search-form select {
        border: 1px solid #333;
    }

    #insights-filter-search-form .author-dropdown {
        position: absolute;
        background: #fff;
        width: calc(100% - 16px);
        z-index: 999;
        max-height: 200px;
        overflow-y: scroll;
        border: 1px solid #eee;
        /* margin-top: 10px; */
        padding-top: 10px;
    }

    .author-dropdown ul {
        list-style: none;
        padding-left: 20px;
        margin: 0;
    }

    .author-dropdown ul li {
        padding-bottom: 10px !important;
        cursor: pointer;
        font-size: 14px;
        font-weight: 400;
    }

    .author-dropdown span {
        display: flex;
        justify-content: start;
        align-items: center;
        margin: 0 0 10px 5px;
    }

    .section-title-one.results h2 {
        font-size: 22px;
        font-weight: 500;
        padding-bottom: 10px;
    }

    /* .insight-title {
        text-transform: capitalize !important;
    } */

    .insight-item-new .image {
        position: relative;
    }

    .insight-item-new .image h6 {
        position: absolute;
        left: 0;
        top: 0;
        margin-bottom: 0;
    }

    .insight-item-new .image h6 a {
        font-size: 13px;
        font-weight: 600;
        color: #ffffff;
        display: block;
        padding: 5px 12px;
        border-radius: 0 100px 100px 0;
        background: var(--primary-blue);
    }

    form.filter-search-form select:disabled {
        border-color: #ccc;
        color: #ccc;
        background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='rgb(204, 204, 204)' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'/%3e%3c/svg%3e");
        cursor: not-allowed;
    }
</style>

<script type="text/javascript">
    jQuery(document).ready(function($) {
        const $insightType = $('select[name="insight_typee"]');
        const $nameField = $('select[name="name"]');
        const $serviceField = $('select[name="service"]');
        const $sectorField = $('select[name="sector"]');

        function toggleFields() {
            const selectedVal = $insightType.val();
            const disable = selectedVal === 'news-post';

            $nameField.prop('disabled', disable);
            $serviceField.prop('disabled', disable);
            $sectorField.prop('disabled', disable);
        }

        // Run on load
        toggleFields();

        // Run on change
        $insightType.on('change', toggleFields);
    });



    jQuery(function($) {
        let debounceTimer;

        $(document).on('change', '#insights-filter-search-form select[name="name"]', function(e) {
            const author_id = $(this).val();
            const author_name = $(this).find("option:selected").text();
            $('input[name="author_id"]').val(author_id);
            $('input[name="name"]').val(author_name);
            setTimeout(() => {
                $(this).closest('form').trigger('submit');
            }, 100);
        });

        $(document).on('change', '#insights-filter-search-form select[name="insight_typee"], #insights-filter-search-form select[name="service"], #insights-filter-search-form select[name="sector"]', function(e) {
            $(this).closest('form').trigger('submit');
        });

        $(document).on('submit', '#insights-filter-search-form', function(e) {
            e.preventDefault();
            var this_form = $(this);
            var this_data = this_form.serialize();

            $.ajax({
                url: '<?php echo admin_url("admin-ajax.php"); ?>',
                method: 'POST',
                dataType: 'json',
                data: this_data,
                success: function(response) {
                    if (response.success && response?.data?.results) {
                        const results = response.data.results;
                        const hasBulletins = results.bulletins.length > 0;

                        let html = `<div class="insights-section mt-60 position-relative">
                        <div class="box-design-seven"></div>
                        <div class="container-one">`;

                        if (hasBulletins) {
                            html += `<div class="section-title-one results style-green">
                                </div>
                                <div class="section-title-one style-black">
                                    <h2>Results</h2>
                                </div>
                                <div class="row g-lg-4 g-3">`;

                            results.bulletins.forEach(function(item) {
                                html += `
                                <div class="col-lg-3 col-md-6 col-12">
                                    <div class="insight-item insight-item-new style-black">
                                        <div class="image">
                                            <img src="${item.thumbnail}" alt="image">
                                            <h6><a class="insight-title" href="${item.permalink}">${item.post_type}</a></h6>
                                        </div>
                                        <div class="content">
                                            <h6><a class="insight-title" href="${item.permalink}">${item.title}</a></h6>
                                                <p>${item.excerpt}</p>
                                        </div>
                                    </div>
                                </div>`;
                            });

                            html += `</div>`;
                        } else {
                            html += `<p>No results found.</p>`;
                        }

                        html += `</div></div>`; // Close container-one and insights-section

                        $('#insights-data-wrapper').html(html);
                    } else {
                        $('#insights-data-wrapper').html(`
                        <div class="insights-section pt-100 position-relative">
                            <div class="box-design-seven"></div>
                            <div class="container-one">
                                <p>No results found.</p>
                            </div>
                        </div>
                    `);
                    }
                },
                error: function(xhr, status, error) {
                    console.error('AJAX Error:', status, error, xhr.responseText);
                }
            });
        });
    });
</script>


<style>
    .right-box-two.style-blue {
        height: auto !important;
        transform: unset;
        position: relative;
    }

    .home-banner-section .swiper-pagination-bullets.swiper-pagination-horizontal {
        bottom: -40px;
    }

    @media only screen and (max-width: 1200px) {
        /* .right-box-two.style-blue {
            margin-top: -50px;
        } */

        .home-banner-section .swiper-pagination-bullets.swiper-pagination-horizontal {
            padding: 20px 35px;
        }
    }

    @media (max-width: 991px) {
        .home-banner-section .swiper-pagination-bullets.swiper-pagination-horizontal {
            bottom: 0px;
        }

        .right-box-two.style-blue {
            margin-left: auto;
            /* margin-top: -100px; */
        }

        .people-section {
            display: block;
        }
    }

    .bh-slider-cursor {
        display: inline-block;
        width: 10px;
        height: 10px;
        margin-left: -50px;
        margin-top: -105px;
        border-radius: 50%;
        background-color: red;
        left: 0;
        top: 0;
        opacity: 0;
        transition: opacity 0.5s ease-out, width 0.5s ease-out, height 0.5s ease-out;
        text-align: center;
        font-size: 6px;

    }

    .bh-slider-block-wrap {
        width: 100%;
        position: relative;
        overflow: hidden;
    }

    .bh-slider-block-wrap:hover .bh-slider-cursor {
        width: 100px;
        height: 100px;
        line-height: 100px;
        font-size: 30px;
        opacity: 1;
        z-index: 22222;
    }

    .bh-cursor {
        position: fixed;
    }

    .box-color-yellow {
        background-color: var(--primary-yellow) !important;
    }

    .box-color-red {
        background-color: var(--primary-red) !important;
    }

    .box-color-green {
        background-color: var(--primary-green) !important;
    }

    .box-color-blue {
        background-color: var(--primary-blue) !important;
    }

    .banner-slider-content h1 {
        margin: 0;
    }
</style>
<?php
$banner_section = get_field('banner_section');
$trans['readmore'] = 'READ MORE';
if ($pll == 'es') {
    $trans['readmore'] = 'LEER MÁS';
} elseif ($pll == 'de') {
    $trans['readmore'] = 'MEHR LESEN';
}
?>
<!-- ========== banner-section start============= -->
<style>
    .new-signup-area {
        clip-path: polygon(0 21%, 100% 0, 100% 100%, 29% 100%);
        background-color: red;
        width: 170px;
        padding-top: 34px;
        padding-left: 32px;
        position: absolute;
        right: 0;
        bottom: 0;
        z-index: 99;
    }

    .new-signup-area.bottom {
        bottom: -150px;
    }

    @media (max-width: 576px) {
        .new-signup-area.bottom {
            position: relative;
            bottom: unset;
            right: unset;
            margin-left: auto;
        }
    }

    .new-signup-area p {
        color: #fff;
        font-size: 14px;
        padding: 20px 13px 0;
    }

    .new-signup-area a {
        color: #fff;
        font-size: 14px;
        padding: 12px;
        text-align: center;
        border-top: 1px solid #fff;
        display: block;
        transition: 0.45s;
    }

    .new-signup-area a:hover {
        color: #000;
        background-color: #fff;
    }
</style>
<div class="insight-page-banner">
    <div class="home-banner-section position-relative">
        <div class="container-fluid px-0">
            <div class="swiper main-home-banner-slider">
                <div class="swiper-wrapper">
                    <style>
                        .banner-btn {
                            padding: 13px 34px;
                            text-align: center;
                            display: inline-flex;
                            justify-content: center;
                            font-size: 16px;
                        }
                    </style>
                    <?php
                    if (!empty($banner_section)) {
                        foreach ($banner_section as $value) {
                    ?>
                            <div class="swiper-slide" style="background-image: url('<?php echo $value['banner_image']; ?>');background-size: cover;">
                                <div class="banner-slider-content style-dark">
                                    <div class="container-one">
                                        <div class="col-lg-12 text-center">
                                            <h1><?php echo $value['banner_title']; ?></h1>
                                            <?php if (!empty($value['button'])) : ?>
                                                <a hreflang="<?php echo esc_attr($pll); ?>" href="<?php echo $value['button']['url']; ?>" class="eg-btn btn--primary-green btn--lg2 mt-3 banner-btn"><?php echo $value['button']['title']; ?></a>
                                            <?php endif; ?>
                                        </div>
                                    </div>
                                </div>
                                <!-- <div class="banner-pagination d-flex justify-content-end align-items-start box-color-<?php echo isset($value['box_color']) ? $value['box_color'] : 'yellow'; ?>"></div> -->
                            </div>
                    <?php
                        }
                    }
                    ?>
                </div>
            </div>
        </div>
        <!-- <div class = "banner-pagination d-flex justify-content-end align-items-start"></div> -->
        <div class="new-signup-area">
            <p>Sign up to our insights so you can keep on top of the latest IP developments and updates.</p>
            <a target="_blank" href="/updates-form/">SIGN UP <i class="bi bi-chevron-right"></i></a>
        </div>
    </div>
    <style>
        .insight-page-banner {
            position: relative;
        }

        .mouse_scroll {
            display: block;
            width: 24px;
            height: 50px;
            position: absolute;
            bottom: 30px;
            left: 50%;
            transform: translateX(-50%);
            cursor: pointer;
            z-index: 999;

        }

        .m_scroll_arrows {
            display: block;
            width: 5px;
            height: 5px;
            -ms-transform: rotate(45deg);
            -webkit-transform: rotate(45deg);
            transform: rotate(45deg);
            border-right: 2px solid white;
            border-bottom: 2px solid white;
            margin: 0 0 3px 4px;
            width: 16px;
            height: 16px
        }

        .unu {
            margin-top: 1px
        }

        .unu,
        .doi,
        .trei {
            -webkit-animation: mouse-scroll 1s infinite;
            -moz-animation: mouse-scroll 1s infinite;
            animation: mouse-scroll 1s infinite
        }

        .unu {
            -webkit-animation-delay: .1s;
            -moz-animation-delay: .1s;
            -webkit-animation-direction: alternate;
            animation-direction: alternate;
            animation-delay: alternate
        }

        .doi {
            -webkit-animation-delay: .2s;
            -moz-animation-delay: .2s;
            -webkit-animation-direction: alternate;
            animation-delay: .2s;
            animation-direction: alternate;
            margin-top: -6px
        }

        .trei {
            -webkit-animation-delay: .3s;
            -moz-animation-delay: .3s;
            -webkit-animation-direction: alternate;
            animation-delay: .3s;
            animation-direction: alternate;
            margin-top: -6px
        }

        .mouse {
            height: 42px;
            width: 24px;
            border-radius: 14px;
            transform: none;
            border: 2px solid white;
            top: 170px
        }

        .wheel {
            height: 5px;
            width: 2px;
            display: block;
            margin: 5px auto;
            background: white;
            position: relative;
            height: 4px;
            width: 4px;
            border: 2px solid #fff;
            -webkit-border-radius: 8px;
            border-radius: 8px
        }

        .wheel {
            -webkit-animation: mouse-wheel 0.6s linear infinite;
            -moz-animation: mouse-wheel 0.6s linear infinite;
            animation: mouse-wheel 0.6s linear infinite
        }

        @keyframes mouse-wheel {
            0% {
                top: 1px
            }

            25% {
                top: 2px
            }

            50% {
                top: 3px
            }

            75% {
                top: 2px
            }

            100% {
                top: 1px
            }
        }

        @keyframes mouse-scroll {
            0% {
                opacity: 0
            }

            50% {
                opacity: .5
            }

            100% {
                opacity: 1
            }
        }
    </style>
    <div class="mouse_scroll">
        <div> <span class="m_scroll_arrows unu"></span> <span class="m_scroll_arrows doi"></span></div>
    </div>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            function scrollToElement(element) {
                const offset = element.getBoundingClientRect().top + window.pageYOffset - 100;
                window.scrollTo({
                    top: offset,
                    behavior: 'smooth'
                });
            }

            const trigger = document.querySelector('.mouse_scroll');
            const target = document.querySelector('#search-filter');

            if (trigger && target) {
                trigger.addEventListener('click', function(event) {
                    event.preventDefault();

                    // 🔽 Scroll to the element
                    scrollToElement(target);
                });
            }
        });
    </script>
</div>

<!-- ========== banner-section end============= -->

<!-- ========== people details section start ============= -->
<style>
    .right-box-two.style-blue {
        height: auto;
        transform: unset;
        position: relative;
    }
</style>

<!-- <div class="people-section  <//?php echo get_field('insights_title') || get_field('insights_description') ? 'pt-60 pb-60' : ''; ?>"> -->
<div class="people-section mt-60 d-none" style="margin-top: -70px;">
    <div class="container-one">
        <div class="row justify-content-center">
            <!-- <div class="col-lg-6">
                <div class="section-title-one style-blue">
                    <h2><//?php echo get_field('insights_title'); ?></h2>
                    <div>
                        <p><//?php echo get_field('insights_description'); ?>
                        </p>
                    </div>
                </div>
            </div> -->
            <div class="col-lg-6">
                <div class="right-box-two style-blue" style="background-color: rgba(58,170,53,1);">
                    <div class="sign-up-text">
                        <?php
                        if ($pll == 'es') {
                        ?>
                            <p> <a target="_blank" href="/updates-form/">Suscríbete a nuestras actualizaciones (en inglés)</a>Suscríbete a nuestras novedades aquí para estar al tanto de los últimos avances y actualizaciones en materia de propiedad intelectual.</p>

                        <?php
                        } else {
                        ?>
                            <p>Subscribe to our insights <a target="_blank" href="/updates-form/">here</a> so you can keep on top of the latest IP developments and updates.</p>
                        <?php
                        }
                        ?>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- ========== people details section end ============= -->



<?php

$ordered_ids = array(3785, 3786, 3783, 3784);

$services = get_terms(array(
    'taxonomy'   => 'service',
    'include'    => $ordered_ids,
    'hide_empty' => false,
));

// Sort them in the exact order of $ordered_ids
usort($services, function ($a, $b) use ($ordered_ids) {
    $pos_a = array_search($a->term_id, $ordered_ids);
    $pos_b = array_search($b->term_id, $ordered_ids);
    return $pos_a - $pos_b;
});
$insight_types = get_terms(array(
    'taxonomy'   => 'insight_type', // Replace with your taxonomy name
    'hide_empty' => false, // Show empty terms
    'orderby'    => 'id',     // Order by ID
));
$sectors = get_terms(array(
    'taxonomy'   => 'sectors', // Replace with your taxonomy name
    'hide_empty' => false, // Show empty terms
));

?>

<!-- ========== insights details section end ============= -->
<div class="filter-search-area mt-60" id="search-filter">
    <div class="container-one">
        <form class="filter-search-form" id="insights-filter-search-form" method="post" action="">
            <input type="hidden" name="action" value="get_main_search_insights_data">
            <input type="hidden" name="author_id">
            <!-- <div class="row row-cols-lg-5 row-cols-md-4 row-cols-sm-2 row-cols-2 justify-content-start g-3"> -->
            <div class="row row-cols-lg-4 row-cols-md-4 row-cols-sm-2 row-cols-2 justify-content-start g-3">
                <div class="col d-none">
                    <div class="form-inner">
                        <input type="submit" value="">
                        <i class="bi bi-search"></i>
                    </div>
                </div>
                <div class="col position-relative">
                    <div class="form-inner">
                        <!-- <input type="text" name="name" placeholder="<//?php echo __('Author', 'picostrap5-child-base'); ?>"> -->
                        <select name="name">
                            <option value=""><?php echo __('Author', 'picostrap5-child-base'); ?></option>
                            <?php
                            $people_posts = get_insights_people_list();

                            foreach ($people_posts as $people) :
                                $first_name = get_field('first_name', $people->ID);
                                $last_name = get_field('last_name', $people->ID);
                            ?>
                                <option value="<?php echo esc_attr($people->ID); ?>">
                                    <?php echo esc_html($first_name . ' ' . $last_name); ?>
                                </option>
                            <?php endforeach; ?>
                        </select>

                    </div>
                    <!-- <div class="author-dropdown d-none"></div> -->
                </div>
                <div class="col">
                    <div class="form-inner">
                        <select name="insight_typee">
                            <option value=""><?php echo __('Topics', 'picostrap5-child-base'); ?></option>
                            <?php foreach ($insight_types as $insight_types) : ?>
                                <option value="<?php echo $insight_types->term_id ?? null ?>"><?php echo $insight_types->name ?? '' ?></option>
                            <?php endforeach ?>
                            <option value="news-post">Firm news</option>
                        </select>
                    </div>
                </div>
                <div class="col">
                    <div class="form-inner">
                        <select name="service">
                            <option value=""><?php echo __('Service', 'picostrap5-child-base'); ?></option>
                            <?php foreach ($services as $service) : ?>
                                <option value="<?php echo $service->term_id ?? null ?>"><?php echo $service->name ?? '' ?></option>
                            <?php endforeach ?>
                        </select>
                    </div>
                </div>
                <div class="col">
                    <div class="form-inner">
                        <select name="sector">
                            <option value=""><?php echo __('Sector', 'picostrap5-child-base'); ?></option>
                            <!-- <//?php
                            // Retrieve the sector_sub_sector field
                            // $sectors = get_field('sector_sub_sector');
                            // Extract IDs from the sectors
                            // $sector_ids = wp_list_pluck($sectors, 'ID');
                            // print_r($sectors);
                            // Query the sector posts
                            $args = array(
                                'post_type'      => 'sector',
                                'posts_per_page' => -1,
                                'post_status'    => 'publish',
                                'post_parent'    => 0,
                                // 'post__in'       => $sector_ids,
                                'orderby'        => 'title',
                                'order'          => 'ASC',
                            );

                            $sector_posts = get_posts($args);

                            // Loop through the sector posts and output the options
                            foreach ($sector_posts as $sector) : ?>
                                <option value="<//?php echo esc_attr($sector->ID); ?>">
                                    <//?php echo esc_html($sector->post_title); ?>
                                </option>
                            <//?php endforeach;
                            ?> -->
                            <?php foreach ($sectors as $sectors) : ?>
                                <option value="<?php echo $sectors->term_id ?? null ?>"><?php echo $sectors->name ?? '' ?></option>
                            <?php endforeach ?>
                        </select>

                    </div>
                </div>
            </div>
        </form>
    </div>
</div>
<div id="insights-data-wrapper" class="position-relative">
    <!-- ========== insights section start ============= -->
    <div class="insights-section mt-60 position-relative">
        <div class="box-design-seven">
        </div>
        <div class="container-one">
            <div class="section-title-one style-black">
                <h2><?php echo __('Insights', 'picostrap5-child-base'); ?></h2>
            </div>
            <div class="row justify-content-center mb-40 g-lg-4 g-3">
                <?php
                $args = array(
                    'post_type' => array('bulletin', 'upc_news', 'news'),
                    'posts_per_page' => 20,
                    'post_status' => 'publish',
                    'meta_query' => array(
                        'relation' => 'OR',
                        array(
                            'key' => 'trending',
                            'compare' => 'NOT EXISTS',
                        ),
                        array(
                            'key' => 'trending',
                            'value' => '0',
                            'compare' => '==',
                        ),
                    ),
                );

                $bulletins = get_posts($args);

                if (!empty($bulletins)) {
                    foreach ($bulletins as $b) {
                        $permalink = get_permalink($b);
                        if ($pll == 'de') {
                            $permalink = str_replace("/de/", "/", $permalink);
                        }

                        $post_type = get_post_type($b);
                        $post_type_obj = get_post_type_object($post_type);
                        $singular_name = $post_type_obj ? $post_type_obj->labels->singular_name : '';

                        if ($post_type === 'bulletin') {
                            $singular_name = 'IP updates';
                        }
                        $excerpt = get_the_excerpt($b);

                        $excerpt = substr($excerpt, 0, 200);
                        // $excerpt = substr($excerpt, 0, strrpos($excerpt, ' ')) . '...';
                ?>
                        <div class="col-lg-3 col-md-6 col-12">
                            <div class="insight-item insight-item-new style-black">
                                <div class="image">
                                    <img src="<?php echo has_post_thumbnail($b) ? get_the_post_thumbnail_url($b, 'medium') : home_url('/wp-content/uploads/2023/06/iStock-97970805-scaled.jpg'); ?>" alt="image">
                                    <h6><a class="insight-title" href="<?php echo $permalink; ?>"><?php echo __($singular_name, 'picostrap5-child-base'); ?></a></h6>
                                </div>
                                <div class="content">
                                    <h6><a class="insight-title" href="<?php echo $permalink; ?>"><?php echo esc_html($b->post_title); ?></a></h6>
                                    <p><?php echo $excerpt; ?></p>
                                </div>
                            </div>
                        </div>
                <?php
                    }
                }
                ?>


            </div>

            <a hreflang="<?php echo esc_attr($pll); ?>" href="<?php echo home_url('/all-insights'); ?>" class="eg-btn btn--primary-green btn--lg2"><?php echo __('MORE', 'picostrap5-child-base'); ?><i
                    class="bi bi-arrow-right"></i></a>
            <style>
                .eg-btn.btn--primary-green {
                    background-color: #000;
                }

                .eg-btn.btn--primary-green::before {
                    background-color: var(--primary-green-dark);
                }
            </style>
        </div>
        <div class="container-one pt-100 d-none">
            <div class="section-title-one style-black">
                <h2><?php echo __('News', 'picostrap5-child-base'); ?> </h2>
            </div>
            <div class="row justify-content-center mb-40 g-lg-4 g-3">
                <?php
                $args = array(
                    'post_type' => 'news',
                    'posts_per_page' => '4',
                    'post_status' => 'publish',
                );

                $news = get_posts($args);
                if ($news) {
                    foreach ($news as $key => $value) {
                        $permalink = '';
                        if ($pll == 'de') {
                            $permalink = str_replace("/de/news/", "/news/", get_permalink($value));
                        } else {
                            $permalink = get_permalink($value);
                        }

                ?>
                        <div class="col-lg-3 col-md-6 col-12">
                            <div class="insight-item">
                                <div class="image">
                                    <!-- <img src="<?php echo get_stylesheet_directory_uri(); ?>/assets/images/insights/insight-1.jpg" alt="image"> -->
                                    <img src="<?php echo has_post_thumbnail($value,  'medium') ? get_the_post_thumbnail_url($value,  'medium') : home_url('/wp-content/uploads/2023/06/iStock-97970805-scaled.jpg'); ?>" alt="image">
                                </div>
                                <div class="content">
                                    <h6><a href="<?php echo $permalink; ?>"><?php echo get_the_title($value); ?></a></h6>
                                    <p><?php echo get_the_excerpt($value); ?></p>
                                    <!-- <a href="<//?php echo $permalink; ?>"><//?php echo __('MORE', 'picostrap5-child-base'); ?></a> -->
                                </div>
                            </div>
                        </div>
                <?php
                    }
                }

                ?>
            </div>
            <?php
            $pll = pll_current_language();
            $insightUrl = '';
            if ($pll == 'es') {
                $insightUrl = home_url('/es/noticias/');
            } else {
                $insightUrl = home_url('/all-news/');
            }
            ?>
            <a href="<?php echo home_url('/all-news/'); ?>" class="eg-btn btn--primary-red btn--lg2"><?php echo __('News', 'picostrap5-child-base'); ?><i class="bi bi-arrow-right"></i></a>


        </div>
    </div>

    <!-- ========== insights section end ============= -->
    <div class="new-signup-area bottom">
        <p>Sign up to our insights so you can keep on top of the latest IP developments and updates.</p>
        <a target="_blank" href="/updates-form/">SIGN UP <i class="bi bi-chevron-right"></i></a>
    </div>
</div>
<div class="insights-section pt-100 position-relative d-none">
    <div class="box-design-six">
    </div>
    <div class="container-one">
        <div class="section-title-one style-red">
            <h2><?php echo __('News', 'picostrap5-child-base'); ?> </h2>
        </div>
        <div class="row justify-content-center mb-40 g-lg-4 g-3">
            <?php
            $args = array(
                'post_type' => 'news',
                'posts_per_page' => '4',
                'post_status' => 'publish',
            );

            $news = get_posts($args);
            if ($news) {
                foreach ($news as $key => $value) {
                    $permalink = '';
                    if ($pll == 'de') {
                        $permalink = str_replace("/de/news/", "/news/", get_permalink($value));
                    } else {
                        $permalink = get_permalink($value);
                    }

            ?>
                    <div class="col-lg-3 col-md-6 col-12">
                        <div class="insight-item">
                            <div class="image">
                                <!-- <img src="<?php echo get_stylesheet_directory_uri(); ?>/assets/images/insights/insight-1.jpg" alt="image"> -->
                                <img src="<?php echo has_post_thumbnail($value,  'medium') ? get_the_post_thumbnail_url($value,  'medium') : home_url('/wp-content/uploads/2023/06/iStock-97970805-scaled.jpg'); ?>" alt="image">
                            </div>
                            <div class="content">
                                <h6><a class="insight-title" href="<?php echo $permalink; ?>"><?php echo get_the_title($value); ?></a></h6>
                                <p><?php echo get_the_excerpt($value); ?></p>
                                <!-- <a href="<//?php echo $permalink; ?>"><//?php echo __('MORE', 'picostrap5-child-base'); ?></a> -->
                            </div>
                        </div>
                    </div>
            <?php
                }
            }

            ?>
        </div>
        <?php
        $pll = pll_current_language();
        $insightUrl = '';
        if ($pll == 'es') {
            $insightUrl = home_url('/es/noticias/');
        } else {
            $insightUrl = home_url('/all-news/');
        }
        ?>
        <a href="<?php echo home_url('/all-news/'); ?>" class="eg-btn btn--primary-red btn--lg2"><?php echo __('News', 'picostrap5-child-base'); ?><i class="bi bi-arrow-right"></i></a>
    </div>
</div>
<!-- ========== toolkit section start ============= -->

<div class="event-section position-relative pt-100 pb-100">
    <div class="box-design-ten"></div>
    <div class="container-one">
        <div class="section-title-one style-yellow">
            <h2><?php echo __('Technical toolkits', 'picostrap5-child-base'); ?></h2>
        </div>
        <div class="row justify-content-lg-start justify-content-center g-sm-4 g-2">
            <?php
            $args = array(
                'post_type' => 'toolkit',
                'posts_per_page' => '4',
                'post_status' => 'publish',
            );

            $toolkits = get_posts($args);
            if (!empty($toolkits)) {
                foreach ($toolkits as $b) {
                    $b_id = $b->ID;
            ?>
                    <div class="col-lg-3 col-md-6 col-6">
                        <a href="<?php echo get_field('external_url', $b_id) ? get_field('external_url', $b_id) : get_permalink($b); ?>">
                            <div class="sector-item-two style-yellow">
                                <h6 class="title"><?php echo $b->post_title; ?></h6>
                                <p><?php echo get_the_excerpt($b); ?></p>
                                <!-- <span class="date"><?php echo get_the_date('d F Y', $b); ?></span> -->
                                <div class="arrow-btn">
                                    <img src="<?php echo get_stylesheet_directory_uri(); ?>/assets/images/icons/arrow-white.svg" alt="">
                                </div>
                            </div>
                        </a>
                    </div>
            <?php
                }
            }
            ?>
        </div>
    </div>
</div>

<!-- ========== toolkit section start ============= -->

<!-- ========== search section start ============= -->

<div class="search-section style-deep-blue mt-100">
    <div class="container-one">
        <div class="search-block">
            <input type="text" placeholder="<?php echo __('SIGN UP TO OUR UPDATES', 'picostrap5-child-base'); ?>">
        </div>
    </div>
</div>

<!-- ========== search section start ============= -->

<style>
    @media only screen and (max-width: 576px) {
        .banner-section {
            min-height: 540px !important;
        }
    }
</style>

<?php

get_footer();
