<?php
/*
Template Name: Thoughts/Newsletters List
*/

// Exit if accessed directly.
defined( 'ABSPATH' ) || exit;
$pll = pll_current_language();
get_header();

?>
    <!-- ========== banner-section start============= -->

    <div class="banner-section d-flex flex-column align-items-staer justify-content-center position-relative"
        style="background-image: url('<?php echo get_the_post_thumbnail_url(get_the_ID(), 'full'); ?>')">
        <div class="inner-overlay"></div>
        <!-- style="background-image:linear-gradient(to bottom, rgba(255,255,255,0.05) 0%, rgba(0,0,0,0.15) 50%), radial-gradient(at top center, rgba(255,255,255,0.20) 50%, rgba(0,0,0,0.40) 190%), url('<?php echo get_the_post_thumbnail_url(get_the_ID(), 'full'); ?>')"> -->
        <!-- style="background-image:linear-gradient(to bottom, rgba(255,255,255,0.05) 0%, rgba(0,0,0,0.15) 100%), radial-gradient(at top center, rgba(255,255,255,0.40) 0%, rgba(0,0,0,0.40) 120%), url('<?php echo get_the_post_thumbnail_url(get_the_ID(), 'full'); ?>')"> -->
        <div class="container-one position-relative">
         
        </div>
    </div>
    <div class="banner-content banner-single-post-title">
        <h1 class="title-position-design text-white" ><?php echo get_field('banner_title') ? get_field('banner_title') : get_the_title(); ?><span></span></h1>
    </div>
    <!-- ========== banner-section end============= -->

    <!-- ========== people details section start ============= -->

    <div class="people-section pt-60 pb-60 d-none">
        <div class="container-one">
            <div class="row">
                <div class="col-lg-6">
                    <div class="section-title-one style-yellow">
                        <h2 ><?php echo get_field('people_title'); ?></h2>
                        <div >
                            <p><?php echo get_field('people_description'); ?></p>
                        </div>
                    </div>
                </div>
                <div class="col-lg-6">
                    <div class="right-box">
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- ========== people details section end ============= -->

    <div class="filter-search-area pt-100 pb-100 d-none">        
        <div class="container-one" >
            <form class="filter-search-form" id="people-filter-search-form" method="post" action="">
                <input type="hidden" name="action" value="get_main_search_people_data">
                <!-- <div class="row row-cols-lg-5 row-cols-md-4 row-cols-sm-2 row-cols-2 justify-content-start g-3"> -->
                <div class="row row-cols-lg-4 row-cols-md-4 row-cols-sm-2 row-cols-2 justify-content-start g-3">
                    <div class="col d-none">
                        <div class="form-inner">
                            <input type="submit" value="">
                            <i class="bi bi-search"></i>
                        </div>
                    </div>
                    <div class="col">
                        <div class="form-inner">
                            <input type="text" name="name" placeholder="<?php echo __('Name', 'picostrap5-child-base'); ?>">
                        </div>
                    </div>
                    <div class="col">
                        <div class="form-inner">
                            <select name="role">
                                <option value=""><?php echo __('Role', 'picostrap5-child-base'); ?></option>
                                <?php 
                                    $roles = get_terms( array(
                                        'taxonomy'   => 'people_role',
                                        'hide_empty' => false,
                                    ) );

                                    if(!empty($roles)){
                                        foreach ($roles as $value) {
                                            ?>
                                                <option value="<?php echo $value->term_id; ?>"><?php echo $value->name; ?></option>
                                            <?php 
                                        }
                                    }
                                ?>
                            </select>
                        </div>
                    </div>
                    <div class="col">
                        <div class="form-inner">
                            <select name="location">
                                <option value=""><?php echo __('Location', 'picostrap5-child-base'); ?></option>
                                <?php 
                                    $locations = get_terms( array(
                                        'taxonomy'   => 'location',
                                        'hide_empty' => false,
                                    ) );

                                    if(!empty($locations)){
                                        foreach ($locations as $value) {
                                            ?>
                                                <option value="<?php echo $value->term_id; ?>"><?php echo $value->name; ?></option>
                                            <?php 
                                        }
                                    }
                                ?>
                            </select>
                        </div>
                    </div>
                    <div class="col">
                        <div class="form-inner">
                            <select name="sector">
                                <option value=""><?php echo __('Sector', 'picostrap5-child-base'); ?></option>
                                <?php 
                                    $sectors = get_posts(['post_type' => 'sector', 'posts_per_page' => '-1', 'order_by' => 'title', 'order' => 'ASC']);
                                    if(!empty($sectors)){
                                        foreach ($sectors as $key => $value) {
                                            ?>
                                                <option value="<?php echo $value->ID; ?>"><?php echo $value->post_title; ?></option>
                                            <?php 
                                        }
                                    }
                                ?>
                            </select>
                        </div>
                    </div>
                </div>
            </form>
        </div>
    </div>


    <!-- ========== team section start ============= -->

    <div class="team-section pt-100">
        <div class="container-one">
            <div class="section-title-two">
                <!-- <h4 ><?php // echo __('Thoughts', 'picostrap5-child-base'); ?></h4> -->
                &nbsp;
            </div>
            <div class="row row-cols-xl-4 row-cols-lg-4 row-cols-md-3 row-cols-sm-2 row-cols-2 g-4 mb-20" id="people-filter-search-list">
                <?php 
                    $args = [
                        'post_type' => 'newsletter',
                        'posts_per_page' => '-1',
                        'post_status' => 'publish',
                        'orderby' => 'title',
                        // 'orderby' => 'meta_value',
                        // 'meta_key' => 'last_name',
                        'order' => 'ASC',
                    ];

                    $q = new WP_Query($args);
                    if($q->have_posts()){
                        while($q->have_posts()){
                            $q->the_post();

                            ?>
                                <div class="col" >
                                    <div class="insight-item style-green">
                                        <?php if(!empty(get_the_post_thumbnail_url(get_the_ID()))) :?>
                                        <div class="image">
                                           <a hreflang="<?php echo esc_attr($pll); ?>" href="<?php the_permalink(); ?>"><img src="<?php echo get_the_post_thumbnail_url(get_the_ID(), 'full'); ?>" alt="image"></a> 
                                        </div>
                                        <?php endif; ?>
                                        <div class="content">
                                            <h6><a hreflang="<?php echo esc_attr($pll); ?>" href="<?php the_permalink(); ?>"><?php echo get_the_title(); ?></a></h6>
                                            <p><?php echo get_the_excerpt(); ?></p>
                                            <a hreflang="<?php echo esc_attr($pll); ?>" href="<?php the_permalink(); ?>"><?php echo __('MORE', 'picostrap5-child-base'); ?></a>
                                        </div>
                                    </div>
                                </div>
                            <?php 
                        }
                        wp_reset_query();
                    }
                ?>
            </div>
        </div>
    </div>

    <!-- ========== team section end ============= -->

    <!-- ========== expertise-section start======== -->

    <div class="expertise-section-1">
        <div class="container-one">
            <div class="row justiy-content-start">
                <div class="col-lg-7">
                    <div class="section-title-one style-blue">
                        <h2 ><?php echo get_field('expertise_title'); ?></h2>
                        <p class="mb-45" ><?php echo get_field('expertise_description'); ?></p>
                        <?php 
                            $expertise_button = get_field('expertise_button');
                            if($expertise_button && isset($expertise_button['url'])){
                                ?>
                                    <a hreflang="<?php echo esc_attr($pll); ?>"  href="<?php echo $expertise_button['url']; ?>" class="eg-btn btn--primary-blue btn--lg2"><?php echo $expertise_button['title']; ?> <i
                                            class="bi bi-arrow-right"></i></a>
                                <?php 
                            }
                        ?>
                    </div>
                </div>
                <div class="col-lg-5">
                    <div class="right-box-blue">

                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- ========== expertise-section end======== -->

    <div class="footer-top-design-one d-none">
        <div class="box"></div>
    </div>


    <script type="text/javascript">
        jQuery(function($){
            var ajax_var = null;

            $(document).on('keyup', '#people-filter-search-form [name="name"]', function(e){
                $(this).closest('form').trigger('submit');
            });
            $(document).on('change', '#people-filter-search-form select[name="role"]', function(e){
                $(this).closest('form').trigger('submit');
            });
            $(document).on('change', '#people-filter-search-form select[name="location"]', function(e){
                $(this).closest('form').trigger('submit');
            });
            $(document).on('change', '#people-filter-search-form select[name="sector"]', function(e){
                $(this).closest('form').trigger('submit');
            });

            $(document).on('submit', '#people-filter-search-form', function(e){
                e.preventDefault();

                var this_form = $(this);
                var this_data = this_form.serialize();

                ajax_var = $.ajax({
                    beforeSend : function()    {          
                        if(ajax_var != null) {
                            ajax_var.abort();
                            ajax_var = null;
                        }
                    },
                    url: '<?php echo admin_url("admin-ajax.php"); ?>',
                    method: 'POST',
                    dataType: 'JSON',
                    data: this_data,
                    success: function(r){
                        // $(document).find('#people-filter-search-list').html(r.html);
                        var data = r.data;
                        let output = '';
                        data.forEach(function (item) {
                            output += `
                                <div class="col">
                                    <div class="team-item style-two style-two">
                                        <div class="team-image">
                                            <a href="${item.permalink}">
                                                <img src="${item.image}" alt="${item.title}">
                                            </a>
                                        </div>
                                        <div class="team-content">
                                            <h6><a href="${item.permalink}">${item.title}</a></h6>
                                            <span>${item.roles} </span>
                                            <span>${item.locations}</span>
                                        </div>
                                    </div>
                                </div>
                            `
                        });
                        document.getElementById('people-filter-search-list').innerHTML = output;
                    }
                });
            });
        });
    </script>

<?php 

get_footer();

